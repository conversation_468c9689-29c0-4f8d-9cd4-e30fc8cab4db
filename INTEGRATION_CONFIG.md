# 前后端集成配置调整指南

## 🔍 问题分析

经过全面梳理，发现以下关键配置不一致问题：

### 1. API响应格式不匹配
- **前端期望**: `{ code: number, message: string, data: any }`
- **后端返回**: `{ success: boolean, data: any, error?: object }`

### 2. API路径不匹配
- **前端调用**: `/auth/refresh`
- **后端路由**: `/api/auth/refresh`

### 3. 环境配置不完整
- 缺少生产环境配置
- 微信小程序配置为测试值
- CORS配置过于宽松

## 🛠️ 配置调整方案

### 第一步：统一API响应格式

#### 1.1 修改后端响应格式

需要修改后端所有API返回统一格式：
```typescript
// 成功响应
{
  code: 200,
  message: "success",
  data: { ... }
}

// 错误响应
{
  code: 400|401|403|404|500,
  message: "错误信息",
  data: null
}
```

#### 1.2 创建统一响应工具

```typescript
// server/src/utils/response.ts
export class ApiResponse {
  static success<T>(data: T, message: string = 'success') {
    return {
      code: 200,
      message,
      data
    };
  }

  static error(code: number, message: string) {
    return {
      code,
      message,
      data: null
    };
  }
}
```

### 第二步：修正API路径

#### 2.1 前端API配置调整
```typescript
// miniprogram/utils/api.ts
const API_CONFIG = {
  baseUrl: process.env.NODE_ENV === 'production' 
    ? 'https://your-domain.com/api' 
    : 'http://127.0.0.1:3000/api',
  timeout: 10000,
  retryCount: 3,
};
```

#### 2.2 前端API调用路径调整
```typescript
// 刷新Token API调用
url: '/api/auth/refresh' // 改为完整路径
```

### 第三步：环境配置优化

#### 3.1 开发环境配置
```env
# server/.env.development
NODE_ENV=development
PORT=3000
DATABASE_PATH=./data/accounting.db
JWT_SECRET=dev-jwt-secret-key
JWT_REFRESH_SECRET=dev-refresh-secret-key
WECHAT_APP_ID=test-app-id
WECHAT_APP_SECRET=test-app-secret
CORS_ORIGIN=*
LOG_LEVEL=debug
```

#### 3.2 生产环境配置
```env
# server/.env.production
NODE_ENV=production
PORT=3000
DATABASE_PATH=/var/data/accounting.db
JWT_SECRET=your-very-strong-production-jwt-secret
JWT_REFRESH_SECRET=your-very-strong-production-refresh-secret
WECHAT_APP_ID=your-real-wechat-app-id
WECHAT_APP_SECRET=your-real-wechat-app-secret
CORS_ORIGIN=https://servicewechat.com
LOG_LEVEL=info
```

### 第四步：微信小程序配置

#### 4.1 小程序项目配置
```json
// miniprogram/project.config.json
{
  "appid": "your-real-wechat-app-id",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true
  },
  "compileType": "miniprogram"
}
```

#### 4.2 小程序网络域名配置
在微信公众平台配置：
- **request合法域名**: `https://your-domain.com`
- **uploadFile合法域名**: `https://your-domain.com`
- **downloadFile合法域名**: `https://your-domain.com`

### 第五步：HTTPS和域名配置

#### 5.1 Nginx配置
```nginx
# /etc/nginx/sites-available/accounting-app
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;

    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /uploads {
        alias /path/to/your/server/uploads;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

### 第六步：数据库配置

#### 6.1 生产环境MySQL配置
```env
# 如果使用MySQL
DATABASE_TYPE=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=accounting_app
DATABASE_USER=accounting_user
DATABASE_PASSWORD=your-secure-password
```

#### 6.2 数据库连接池配置
```typescript
// server/src/database/mysql.ts
const pool = mysql.createPool({
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.name,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
});
```

## 📋 具体修改清单

### 前端修改 (miniprogram/)

1. **utils/api.ts**
   - [ ] 修改baseUrl配置支持环境变量
   - [ ] 调整API路径为完整路径
   - [ ] 修改响应格式处理逻辑

2. **utils/config.ts** (新建)
   - [ ] 创建环境配置管理
   - [ ] 区分开发和生产环境

3. **project.config.json**
   - [ ] 配置真实的AppID
   - [ ] 设置合适的编译选项

### 后端修改 (server/)

1. **src/utils/response.ts** (新建)
   - [ ] 创建统一响应格式工具

2. **src/routes/*.ts**
   - [ ] 所有路由使用统一响应格式
   - [ ] 错误处理统一化

3. **src/middleware/error.middleware.ts**
   - [ ] 修改错误响应格式

4. **src/config/index.ts**
   - [ ] 添加环境配置验证
   - [ ] 完善生产环境配置

5. **.env.production** (新建)
   - [ ] 创建生产环境配置文件

### 部署配置

1. **nginx.conf**
   - [ ] 配置反向代理
   - [ ] 设置SSL证书
   - [ ] 配置静态文件服务

2. **ecosystem.config.js** (PM2配置)
   - [ ] 配置进程管理
   - [ ] 设置环境变量

3. **docker-compose.yml** (可选)
   - [ ] 容器化部署配置

## 🚀 部署流程

### 开发环境
```bash
# 1. 启动后端
cd server
npm run dev

# 2. 启动小程序开发工具
# 导入miniprogram目录
```

### 生产环境
```bash
# 1. 构建后端
cd server
npm run build

# 2. 配置环境变量
cp .env.production .env

# 3. 启动生产服务
pm2 start ecosystem.config.js --env production

# 4. 配置Nginx
sudo nginx -t
sudo systemctl reload nginx

# 5. 上传小程序代码
# 使用微信开发者工具上传
```

## ✅ 验证清单

### API接口验证
- [ ] 登录接口返回正确格式
- [ ] Token刷新机制正常
- [ ] 所有CRUD接口响应一致
- [ ] 错误处理统一

### 网络配置验证
- [ ] HTTPS证书正常
- [ ] CORS配置正确
- [ ] 域名解析正常
- [ ] 小程序网络权限配置

### 安全配置验证
- [ ] JWT密钥足够强
- [ ] 数据库连接安全
- [ ] 文件上传限制
- [ ] 请求频率限制

### 性能配置验证
- [ ] 数据库连接池
- [ ] 静态文件缓存
- [ ] API响应时间
- [ ] 并发处理能力

## 🔧 监控和日志

### 应用监控
```bash
# PM2监控
pm2 monit

# 日志查看
pm2 logs accounting-server

# 性能监控
pm2 install pm2-server-monit
```

### 错误追踪
```typescript
// 集成错误追踪服务
import * as Sentry from '@sentry/node';

Sentry.init({
  dsn: 'your-sentry-dsn',
  environment: process.env.NODE_ENV,
});
```

---

**下一步**: 按照此清单逐项完成配置调整，确保前后端完全集成并达到生产环境要求。
