# 生产环境部署检查清单

## 📋 前端配置检查

### 小程序基础配置
- [ ] **AppID配置**: 在 `miniprogram/utils/config.ts` 中配置真实的微信小程序AppID
- [ ] **API域名**: 确认生产环境API域名配置正确
- [ ] **网络域名**: 在微信公众平台配置request合法域名
- [ ] **版本管理**: 确认小程序版本号和描述

### 小程序网络权限配置
在微信公众平台 -> 开发 -> 开发设置 -> 服务器域名中配置：
- [ ] **request合法域名**: `https://your-domain.com`
- [ ] **uploadFile合法域名**: `https://your-domain.com`
- [ ] **downloadFile合法域名**: `https://your-domain.com`

### 代码检查
- [ ] **移除调试代码**: 删除所有console.log和调试代码
- [ ] **错误处理**: 确保所有API调用都有错误处理
- [ ] **用户体验**: 添加适当的loading和错误提示
- [ ] **性能优化**: 图片压缩、代码分包等

## 🖥️ 后端配置检查

### 环境配置
- [ ] **环境变量**: 复制 `.env.production` 到 `.env`
- [ ] **JWT密钥**: 使用强密码（至少32字符）
- [ ] **微信配置**: 配置真实的AppID和AppSecret
- [ ] **数据库路径**: 确认生产环境数据库路径
- [ ] **文件上传**: 确认上传目录权限和路径

### 安全配置
- [ ] **CORS设置**: 限制为微信小程序域名
- [ ] **请求限流**: 配置合适的限流参数
- [ ] **文件上传**: 限制文件类型和大小
- [ ] **日志级别**: 设置为info或warn
- [ ] **错误信息**: 不暴露敏感信息

### 数据库配置
- [ ] **SQLite路径**: 确认数据库文件路径和权限
- [ ] **MySQL配置**: 如果使用MySQL，配置连接参数
- [ ] **数据备份**: 设置定期备份策略
- [ ] **索引优化**: 确认数据库索引已创建

## 🌐 服务器配置检查

### 基础环境
- [ ] **Node.js版本**: >= 16.0.0
- [ ] **PM2安装**: 全局安装PM2进程管理器
- [ ] **Nginx安装**: 安装并配置Nginx
- [ ] **防火墙**: 开放必要端口（80, 443）

### SSL证书
- [ ] **证书申请**: 申请SSL证书（Let's Encrypt或商业证书）
- [ ] **证书配置**: 在Nginx中配置SSL
- [ ] **HTTPS重定向**: 配置HTTP到HTTPS重定向
- [ ] **证书自动续期**: 设置证书自动续期

### Nginx配置
- [ ] **反向代理**: 配置API请求代理到Node.js
- [ ] **静态文件**: 配置uploads目录静态文件服务
- [ ] **Gzip压缩**: 启用Gzip压缩
- [ ] **安全头**: 配置安全相关HTTP头
- [ ] **限流配置**: 配置请求频率限制

### 进程管理
- [ ] **PM2配置**: 使用ecosystem.config.js配置
- [ ] **集群模式**: 启用cluster模式提高性能
- [ ] **自动重启**: 配置进程异常自动重启
- [ ] **开机自启**: 设置PM2开机自启动
- [ ] **日志管理**: 配置日志轮转和清理

## 📊 监控和日志

### 应用监控
- [ ] **健康检查**: 配置/health端点监控
- [ ] **性能监控**: 监控CPU、内存使用率
- [ ] **错误监控**: 集成错误追踪服务（如Sentry）
- [ ] **API监控**: 监控API响应时间和成功率

### 日志管理
- [ ] **日志级别**: 生产环境设置为info
- [ ] **日志轮转**: 配置日志文件轮转
- [ ] **日志清理**: 定期清理旧日志文件
- [ ] **错误告警**: 配置错误日志告警

### 备份策略
- [ ] **数据库备份**: 每日自动备份数据库
- [ ] **文件备份**: 备份上传的文件
- [ ] **代码备份**: 保留多个版本的代码备份
- [ ] **配置备份**: 备份重要配置文件

## 🔒 安全检查

### 网络安全
- [ ] **防火墙**: 只开放必要端口
- [ ] **DDoS防护**: 配置DDoS防护
- [ ] **IP白名单**: 如需要，配置IP访问限制
- [ ] **VPN访问**: 管理后台使用VPN访问

### 应用安全
- [ ] **依赖更新**: 更新所有依赖到最新安全版本
- [ ] **漏洞扫描**: 运行安全漏洞扫描
- [ ] **权限控制**: 最小权限原则
- [ ] **敏感信息**: 确保不暴露敏感信息

## 🚀 部署流程

### 自动化部署
- [ ] **部署脚本**: 使用 `scripts/deploy.sh` 自动部署
- [ ] **CI/CD**: 配置持续集成和部署
- [ ] **回滚机制**: 准备快速回滚方案
- [ ] **蓝绿部署**: 如需要，配置蓝绿部署

### 部署验证
- [ ] **功能测试**: 验证所有核心功能
- [ ] **性能测试**: 验证系统性能
- [ ] **安全测试**: 验证安全配置
- [ ] **兼容性测试**: 验证小程序兼容性

## ✅ 上线前最终检查

### 配置确认
- [ ] **域名解析**: 确认域名正确解析到服务器
- [ ] **SSL证书**: 确认HTTPS正常工作
- [ ] **API测试**: 测试所有API接口
- [ ] **小程序测试**: 在真机上测试小程序

### 性能确认
- [ ] **响应时间**: API响应时间 < 500ms
- [ ] **并发处理**: 能处理预期并发量
- [ ] **内存使用**: 内存使用率 < 80%
- [ ] **磁盘空间**: 磁盘使用率 < 80%

### 监控确认
- [ ] **监控告警**: 确认监控告警正常
- [ ] **日志收集**: 确认日志正常收集
- [ ] **备份任务**: 确认备份任务正常运行
- [ ] **健康检查**: 确认健康检查正常

## 📞 应急预案

### 故障处理
- [ ] **联系方式**: 准备技术支持联系方式
- [ ] **回滚方案**: 准备快速回滚方案
- [ ] **备用服务器**: 准备备用服务器
- [ ] **数据恢复**: 准备数据恢复方案

### 文档准备
- [ ] **运维文档**: 准备详细运维文档
- [ ] **故障手册**: 准备常见故障处理手册
- [ ] **联系清单**: 准备相关人员联系清单
- [ ] **密码管理**: 安全保存所有密码和密钥

---

## 🎯 快速部署命令

### 服务器端部署
```bash
# 1. 克隆代码
git clone <repository-url>
cd jh-wx-app/server

# 2. 自动化部署
sudo ./scripts/deploy.sh

# 3. 手动部署（如果自动化失败）
npm install
npm run build
cp .env.production .env
pm2 start ecosystem.config.js --env production
```

### 小程序端发布
```bash
# 1. 更新配置
# 编辑 miniprogram/utils/config.ts

# 2. 使用微信开发者工具
# - 上传代码
# - 提交审核
# - 发布上线
```

---

**⚠️ 重要提醒**：
1. 生产环境部署前务必在测试环境完整验证
2. 保留回滚方案，确保可以快速恢复
3. 监控系统运行状态，及时发现和处理问题
4. 定期更新依赖和安全补丁
