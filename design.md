# 记账小程序系统设计文档

## 1. 项目概述

### 1.1 项目简介
基于微信小程序的个人记账应用，提供收支记录、分类管理、统计分析等功能，帮助用户更好地管理个人财务。

### 1.2 技术栈
- **前端**: 微信小程序原生开发
- **后端**: Node.js + Express + TypeScript
- **数据库**: SQLite（开发环境）/ MySQL（生产环境）
- **认证**: JWT + 微信小程序登录
- **部署**: PM2 + Nginx

### 1.3 核心功能
- 微信登录认证
- 收支记录管理
- 分类管理
- 统计分析
- 数据同步
- 文件上传

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "微信生态"
        A[微信小程序]
        B[微信服务器]
    end
    
    subgraph "应用层"
        C[小程序前端]
        D[API网关]
    end
    
    subgraph "服务层"
        E[认证服务]
        F[业务服务]
        G[文件服务]
    end
    
    subgraph "数据层"
        H[SQLite/MySQL]
        I[文件存储]
        J[缓存层]
    end
    
    A --> C
    C --> D
    D --> E
    D --> F
    D --> G
    E --> B
    F --> H
    G --> I
    F --> J
```

### 2.2 前端架构

```mermaid
graph TB
    subgraph "小程序架构"
        A[App.js - 应用入口]
        B[Pages - 页面层]
        C[Components - 组件层]
        D[Utils - 工具层]
        E[Services - 服务层]
        F[Store - 状态管理]
    end
    
    subgraph "页面模块"
        G[首页 - 概览]
        H[记账 - 添加记录]
        I[分类 - 管理分类]
        J[统计 - 数据分析]
        K[我的 - 个人中心]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    E --> F
    B --> G
    B --> H
    B --> I
    B --> J
    B --> K
```

### 2.3 后端架构

```mermaid
graph TB
    subgraph "Express应用"
        A[app.ts - 应用入口]
        B[Routes - 路由层]
        C[Controllers - 控制器层]
        D[Services - 服务层]
        E[Models - 数据模型]
        F[Middleware - 中间件]
    end
    
    subgraph "核心模块"
        G[认证模块]
        H[用户模块]
        I[分类模块]
        J[交易模块]
        K[统计模块]
        L[上传模块]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    A --> F
    D --> G
    D --> H
    D --> I
    D --> J
    D --> K
    D --> L
```

## 3. 数据库设计

### 3.1 数据库ER图

```mermaid
erDiagram
    USERS ||--o{ TRANSACTIONS : creates
    USERS ||--o{ CATEGORIES : owns
    CATEGORIES ||--o{ TRANSACTIONS : categorizes
    
    USERS {
        string id PK
        string openid UK
        string nickname
        string avatar_url
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    CATEGORIES {
        string id PK
        string user_id FK
        string name
        enum type
        string icon
        string color
        decimal budget
        int sort_order
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    TRANSACTIONS {
        string id PK
        string user_id FK
        string category_id FK
        enum type
        decimal amount
        string description
        date date
        string image_url
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
```

### 3.2 表结构详细设计

#### 用户表 (users)
```sql
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    openid VARCHAR(100) UNIQUE NOT NULL,
    nickname VARCHAR(100),
    avatar_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME NULL
);
```

#### 分类表 (categories)
```sql
CREATE TABLE categories (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50),
    name VARCHAR(50) NOT NULL,
    type ENUM('income', 'expense') NOT NULL,
    icon VARCHAR(10),
    color VARCHAR(20),
    budget DECIMAL(10,2),
    sort_order INT DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 交易记录表 (transactions)
```sql
CREATE TABLE transactions (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    category_id VARCHAR(50) NOT NULL,
    type ENUM('income', 'expense') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    date DATE NOT NULL,
    image_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

## 4. API接口设计

### 4.1 接口概览

| 模块 | 接口 | 方法 | 描述 |
|------|------|------|------|
| 认证 | /api/auth/login | POST | 微信登录 |
| 认证 | /api/auth/refresh | POST | 刷新令牌 |
| 用户 | /api/users/profile | GET | 获取用户信息 |
| 用户 | /api/users/profile | PUT | 更新用户信息 |
| 分类 | /api/categories | GET | 获取分类列表 |
| 分类 | /api/categories | POST | 创建分类 |
| 分类 | /api/categories/:id | PUT | 更新分类 |
| 分类 | /api/categories/:id | DELETE | 删除分类 |
| 交易 | /api/transactions | GET | 获取交易列表 |
| 交易 | /api/transactions | POST | 创建交易 |
| 交易 | /api/transactions/:id | PUT | 更新交易 |
| 交易 | /api/transactions/:id | DELETE | 删除交易 |
| 统计 | /api/statistics/overview | GET | 概览统计 |
| 统计 | /api/statistics/monthly | GET | 月度统计 |
| 统计 | /api/statistics/categories | GET | 分类统计 |
| 上传 | /api/upload/image | POST | 上传图片 |

### 4.2 接口详细设计

#### 用户登录接口
```json
POST /api/auth/login
Content-Type: application/json

Request:
{
  "code": "微信授权码",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}

Response:
{
  "success": true,
  "data": {
    "token": "JWT令牌",
    "refreshToken": "刷新令牌",
    "user": {
      "id": "用户ID",
      "nickname": "用户昵称",
      "avatar_url": "头像URL"
    }
  }
}
```

#### 创建交易记录接口
```json
POST /api/transactions
Authorization: Bearer <token>
Content-Type: application/json

Request:
{
  "type": "expense",
  "amount": 25.50,
  "category_id": "cat_xxx",
  "description": "午餐",
  "date": "2023-12-01",
  "image_url": "/uploads/xxx.jpg"
}

Response:
{
  "success": true,
  "data": {
    "id": "txn_xxx",
    "type": "expense",
    "amount": 25.50,
    "category_id": "cat_xxx",
    "category_name": "餐饮",
    "category_icon": "🍔",
    "description": "午餐",
    "date": "2023-12-01",
    "created_at": "2023-12-01T12:00:00Z"
  }
}
```

## 5. 业务流程设计

### 5.1 用户登录流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant MP as 小程序
    participant WX as 微信服务器
    participant API as 后端API
    participant DB as 数据库

    U->>MP: 点击登录
    MP->>WX: wx.login()获取code
    WX-->>MP: 返回code
    MP->>API: POST /api/auth/login {code, userInfo}
    API->>WX: 调用微信API验证code
    WX-->>API: 返回openid和session_key
    API->>DB: 查询或创建用户
    DB-->>API: 返回用户信息
    API->>API: 生成JWT令牌
    API-->>MP: 返回token和用户信息
    MP->>MP: 存储token到本地
    MP-->>U: 登录成功，跳转首页
```

### 5.2 记账流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant MP as 小程序
    participant API as 后端API
    participant DB as 数据库

    U->>MP: 进入记账页面
    MP->>API: GET /api/categories 获取分类
    API->>DB: 查询用户分类
    DB-->>API: 返回分类列表
    API-->>MP: 返回分类数据
    MP-->>U: 显示分类选择
    
    U->>MP: 填写金额、选择分类、添加备注
    U->>MP: 点击保存
    MP->>API: POST /api/transactions 创建记录
    API->>DB: 保存交易记录
    DB-->>API: 返回保存结果
    API-->>MP: 返回创建的记录
    MP-->>U: 显示保存成功，返回首页
```

### 5.3 数据同步流程

```mermaid
sequenceDiagram
    participant MP as 小程序
    participant API as 后端API
    participant DB as 数据库
    participant Cache as 缓存

    MP->>API: 请求数据（带时间戳）
    API->>Cache: 检查缓存
    alt 缓存命中且未过期
        Cache-->>API: 返回缓存数据
        API-->>MP: 返回数据
    else 缓存未命中或已过期
        API->>DB: 查询最新数据
        DB-->>API: 返回数据
        API->>Cache: 更新缓存
        API-->>MP: 返回数据
    end
    
    MP->>MP: 更新本地数据
    MP->>MP: 刷新界面
```

## 6. 前端详细设计

### 6.1 页面结构

```
pages/
├── index/                 # 首页
│   ├── index.js
│   ├── index.wxml
│   ├── index.wxss
│   └── index.json
├── add-record/           # 记账页面
│   ├── index.js
│   ├── index.wxml
│   ├── index.wxss
│   └── index.json
├── categories/           # 分类管理
│   ├── index.js
│   ├── index.wxml
│   ├── index.wxss
│   └── index.json
├── statistics/           # 统计分析
│   ├── index.js
│   ├── index.wxml
│   ├── index.wxss
│   └── index.json
└── profile/              # 个人中心
    ├── index.js
    ├── index.wxml
    ├── index.wxss
    └── index.json
```

### 6.2 组件设计

```
components/
├── record-item/          # 记录条目组件
├── category-selector/    # 分类选择器
├── amount-input/         # 金额输入组件
├── date-picker/          # 日期选择器
├── chart/                # 图表组件
└── loading/              # 加载组件
```

### 6.3 状态管理

```javascript
// store/index.js
const store = {
  state: {
    user: null,
    categories: [],
    transactions: [],
    statistics: {}
  },
  
  mutations: {
    setUser(state, user) {
      state.user = user;
    },
    setCategories(state, categories) {
      state.categories = categories;
    },
    addTransaction(state, transaction) {
      state.transactions.unshift(transaction);
    }
  },
  
  actions: {
    async login({ commit }, { code, userInfo }) {
      const result = await api.login(code, userInfo);
      commit('setUser', result.user);
      wx.setStorageSync('token', result.token);
    }
  }
};
```

## 7. 后端详细设计

### 7.1 项目结构

```
server/
├── src/
│   ├── app.ts                # 应用入口
│   ├── config/               # 配置文件
│   │   └── index.ts
│   ├── controllers/          # 控制器
│   │   ├── auth.controller.ts
│   │   ├── user.controller.ts
│   │   ├── category.controller.ts
│   │   ├── transaction.controller.ts
│   │   └── statistics.controller.ts
│   ├── services/             # 服务层
│   │   ├── auth.service.ts
│   │   ├── user.service.ts
│   │   ├── category.service.ts
│   │   ├── transaction.service.ts
│   │   └── statistics.service.ts
│   ├── models/               # 数据模型
│   │   ├── user.model.ts
│   │   ├── category.model.ts
│   │   └── transaction.model.ts
│   ├── middleware/           # 中间件
│   │   ├── auth.middleware.ts
│   │   ├── validation.middleware.ts
│   │   └── error.middleware.ts
│   ├── routes/               # 路由
│   │   ├── auth.routes.ts
│   │   ├── user.routes.ts
│   │   ├── category.routes.ts
│   │   ├── transaction.routes.ts
│   │   ├── statistics.routes.ts
│   │   └── upload.routes.ts
│   ├── database/             # 数据库
│   │   ├── index.ts
│   │   ├── schema.sql
│   │   └── migrations/
│   ├── utils/                # 工具函数
│   │   ├── logger.ts
│   │   ├── response.ts
│   │   └── helpers.ts
│   └── types/                # 类型定义
│       └── index.ts
├── data/                     # 数据文件
├── uploads/                  # 上传文件
├── logs/                     # 日志文件
├── package.json
├── tsconfig.json
└── .env
```

### 7.2 中间件设计

```typescript
// 认证中间件
export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      throw new AppError('缺少访问令牌', 401);
    }
    
    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
    req.user = decoded;
    next();
  } catch (error) {
    next(new AppError('无效的访问令牌', 401));
  }
};

// 数据验证中间件
export const validateRequest = (schema: Joi.Schema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }
    next();
  };
};
```

### 7.3 错误处理

```typescript
// 全局错误处理中间件
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error(error);
  
  if (error instanceof AppError) {
    return res.status(error.statusCode).json({
      success: false,
      error: {
        message: error.message,
        code: error.statusCode
      }
    });
  }
  
  res.status(500).json({
    success: false,
    error: {
      message: '服务器内部错误',
      code: 500
    }
  });
};
```

## 8. 安全设计

### 8.1 认证安全
- JWT令牌机制
- 令牌过期时间控制
- 刷新令牌机制
- 微信登录验证

### 8.2 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护
- 数据加密存储

### 8.3 接口安全
- 请求频率限制
- 参数验证
- 权限控制
- 日志记录

## 9. 性能优化

### 9.1 前端优化
- 图片懒加载
- 数据分页加载
- 本地缓存策略
- 代码分包

### 9.2 后端优化
- 数据库索引优化
- 查询缓存
- 连接池管理
- 静态资源CDN

## 10. 部署架构

### 10.1 开发环境
```
开发机 -> Git -> 测试服务器
```

### 10.2 生产环境
```
用户 -> CDN -> Nginx -> PM2 -> Node.js应用 -> MySQL
                              -> Redis缓存
                              -> 文件存储
```

## 11. 监控和日志

### 11.1 日志系统
- 访问日志
- 错误日志
- 业务日志
- 性能日志

### 11.2 监控指标
- 服务可用性
- 响应时间
- 错误率
- 资源使用率

## 12. 前端页面流程图

### 12.1 应用启动流程

```mermaid
flowchart TD
    A[应用启动] --> B{检查登录状态}
    B -->|已登录| C[获取用户信息]
    B -->|未登录| D[显示登录页面]
    C --> E[加载首页数据]
    D --> F[用户点击登录]
    F --> G[微信授权]
    G --> H{授权成功?}
    H -->|是| I[调用登录API]
    H -->|否| J[显示授权失败]
    I --> K{登录成功?}
    K -->|是| L[保存用户信息]
    K -->|否| M[显示登录失败]
    L --> E
    E --> N[显示首页]
    J --> D
    M --> D
```

### 12.2 记账页面流程

```mermaid
flowchart TD
    A[进入记账页面] --> B[加载分类数据]
    B --> C[显示分类选择器]
    C --> D[用户选择收入/支出]
    D --> E[显示对应分类]
    E --> F[用户选择分类]
    F --> G[用户输入金额]
    G --> H[用户输入备注]
    H --> I[用户选择日期]
    I --> J{是否上传图片?}
    J -->|是| K[选择图片]
    J -->|否| L[点击保存]
    K --> M[上传图片]
    M --> N{上传成功?}
    N -->|是| L
    N -->|否| O[显示上传失败]
    L --> P[验证数据]
    P --> Q{数据有效?}
    Q -->|是| R[调用保存API]
    Q -->|否| S[显示错误提示]
    R --> T{保存成功?}
    T -->|是| U[显示成功提示]
    T -->|否| V[显示保存失败]
    U --> W[返回首页]
    O --> I
    S --> G
    V --> L
```

### 12.3 统计页面数据流

```mermaid
flowchart TD
    A[进入统计页面] --> B[选择统计维度]
    B --> C{选择月度统计}
    B --> D{选择分类统计}
    B --> E{选择趋势分析}

    C --> F[选择年月]
    F --> G[调用月度统计API]
    G --> H[渲染月度图表]

    D --> I[选择时间范围]
    I --> J[调用分类统计API]
    J --> K[渲染饼图/柱状图]

    E --> L[选择时间范围]
    L --> M[调用趋势分析API]
    M --> N[渲染折线图]

    H --> O[显示详细数据]
    K --> O
    N --> O
```

## 13. 后端服务时序图

### 13.1 用户认证时序图

```mermaid
sequenceDiagram
    participant Client as 小程序客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant WeChat as 微信服务器
    participant DB as 数据库
    participant Cache as Redis缓存

    Client->>Gateway: POST /api/auth/login
    Gateway->>Auth: 转发登录请求
    Auth->>WeChat: 验证微信code
    WeChat-->>Auth: 返回openid和session_key
    Auth->>DB: 查询用户信息
    DB-->>Auth: 返回用户数据

    alt 用户不存在
        Auth->>DB: 创建新用户
        DB-->>Auth: 返回新用户信息
    end

    Auth->>Auth: 生成JWT令牌
    Auth->>Cache: 缓存用户会话
    Auth-->>Gateway: 返回认证结果
    Gateway-->>Client: 返回token和用户信息
```

### 13.2 交易记录创建时序图

```mermaid
sequenceDiagram
    participant Client as 小程序客户端
    participant Gateway as API网关
    participant Auth as 认证中间件
    participant Transaction as 交易服务
    participant Category as 分类服务
    participant DB as 数据库
    participant Cache as Redis缓存

    Client->>Gateway: POST /api/transactions
    Gateway->>Auth: 验证JWT令牌
    Auth->>Cache: 检查令牌缓存
    Cache-->>Auth: 返回用户信息
    Auth->>Gateway: 认证通过
    Gateway->>Transaction: 创建交易记录
    Transaction->>Category: 验证分类存在
    Category->>DB: 查询分类信息
    DB-->>Category: 返回分类数据
    Category-->>Transaction: 分类验证通过
    Transaction->>DB: 保存交易记录
    DB-->>Transaction: 返回保存结果
    Transaction->>Cache: 更新统计缓存
    Transaction-->>Gateway: 返回创建结果
    Gateway-->>Client: 返回交易记录
```

### 13.3 统计数据查询时序图

```mermaid
sequenceDiagram
    participant Client as 小程序客户端
    participant Gateway as API网关
    participant Statistics as 统计服务
    participant Cache as Redis缓存
    participant DB as 数据库

    Client->>Gateway: GET /api/statistics/overview
    Gateway->>Statistics: 查询统计数据
    Statistics->>Cache: 检查缓存

    alt 缓存命中
        Cache-->>Statistics: 返回缓存数据
        Statistics-->>Gateway: 返回统计结果
    else 缓存未命中
        Statistics->>DB: 查询原始数据
        DB-->>Statistics: 返回交易记录
        Statistics->>Statistics: 计算统计指标
        Statistics->>Cache: 更新缓存
        Statistics-->>Gateway: 返回统计结果
    end

    Gateway-->>Client: 返回统计数据
```

## 14. 数据流图

### 14.1 系统数据流图

```mermaid
flowchart LR
    subgraph "数据输入"
        A[用户操作]
        B[微信登录]
        C[文件上传]
    end

    subgraph "数据处理"
        D[数据验证]
        E[业务逻辑]
        F[数据转换]
    end

    subgraph "数据存储"
        G[SQLite/MySQL]
        H[文件系统]
        I[Redis缓存]
    end

    subgraph "数据输出"
        J[API响应]
        K[统计报表]
        L[文件下载]
    end

    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    G --> J
    H --> L
    I --> J
    J --> K
```

### 14.2 缓存策略图

```mermaid
flowchart TD
    A[API请求] --> B{检查缓存}
    B -->|命中| C[返回缓存数据]
    B -->|未命中| D[查询数据库]
    D --> E[处理数据]
    E --> F[更新缓存]
    F --> G[返回数据]

    H[数据变更] --> I[清除相关缓存]
    I --> J[更新数据库]
    J --> K[重新计算缓存]
```

## 15. 错误处理流程

### 15.1 前端错误处理

```mermaid
flowchart TD
    A[API调用] --> B{请求成功?}
    B -->|是| C{业务成功?}
    B -->|否| D[网络错误处理]
    C -->|是| E[正常业务流程]
    C -->|否| F[业务错误处理]

    D --> G[显示网络错误提示]
    F --> H[显示业务错误提示]
    G --> I[提供重试选项]
    H --> J[引导用户操作]

    I --> K{用户选择重试?}
    K -->|是| A
    K -->|否| L[返回上级页面]
```

### 15.2 后端错误处理

```mermaid
flowchart TD
    A[接收请求] --> B[参数验证]
    B --> C{验证通过?}
    C -->|否| D[返回400错误]
    C -->|是| E[业务处理]
    E --> F{处理成功?}
    F -->|是| G[返回成功响应]
    F -->|否| H[错误分类]

    H --> I{业务错误?}
    H --> J{系统错误?}
    H --> K{第三方错误?}

    I --> L[返回业务错误]
    J --> M[记录错误日志]
    K --> N[返回服务不可用]

    M --> O[返回500错误]

    D --> P[记录请求日志]
    L --> P
    O --> P
    N --> P
```

## 16. 服务端本地运行要求

### 16.1 环境依赖

#### 必需软件
- **Node.js**: >= 16.0.0 (推荐 18.x LTS)
- **npm**: >= 8.0.0 (随Node.js安装)
- **Git**: >= 2.20.0 (用于版本控制)

#### 可选软件
- **MySQL**: 8.0+ (生产环境推荐，开发环境使用SQLite)
- **Redis**: 6.0+ (缓存，可选)
- **PM2**: 进程管理器 (生产环境)
- **Nginx**: 反向代理 (生产环境)

### 16.2 快速启动步骤

#### 自动化设置（推荐）
```bash
# Linux/macOS
cd server
chmod +x scripts/setup-project.sh
./scripts/setup-project.sh

# Windows
cd server
scripts\setup-project.bat
```

#### 手动设置
```bash
# 1. 环境检查
node --version  # 确保 >= 16.0.0
npm --version   # 确保 >= 8.0.0

# 2. 安装依赖
cd server
npm install

# 3. 环境配置
cp .env.example .env
# 编辑 .env 文件配置必要变量

# 4. 初始化数据库
node scripts/init-database.js

# 5. 启动服务
npm run dev
```

### 16.3 数据库设置

#### SQLite（默认，无需安装）
- 自动创建数据库文件
- 位置：`./data/accounting.db`
- 适用于开发和小规模部署

#### MySQL（生产环境推荐）
```sql
-- 创建数据库
CREATE DATABASE accounting_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'accounting_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON accounting_app.* TO 'accounting_user'@'localhost';
FLUSH PRIVILEGES;
```

### 16.4 目录结构要求
```
server/
├── data/           # 数据库文件目录
├── uploads/        # 文件上传目录
├── logs/           # 日志文件目录
├── scripts/        # 脚本文件目录
├── src/            # 源代码目录
├── dist/           # 编译输出目录（构建后生成）
├── node_modules/   # 依赖包目录（npm install后生成）
├── .env            # 环境配置文件
└── package.json    # 项目配置文件
```

### 16.5 环境变量配置

#### 必需配置
```env
PORT=3000
NODE_ENV=development
DATABASE_PATH=./data/accounting.db
JWT_SECRET=your-secret-key
JWT_REFRESH_SECRET=your-refresh-secret
```

#### 微信小程序配置
```env
WECHAT_APP_ID=your-app-id
WECHAT_APP_SECRET=your-app-secret
```

#### 可选配置
```env
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=5242880
LOG_LEVEL=info
CORS_ORIGIN=*
```

### 16.6 验证安装

#### 健康检查
```bash
curl http://localhost:3000/health
```

#### API测试
```bash
curl http://localhost:3000/api
```

#### 数据库检查
```bash
ls -la data/accounting.db
```

### 16.7 故障排除

详细的故障排除指南请参考：`server/TROUBLESHOOTING.md`

常见问题：
- Node.js ICU库版本不匹配
- 端口被占用
- 权限问题
- TypeScript编译错误

---

*本文档将随着项目开发进度持续更新和完善。*
