# 401认证错误修复指南

## 🐛 问题描述

前端调用后端API时报401错误：
```
GET http://127.0.0.1:3000/api/categories?type=expense 401 (Unauthorized)
```

## 🔍 问题原因

前端在调用需要认证的API时没有设置 `needAuth: true`，导致请求没有携带Authorization头。

## ✅ 已修复的API调用

### 1. 分类服务 (category.service.ts)
- ✅ `getCategories()` - 获取分类列表
- ✅ `createCategory()` - 创建分类
- ✅ `updateCategory()` - 更新分类
- ✅ `deleteCategory()` - 删除分类

### 2. 交易服务 (transaction.service.ts)
- ✅ `getTransactions()` - 获取交易记录
- ✅ `getTransactionById()` - 获取单个交易记录
- ✅ `createTransaction()` - 创建交易记录
- ✅ `updateTransaction()` - 更新交易记录
- ✅ `deleteTransaction()` - 删除交易记录
- ✅ `batchOperation()` - 批量操作

### 3. 统计服务 (statistics.service.ts)
- ✅ `getOverview()` - 获取概览统计
- ✅ `getMonthlyStatistics()` - 获取月度统计
- ✅ `getCategoryStatistics()` - 获取分类统计
- ✅ `getTrendAnalysis()` - 获取趋势分析

### 4. 上传服务 (upload.service.ts)
- ✅ 修复了API基础URL配置问题
- ✅ 确认已正确添加Authorization头

## 🔧 修复详情

### 修复前
```typescript
const response = await apiService.get<Category[]>('/categories', params, {
  showLoading: false
});
```

### 修复后
```typescript
const response = await apiService.get<Category[]>('/categories', params, {
  showLoading: false,
  needAuth: true  // 添加认证标识
});
```

## 📋 认证流程检查

### 1. 不需要认证的API
- ✅ `POST /api/auth/login` - 用户登录
- ✅ `POST /api/auth/refresh` - 刷新Token
- ✅ `GET /api/health` - 健康检查

### 2. 需要认证的API
- ✅ 所有分类相关API
- ✅ 所有交易记录API
- ✅ 所有统计分析API
- ✅ 文件上传API

## 🚀 验证修复

### 1. 确保用户已登录
在调用需要认证的API前，确保用户已经登录并获得了Token：

```typescript
// 检查登录状态
const token = authService.getToken();
if (!token) {
  // 跳转到登录页面
  wx.navigateTo({
    url: '/pages/login/login'
  });
  return;
}
```

### 2. 测试API调用
现在所有需要认证的API都会自动携带Authorization头：

```
Authorization: Bearer <token>
```

### 3. 检查网络请求
在小程序开发者工具的网络面板中，确认请求头包含Authorization字段。

## 🔄 登录流程

### 1. 用户登录
```typescript
const loginResult = await authService.wechatLogin();
// Token会自动保存到本地存储
```

### 2. Token自动携带
```typescript
// API服务会自动从本地存储读取Token并添加到请求头
const categories = await categoryService.getCategories();
```

### 3. Token过期处理
```typescript
// 如果Token过期，API服务会自动尝试刷新Token
// 如果刷新失败，会自动跳转到登录页面
```

## 🛡️ 安全检查

### 1. Token存储
- ✅ Token存储在微信小程序的本地存储中
- ✅ 支持Token自动刷新机制
- ✅ Token过期自动清理

### 2. 请求安全
- ✅ 所有需要认证的API都正确设置了认证标识
- ✅ Authorization头格式正确：`Bearer <token>`
- ✅ 网络请求使用HTTPS（生产环境）

## 🔍 故障排除

### 如果仍然出现401错误

#### 1. 检查登录状态
```typescript
const token = authService.getToken();
console.log('当前Token:', token);
```

#### 2. 检查Token有效性
```typescript
// 调用验证Token的API
try {
  await apiService.get('/auth/verify', undefined, { needAuth: true });
  console.log('Token有效');
} catch (error) {
  console.log('Token无效，需要重新登录');
}
```

#### 3. 手动刷新Token
```typescript
try {
  await authService.refreshToken();
  console.log('Token刷新成功');
} catch (error) {
  console.log('Token刷新失败，需要重新登录');
  authService.logout();
}
```

#### 4. 检查后端认证中间件
确认后端路由正确应用了认证中间件：

```typescript
// 后端路由应该这样配置
router.use(authMiddleware); // 应用认证中间件
router.get('/categories', categoryController.getCategories);
```

## 📝 注意事项

1. **首次使用**: 用户首次使用应用时需要先登录
2. **Token过期**: Token过期时会自动尝试刷新，失败则跳转登录页
3. **网络异常**: 网络异常时会显示相应错误提示
4. **并发请求**: 多个并发请求时，Token刷新机制会正确处理

## 🎯 下一步

1. **测试登录流程**: 确保用户可以正常登录
2. **测试API调用**: 验证所有需要认证的API都能正常工作
3. **测试Token刷新**: 验证Token过期时的自动刷新机制
4. **测试错误处理**: 验证各种异常情况的处理

现在所有的认证问题都已修复，用户登录后应该能正常使用所有功能！
