# 小程序配置问题修复指南

## 🐛 问题描述

前端启动后报错：
```
加载月度数据失败: TypeError: Cannot read property 'baseUrl' of undefined
```

## 🔍 问题原因

1. **环境检测问题**: 小程序环境中 `wx.getAccountInfoSync()` 可能不可用或返回格式不同
2. **模块导入冲突**: TypeScript中变量名 `config` 与函数参数 `config` 冲突
3. **配置加载失败**: 复杂的环境检测逻辑在某些情况下失败

## ✅ 解决方案

### 1. 创建简化配置文件

创建了 `utils/simple-config.ts`，特点：
- 更可靠的环境检测
- 简化的配置结构
- 更好的错误处理

### 2. 修复API文件导入

修改 `utils/api.ts`：
```typescript
// 修复前
import { config, utils } from './config';

// 修复后
import { config as appConfig } from './simple-config';
```

### 3. 添加调试信息

在配置加载时添加日志输出，便于调试：
```typescript
console.log('[CONFIG] 当前环境:', env);
console.log('[CONFIG] API配置:', config.api);
```

## 📁 文件变更

### 新增文件
- `utils/simple-config.ts` - 简化的配置管理
- `utils/test-config.js` - 配置测试文件

### 修改文件
- `utils/api.ts` - 修复配置导入
- `utils/config.ts` - 改进环境检测

## 🔧 当前配置

### 开发环境
```typescript
{
  api: {
    baseUrl: 'http://127.0.0.1:3000/api',
    timeout: 10000,
    retryCount: 3,
  },
  wechat: {
    appId: 'wx4e10169147d3138b',
  }
}
```

### 生产环境
```typescript
{
  api: {
    baseUrl: 'https://your-domain.com/api', // 需要替换
    timeout: 15000,
    retryCount: 2,
  },
  wechat: {
    appId: 'wx4e10169147d3138b',
  }
}
```

## 🚀 验证修复

### 1. 检查配置加载
在小程序开发者工具控制台查看：
```
[CONFIG] 当前环境: development
[CONFIG] API配置: {baseUrl: "http://127.0.0.1:3000/api", timeout: 10000, retryCount: 3}
```

### 2. 测试API调用
确保API请求正常发送到正确的地址。

### 3. 功能验证
测试登录、数据加载等核心功能。

## 🔄 如果问题仍然存在

### 方案A: 使用静态配置
如果环境检测仍有问题，可以直接在 `api.ts` 中使用静态配置：

```typescript
// 直接使用静态配置
const API_CONFIG = {
  baseUrl: 'http://127.0.0.1:3000/api',
  timeout: 10000,
};
```

### 方案B: 检查小程序基础库版本
确保小程序基础库版本支持 `wx.getAccountInfoSync()`：
- 最低版本要求: 2.2.2
- 推荐版本: 3.0.0+

### 方案C: 手动设置环境
在配置文件中手动指定环境：

```typescript
// 强制指定环境
const FORCE_ENVIRONMENT = 'development'; // 或 'production'

function getEnvironment() {
  return FORCE_ENVIRONMENT;
}
```

## 📝 注意事项

1. **生产环境配置**: 部署前需要修改 `baseUrl` 为实际域名
2. **AppID配置**: 确保使用正确的微信小程序AppID
3. **网络域名**: 在微信公众平台配置合法域名
4. **调试信息**: 生产环境建议关闭调试日志

## 🎯 下一步

1. 验证当前修复是否解决问题
2. 如果正常，可以移除调试日志
3. 准备生产环境配置
4. 测试完整的登录和数据流程
