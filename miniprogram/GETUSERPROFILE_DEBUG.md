# getUserProfile 授权弹窗问题排查指南

## 🔍 问题现象

点击"微信授权登录"按钮后：
- ❌ 没有弹出授权弹窗
- ❌ 直接弹出"登录失败"的提示框
- ❌ 控制台可能显示相关错误信息

## 🛠️ 排查步骤

### 1. 检查运行环境

#### A. 查看控制台输出
在登录页面加载时，会输出以下信息：
```
登录页面加载
系统信息: {...}
微信版本: 8.x.x
基础库版本: 3.x.x
是否支持getUserProfile: true/false
canIUse getUserProfile: true/false
```

#### B. 环境要求检查
- **微信版本**: >= 7.0.9
- **基础库版本**: >= 2.10.4
- **运行环境**: 真机 > 模拟器

### 2. 使用测试按钮

#### A. 点击"测试授权弹窗"按钮
- 这个按钮专门用于测试 `getUserProfile` 功能
- 会显示详细的成功/失败信息
- 帮助确定问题所在

#### B. 观察测试结果
```
成功: 弹出授权弹窗 → 显示用户信息
失败: 显示具体错误信息
```

### 3. 常见问题和解决方案

#### A. 基础库版本过低
**问题**: `canIUse getUserProfile: false`
**解决**: 
1. 在微信开发者工具中调试基础库版本
2. 设置为 3.0.0 或更高版本
3. 重新编译项目

#### B. 在模拟器中测试
**问题**: 模拟器可能不支持 `getUserProfile`
**解决**: 
1. 使用真机调试
2. 扫码在手机微信中打开小程序
3. 在真机上测试授权功能

#### C. 微信版本过低
**问题**: 手机微信版本不支持该API
**解决**: 
1. 升级微信到最新版本
2. 使用备选的快速登录方案

#### D. 开发者工具设置问题
**问题**: 开发者工具配置不正确
**解决**: 
1. 检查 `project.config.json` 中的 `libVersion`
2. 确保设置为 `"3.0.0"` 或更高
3. 重启开发者工具

### 4. 代码检查要点

#### A. 调用时机
```typescript
// ✅ 正确：在用户点击事件中直接调用
onWechatLogin() {
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: (res) => { /* ... */ },
    fail: (err) => { /* ... */ }
  });
}

// ❌ 错误：在异步函数中调用
async onWechatLogin() {
  await someAsyncFunction();
  wx.getUserProfile({ /* ... */ }); // 这样会失败
}
```

#### B. 错误处理
```typescript
wx.getUserProfile({
  desc: '用于完善用户资料',
  success: (res) => {
    console.log('成功:', res);
  },
  fail: (err) => {
    console.error('失败:', err);
    // 检查 err.errMsg 获取具体错误信息
  }
});
```

### 5. 调试技巧

#### A. 添加详细日志
```typescript
console.log('=== 开始调用getUserProfile ===');
console.log('wx.getUserProfile 是否存在:', !!wx.getUserProfile);
console.log('canIUse 检查结果:', wx.canIUse('getUserProfile'));

wx.getUserProfile({
  desc: '用于完善用户资料',
  success: (res) => {
    console.log('=== getUserProfile 成功 ===', res);
  },
  fail: (err) => {
    console.error('=== getUserProfile 失败 ===', err);
    console.error('错误码:', err.errCode);
    console.error('错误信息:', err.errMsg);
  }
});
```

#### B. 检查网络请求
1. 打开开发者工具的网络面板
2. 观察是否有相关的网络请求
3. 检查请求参数和响应

### 6. 替代方案

#### A. 如果 getUserProfile 不可用
```typescript
if (!wx.getUserProfile || !wx.canIUse('getUserProfile')) {
  // 使用快速登录
  this.onSilentLogin();
  return;
}
```

#### B. 降级处理
```typescript
wx.getUserProfile({
  desc: '用于完善用户资料',
  success: (res) => {
    // 正常流程
  },
  fail: (err) => {
    console.error('授权失败，使用快速登录:', err);
    // 自动降级到快速登录
    this.onSilentLogin();
  }
});
```

## 🔧 修复建议

### 1. 立即检查项目配置
```json
// project.config.json
{
  "libVersion": "3.0.0",  // 确保版本足够高
  "appid": "wx4e10169147d3138b"  // 确保AppID正确
}
```

### 2. 在真机上测试
- 使用微信扫码打开小程序
- 在真机上点击"测试授权弹窗"按钮
- 观察是否弹出授权弹窗

### 3. 检查控制台输出
- 查看页面加载时的环境信息
- 查看点击按钮时的调试信息
- 根据错误信息进行针对性修复

### 4. 使用测试页面
- 导航到 `pages/test-login/test-login`
- 使用专门的测试功能
- 查看详细的测试日志

## 🎯 预期结果

修复后应该看到：
1. ✅ 控制台显示支持 `getUserProfile`
2. ✅ 点击按钮后立即弹出授权弹窗
3. ✅ 用户可以选择"允许"或"拒绝"
4. ✅ 授权成功后能正常登录

## 📞 如果问题仍然存在

请提供以下信息：
1. 控制台的完整输出
2. 运行环境（模拟器/真机）
3. 微信版本和基础库版本
4. 点击按钮后的具体现象
5. 任何错误信息的截图

这样我可以更准确地定位和解决问题。
