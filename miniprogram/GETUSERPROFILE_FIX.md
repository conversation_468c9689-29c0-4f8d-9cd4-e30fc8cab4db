# getUserProfile 授权弹窗修复方案

## 🐛 问题描述

点击微信授权登录后，不会弹出授权弹窗，导致 `wx.getUserProfile` 调用失败。

## 🔍 问题根本原因

根据微信官方文档：https://developers.weixin.qq.com/miniprogram/dev/api/open-api/user-info/wx.getUserProfile.html

**关键限制**：
> `wx.getUserProfile` 必须在用户点击事件的**同步回调**中调用，不能在异步函数、Promise、setTimeout 等异步操作中调用。

之前的代码问题：
```typescript
// ❌ 错误的调用方式
async onWechatLogin() {
  try {
    const result = await authService.wechatLogin(); // 异步调用
    // getUserProfile 在异步函数中调用，会失败
  } catch (error) {
    // ...
  }
}
```

## ✅ 正确的修复方案

### 1. 在点击事件中直接调用 getUserProfile

```typescript
// ✅ 正确的调用方式
onWechatLogin() {
  // 必须在用户点击事件的同步回调中调用
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: (userProfileRes) => {
      // 获取用户信息成功后，进行后续登录操作
      this.performWechatLogin(userProfileRes.userInfo);
    },
    fail: (error) => {
      // 处理授权失败
      console.error('获取用户信息失败:', error);
    },
  });
}
```

### 2. 分离用户信息获取和登录逻辑

**修改前的流程**：
```
点击登录 → 调用异步函数 → 在异步函数中调用getUserProfile ❌
```

**修改后的流程**：
```
点击登录 → 直接调用getUserProfile → 成功后调用登录函数 ✅
```

## 🔧 具体修复内容

### 1. 登录页面 (login.ts)

#### A. 修改点击事件处理
```typescript
/**
 * 微信授权登录
 * 必须由用户点击触发，直接在点击事件中调用getUserProfile
 */
onWechatLogin() {
  if (this.data.loading) return;

  // 必须在用户点击事件的同步回调中调用getUserProfile
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: (userProfileRes) => {
      console.log('获取用户信息成功:', userProfileRes);
      // 获取用户信息成功后，进行登录
      this.performWechatLogin(userProfileRes.userInfo);
    },
    fail: (error) => {
      console.error('获取用户信息失败:', error);
      // 提供备选方案
      wx.showModal({
        title: '授权失败',
        content: '需要您的授权才能获取头像和昵称，您也可以选择快速登录',
        cancelText: '快速登录',
        confirmText: '重新授权',
        success: (res) => {
          if (res.cancel) {
            this.onSilentLogin(); // 备选快速登录
          }
        },
      });
    },
  });
}
```

#### B. 新增登录执行函数
```typescript
/**
 * 执行微信登录（获取到用户信息后）
 */
async performWechatLogin(userInfo: WechatMiniprogram.UserInfo) {
  try {
    this.setData({ loading: true });
    
    wx.showLoading({
      title: '登录中...',
      mask: true,
    });

    // 使用authService的新方法
    const result = await authService.wechatLoginWithUserInfo(userInfo);

    wx.hideLoading();
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500,
    });

    // 延迟跳转
    setTimeout(() => {
      this.redirectToHome();
    }, 1500);

  } catch (error) {
    // 错误处理...
  } finally {
    this.setData({ loading: false });
  }
}
```

### 2. 认证服务 (auth.service.ts)

#### 新增专门的方法处理已获取的用户信息
```typescript
/**
 * 微信授权登录（使用已获取的用户信息）
 * 由登录页面调用，传入已获取的用户信息
 */
async wechatLoginWithUserInfo(userInfo: WechatMiniprogram.UserInfo): Promise<LoginResponse> {
  try {
    // 1. 获取微信登录code
    const loginResult = await new Promise<WechatMiniprogram.LoginSuccessCallbackResult>((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject,
      });
    });

    if (!loginResult.code) {
      throw new Error('获取微信登录code失败');
    }

    // 2. 调用后端登录接口
    const loginRequest: WechatLoginRequest = {
      code: loginResult.code,
      userInfo: {
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        gender: userInfo.gender,
      },
    };

    const response = await apiService.post<LoginResponse>('/auth/login', loginRequest, {
      needAuth: false,
      showLoading: false, // 由调用方控制loading
    });

    if (response.code === 200 && response.data) {
      // 保存Token和用户信息
      apiService.setToken(response.data.token, response.data.refreshToken);
      wx.setStorageSync('userInfo', userInfo);

      return response.data;
    } else {
      throw new Error(response.message || '登录失败');
    }
  } catch (error) {
    console.error('微信授权登录失败:', error);
    throw error;
  }
}
```

### 3. 测试页面 (test-login.ts)

同样修复了测试页面的getUserProfile调用：
```typescript
testWechatLogin() {
  // 必须在用户点击事件的同步回调中调用getUserProfile
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: (userProfileRes) => {
      this.addLog('获取用户信息成功: ' + JSON.stringify(userProfileRes.userInfo));
      this.performTestWechatLogin(userProfileRes.userInfo);
    },
    fail: (error) => {
      this.addLog('获取用户信息失败: ' + JSON.stringify(error));
    },
  });
}
```

## 🎯 修复效果

### 修复前：
- 点击"微信授权登录"按钮
- 不会弹出授权弹窗
- 控制台报错：`getUserProfile:fail can only be invoked by user TAP gesture`

### 修复后：
- 点击"微信授权登录"按钮
- 立即弹出微信授权弹窗
- 用户可以选择"允许"或"拒绝"
- 允许后正常获取用户信息并完成登录
- 拒绝后提供快速登录备选方案

## 📋 测试验证

### 1. 基本功能测试
- [ ] 点击"微信授权登录"按钮
- [ ] 确认弹出授权弹窗
- [ ] 点击"允许"，确认登录成功
- [ ] 点击"拒绝"，确认提供备选方案

### 2. 用户体验测试
- [ ] 授权弹窗显示正确的描述文字
- [ ] 拒绝授权后的提示友好
- [ ] 备选快速登录功能正常

### 3. 兼容性测试
- [ ] 不同版本的微信小程序基础库
- [ ] 不同的设备和系统版本

## 🔄 用户流程

```
用户点击"微信授权登录"
         ↓
    弹出授权弹窗
         ↓
    ┌─────────┴─────────┐
    │                   │
   允许                拒绝
    │                   │
    ↓                   ↓
获取用户信息          提示用户
    ↓                   ↓
调用登录接口        提供快速登录
    ↓                   ↓
登录成功            备选方案
```

## 📝 重要注意事项

1. **同步调用**：`getUserProfile` 必须在点击事件的同步回调中调用
2. **用户体验**：提供清晰的授权说明和备选方案
3. **错误处理**：优雅处理用户拒绝授权的情况
4. **测试验证**：在真机上测试授权弹窗功能

## 🎉 预期结果

修复后，用户点击"微信授权登录"按钮时：
- ✅ 立即弹出微信授权弹窗
- ✅ 用户可以正常选择允许或拒绝
- ✅ 允许后能获取真实的用户头像和昵称
- ✅ 拒绝后有友好的提示和备选方案
- ✅ 整个流程用户体验良好

现在请重新测试微信授权登录功能！
