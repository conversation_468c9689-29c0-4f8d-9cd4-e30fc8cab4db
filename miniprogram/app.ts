// app.ts - 记账小程序应用入口
import authService from './services/auth.service'

// 应用实例的类型声明
interface IAppOption {
  globalData: {
    userInfo?: WechatMiniprogram.UserInfo;
    isLoggedIn: boolean;
    systemInfo?: WechatMiniprogram.SystemInfo;
  };
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback;
}

App<IAppOption>({
  globalData: {
    userInfo: undefined,
    isLoggedIn: false,
    systemInfo: undefined,
  },

  /**
   * 小程序启动时触发
   */
  onLaunch() {
    console.log('记账小程序启动');

    // 获取系统信息
    this.getSystemInfo();

    // 检查登录状态
    this.checkLoginStatus();

    // 初始化应用数据
    this.initAppData();
  },

  /**
   * 小程序显示时触发
   */
  onShow() {
    console.log('记账小程序显示');

    // 检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 小程序隐藏时触发
   */
  onHide() {
    console.log('记账小程序隐藏');
  },

  /**
   * 小程序发生脚本错误或 API 调用报错时触发
   */
  onError(error: string) {
    console.error('小程序发生错误:', error);

    // 错误上报（可以集成第三方错误监控服务）
    this.reportError(error);
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      console.log('系统信息:', systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    try {
      // 尝试自动登录
      const loginSuccess = await authService.autoLogin();
      this.globalData.isLoggedIn = loginSuccess;

      if (loginSuccess) {
        // 获取本地用户信息
        const userInfo = authService.getUserInfo();
        if (userInfo) {
          this.globalData.userInfo = userInfo;

          // 触发用户信息就绪回调
          if (this.userInfoReadyCallback) {
            this.userInfoReadyCallback(userInfo);
          }
        }
        console.log('自动登录成功，用户已登录');
      } else {
        console.log('自动登录失败，需要用户手动登录');
        // 延迟跳转到登录页，避免与页面初始化冲突
        setTimeout(() => {
          this.redirectToLogin();
        }, 1000);
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      this.globalData.isLoggedIn = false;
      // 出错时也跳转到登录页
      setTimeout(() => {
        this.redirectToLogin();
      }, 1000);
    }
  },

  /**
   * 跳转到登录页面
   */
  redirectToLogin() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];

    // 如果当前不在登录页，则跳转
    if (currentPage && !currentPage.route?.includes('login')) {
      wx.reLaunch({
        url: '/pages/login/login',
      });
    }
  },

  /**
   * 初始化应用数据
   */
  initAppData() {
    try {
      // 初始化本地存储结构
      const logs = wx.getStorageSync('logs') || [];
      logs.unshift(Date.now());
      wx.setStorageSync('logs', logs);

      // 清理过期的日志记录（保留最近100条）
      if (logs.length > 100) {
        wx.setStorageSync('logs', logs.slice(0, 100));
      }

      // 初始化其他必要的本地数据
      this.initLocalData();
    } catch (error) {
      console.error('初始化应用数据失败:', error);
    }
  },

  /**
   * 初始化本地数据
   */
  initLocalData() {
    try {
      // 检查并初始化默认设置
      const settings = wx.getStorageSync('app_settings') || {
        theme: 'light',
        currency: 'CNY',
        reminderEnabled: true,
        reminderTime: '21:00',
        autoBackup: true,
        language: 'zh-CN'
      };
      wx.setStorageSync('app_settings', settings);

      // 检查并初始化本地缓存
      const cacheVersion = wx.getStorageSync('cache_version') || '1.0.0';
      const currentVersion = '1.0.0'; // 从配置或构建信息中获取

      if (cacheVersion !== currentVersion) {
        // 版本更新，清理旧缓存
        this.clearOldCache();
        wx.setStorageSync('cache_version', currentVersion);
      }
    } catch (error) {
      console.error('初始化本地数据失败:', error);
    }
  },

  /**
   * 清理旧缓存
   */
  clearOldCache() {
    try {
      // 清理可能过期的缓存数据
      const keysToRemove = [
        'temp_data',
        'cached_categories',
        'cached_statistics'
      ];

      keysToRemove.forEach(key => {
        wx.removeStorageSync(key);
      });

      console.log('已清理旧缓存数据');
    } catch (error) {
      console.error('清理缓存失败:', error);
    }
  },

  /**
   * 错误上报
   */
  reportError(error: string) {
    try {
      // 记录错误到本地
      const errorLogs = wx.getStorageSync('error_logs') || [];
      errorLogs.unshift({
        error,
        timestamp: Date.now(),
        userAgent: this.globalData.systemInfo?.system || 'unknown',
        version: this.globalData.systemInfo?.version || 'unknown'
      });

      // 只保留最近50条错误日志
      if (errorLogs.length > 50) {
        errorLogs.splice(50);
      }

      wx.setStorageSync('error_logs', errorLogs);

      // TODO: 可以在这里集成第三方错误监控服务
      // 例如：Sentry、Bugsnag 等
    } catch (e) {
      console.error('错误上报失败:', e);
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo(): WechatMiniprogram.UserInfo | undefined {
    return this.globalData.userInfo;
  },

  /**
   * 设置用户信息
   */
  setUserInfo(userInfo: WechatMiniprogram.UserInfo) {
    this.globalData.userInfo = userInfo;
    this.globalData.isLoggedIn = true;

    // 触发用户信息就绪回调
    if (this.userInfoReadyCallback) {
      this.userInfoReadyCallback(userInfo);
    }
  },

  /**
   * 清除用户信息
   */
  clearUserInfo() {
    this.globalData.userInfo = undefined;
    this.globalData.isLoggedIn = false;
  }
})