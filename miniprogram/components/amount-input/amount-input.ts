// 金额输入组件
import { validateAmount } from '../../utils/helpers';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前值
    value: {
      type: Number,
      value: 0
    },
    // 占位符
    placeholder: {
      type: String,
      value: '0.00'
    },
    // 最大长度
    maxLength: {
      type: Number,
      value: 10
    },
    // 是否自动聚焦
    autoFocus: {
      type: Boolean,
      value: false
    },
    // 是否显示自定义键盘
    useCustomKeyboard: {
      type: Boolean,
      value: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    displayValue: '',
    showKeyboard: false,
    error: false,
    errorMessage: '',
    focused: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 输入事件处理
     */
    onInput(e: WechatMiniprogram.Input) {
      const value = e.detail.value;
      this.updateValue(value);
    },

    /**
     * 失去焦点事件处理
     */
    onBlur() {
      this.setData({
        focused: false,
        showKeyboard: false
      });
      
      this.validateInput();
    },

    /**
     * 获得焦点事件处理
     */
    onFocus() {
      this.setData({
        focused: true,
        showKeyboard: this.properties.useCustomKeyboard
      });
    },

    /**
     * 自定义键盘按键处理
     */
    onKeyPress(e: WechatMiniprogram.TouchEvent) {
      const key = e.currentTarget.dataset.key as string;
      const currentValue = this.data.displayValue;

      if (key === 'delete') {
        // 删除最后一个字符
        const newValue = currentValue.slice(0, -1);
        this.updateValue(newValue);
      } else if (key === '.') {
        // 添加小数点
        if (!currentValue.includes('.')) {
          const newValue = currentValue + key;
          this.updateValue(newValue);
        }
      } else {
        // 添加数字
        if (currentValue.length < this.properties.maxLength) {
          const newValue = currentValue + key;
          this.updateValue(newValue);
        }
      }
    },

    /**
     * 取消按钮处理
     */
    onCancel() {
      this.setData({
        showKeyboard: false,
        focused: false
      });
      
      // 恢复原始值
      this.setData({
        displayValue: this.properties.value.toString()
      });
    },

    /**
     * 确定按钮处理
     */
    onConfirm() {
      this.setData({
        showKeyboard: false,
        focused: false
      });
      
      this.validateInput();
    },

    /**
     * 更新值
     */
    updateValue(value: string) {
      // 清理输入值
      const cleanValue = this.cleanInputValue(value);
      
      this.setData({
        displayValue: cleanValue,
        error: false,
        errorMessage: ''
      });
    },

    /**
     * 清理输入值
     */
    cleanInputValue(value: string): string {
      // 移除非数字和小数点的字符
      let cleaned = value.replace(/[^\d.]/g, '');
      
      // 确保只有一个小数点
      const parts = cleaned.split('.');
      if (parts.length > 2) {
        cleaned = parts[0] + '.' + parts.slice(1).join('');
      }
      
      // 限制小数位数为2位
      if (parts.length === 2 && parts[1].length > 2) {
        cleaned = parts[0] + '.' + parts[1].substring(0, 2);
      }
      
      // 移除开头的多个0
      if (cleaned.length > 1 && cleaned[0] === '0' && cleaned[1] !== '.') {
        cleaned = cleaned.substring(1);
      }
      
      return cleaned;
    },

    /**
     * 验证输入
     */
    validateInput() {
      const value = this.data.displayValue;
      const validation = validateAmount(value);
      
      if (!validation.isValid) {
        this.setData({
          error: true,
          errorMessage: validation.message || '输入格式错误'
        });
        return;
      }
      
      // 验证通过，触发change事件
      const numValue = parseFloat(value) || 0;
      this.triggerEvent('change', {
        value: numValue,
        displayValue: value
      });
    },

    /**
     * 清空输入
     */
    clear() {
      this.setData({
        displayValue: '',
        error: false,
        errorMessage: ''
      });
    },

    /**
     * 设置值
     */
    setValue(value: number) {
      this.setData({
        displayValue: value > 0 ? value.toString() : '',
        error: false,
        errorMessage: ''
      });
    },

    /**
     * 获取当前值
     */
    getValue(): number {
      return parseFloat(this.data.displayValue) || 0;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化显示值
      if (this.properties.value > 0) {
        this.setData({
          displayValue: this.properties.value.toString()
        });
      }
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'value'(newValue: number) {
      if (newValue !== this.getValue()) {
        this.setValue(newValue);
      }
    }
  }
});
