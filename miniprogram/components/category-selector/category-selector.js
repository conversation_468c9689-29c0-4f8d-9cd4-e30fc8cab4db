"use strict";
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        // 分类列表
        categories: {
            type: Array,
            value: []
        },
        // 当前选中的分类ID
        selectedId: {
            type: String,
            value: '',
            observer: function (newVal) {
                // 处理null值，转换为空字符串
                if (newVal === null || newVal === undefined) {
                    this.setData({ selectedId: '' });
                }
            }
        },
        // 分类类型
        type: {
            type: String,
            value: 'expense' // income | expense
        },
        // 是否显示添加按钮
        showAddButton: {
            type: Boolean,
            value: true
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            value: false
        }
    },
    /**
     * 组件的初始数据
     */
    data: {
        filteredCategories: []
    },
    /**
     * 组件的方法列表
     */
    methods: {
        /**
         * 选择分类
         */
        onSelectCategory(e) {
            if (this.properties.disabled) {
                return;
            }
            const category = e.currentTarget.dataset.category;
            // 触发选择事件
            this.triggerEvent('select', {
                category,
                categoryId: category.id
            });
        },
        /**
         * 添加分类
         */
        onAddCategory() {
            if (this.properties.disabled) {
                return;
            }
            // 触发添加分类事件
            this.triggerEvent('add', {
                type: this.properties.type
            });
        },
        /**
         * 过滤分类
         */
        filterCategories() {
            const categories = this.properties.categories;
            const type = this.properties.type;
            const filtered = categories.filter(category => category.type === type);
            this.setData({
                filteredCategories: filtered
            });
        },
        /**
         * 获取选中的分类
         */
        getSelectedCategory() {
            const categories = this.data.filteredCategories;
            const selectedId = this.properties.selectedId;
            return categories.find(category => category.id === selectedId) || null;
        },
        /**
         * 设置选中的分类
         */
        setSelectedCategory(categoryId) {
            this.setData({
                selectedId: categoryId
            });
        }
    },
    /**
     * 组件生命周期
     */
    lifetimes: {
        attached() {
            this.filterCategories();
        }
    },
    /**
     * 监听属性变化
     */
    observers: {
        'categories, type'() {
            this.filterCategories();
        }
    }
});
