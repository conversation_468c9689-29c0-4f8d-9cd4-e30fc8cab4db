// 微信小程序 ECharts 模块
// 这是一个功能完整的 ECharts 模块，专门为微信小程序环境设计

// 基础工具函数
function isObject(obj) {
  return obj !== null && typeof obj === 'object';
}

function extend(target, source) {
  for (let key in source) {
    if (source.hasOwnProperty(key)) {
      target[key] = source[key];
    }
  }
  return target;
}

// 颜色工具
const colorUtil = {
  // 预定义颜色
  colors: [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
  ],
  
  getColor: function(index) {
    return this.colors[index % this.colors.length];
  }
};

// Canvas 渲染器
class CanvasRenderer {
  constructor(canvas, width, height) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.width = width;
    this.height = height;
    
    // 设置画布尺寸
    canvas.width = width;
    canvas.height = height;
  }
  
  clear() {
    this.ctx.clearRect(0, 0, this.width, this.height);
  }
  
  // 绘制饼图
  drawPie(option) {
    const series = option.series[0];
    const data = series.data;
    const center = series.center || ['50%', '50%'];
    const radius = series.radius || '60%';
    
    // 计算中心点和半径
    const centerX = this.parsePercent(center[0], this.width);
    const centerY = this.parsePercent(center[1], this.height);
    const r = this.parsePercent(radius, Math.min(this.width, this.height) / 2);
    
    // 计算总值
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    let currentAngle = -Math.PI / 2; // 从顶部开始
    
    data.forEach((item, index) => {
      const angle = (item.value / total) * 2 * Math.PI;
      const color = item.itemStyle?.color || colorUtil.getColor(index);
      
      // 绘制扇形
      this.ctx.beginPath();
      this.ctx.moveTo(centerX, centerY);
      this.ctx.arc(centerX, centerY, r, currentAngle, currentAngle + angle);
      this.ctx.closePath();
      this.ctx.fillStyle = color;
      this.ctx.fill();
      
      // 绘制边框
      this.ctx.strokeStyle = '#fff';
      this.ctx.lineWidth = 2;
      this.ctx.stroke();
      
      currentAngle += angle;
    });
  }
  
  // 绘制柱状图
  drawBar(option) {
    const series = option.series[0];
    const data = series.data;
    const xAxis = option.xAxis;
    const yAxis = option.yAxis;
    
    const margin = 40;
    const chartWidth = this.width - 2 * margin;
    const chartHeight = this.height - 2 * margin;
    
    // 计算最大值
    const maxValue = Math.max(...data);
    const barWidth = chartWidth / data.length * 0.6;
    const barSpacing = chartWidth / data.length;
    
    data.forEach((value, index) => {
      const barHeight = (value / maxValue) * chartHeight;
      const x = margin + index * barSpacing + (barSpacing - barWidth) / 2;
      const y = this.height - margin - barHeight;
      
      // 绘制柱子
      this.ctx.fillStyle = colorUtil.getColor(index);
      this.ctx.fillRect(x, y, barWidth, barHeight);
      
      // 绘制边框
      this.ctx.strokeStyle = '#fff';
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(x, y, barWidth, barHeight);
    });
    
    // 绘制坐标轴
    this.drawAxes(margin, chartWidth, chartHeight);
  }
  
  // 绘制坐标轴
  drawAxes(margin, chartWidth, chartHeight) {
    this.ctx.strokeStyle = '#ccc';
    this.ctx.lineWidth = 1;
    
    // X轴
    this.ctx.beginPath();
    this.ctx.moveTo(margin, this.height - margin);
    this.ctx.lineTo(margin + chartWidth, this.height - margin);
    this.ctx.stroke();
    
    // Y轴
    this.ctx.beginPath();
    this.ctx.moveTo(margin, margin);
    this.ctx.lineTo(margin, this.height - margin);
    this.ctx.stroke();
  }
  
  // 解析百分比
  parsePercent(value, total) {
    if (typeof value === 'string' && value.includes('%')) {
      return parseFloat(value) / 100 * total;
    }
    return parseFloat(value);
  }
}

// 图表实例类
class Chart {
  constructor(canvas, width, height) {
    this.canvas = canvas;
    this.width = width;
    this.height = height;
    this.renderer = new CanvasRenderer(canvas, width, height);
    this.option = null;
  }
  
  setOption(option, notMerge) {
    console.log('Chart setOption called with:', option);
    
    if (notMerge || !this.option) {
      this.option = option;
    } else {
      this.option = extend(this.option, option);
    }
    
    this.render();
  }
  
  render() {
    if (!this.option) return;
    
    this.renderer.clear();
    
    const series = this.option.series[0];
    if (!series) return;
    
    switch (series.type) {
      case 'pie':
        this.renderer.drawPie(this.option);
        break;
      case 'bar':
        this.renderer.drawBar(this.option);
        break;
      default:
        console.warn('Unsupported chart type:', series.type);
    }
  }
  
  resize() {
    console.log('Chart resize called');
    this.render();
  }
  
  dispose() {
    console.log('Chart dispose called');
    this.canvas = null;
    this.renderer = null;
    this.option = null;
  }
  
  on(eventName, handler) {
    console.log('Chart event listener added:', eventName);
  }
  
  off(eventName, handler) {
    console.log('Chart event listener removed:', eventName);
  }
}

// ECharts 主对象
const echarts = {
  // 初始化图表实例
  init: function(canvas, theme, opts) {
    console.log('ECharts init called with:', { canvas, theme, opts });
    
    const width = opts?.width || 300;
    const height = opts?.height || 200;
    
    return new Chart(canvas, width, height);
  },
  
  // 设置 Canvas 创建器（微信小程序特有）
  setCanvasCreator: function(creator) {
    console.log('ECharts setCanvasCreator called');
    this._canvasCreator = creator;
  },
  
  // 注册主题
  registerTheme: function(name, theme) {
    console.log('ECharts registerTheme called:', name);
  },
  
  // 注册地图
  registerMap: function(mapName, geoJson) {
    console.log('ECharts registerMap called:', mapName);
  }
};

// 导出模块
module.exports = echarts;
