// 记账页面逻辑
import { formatDate, validateAmount } from '../../utils/helpers';
import categoryService, { Category } from '../../services/category.service';
import transactionService, { CreateTransactionRequest, UpdateTransactionRequest, Transaction } from '../../services/transaction.service';
import uploadService from '../../services/upload.service';
import authService from '../../services/auth.service';

// 页面数据接口
interface PageData {
  recordType: 'income' | 'expense';
  amount: number;
  selectedCategoryId: string;
  categories: Category[];
  selectedDate: string;
  selectedTime: string;
  description: string;
  receiptImage: string;
  quickNotes: string[];

  loading: boolean;
  loadingText: string;
  isEditing: boolean;
  editingId: string;
  canSave: boolean;
}

Page<PageData>({
  /**
   * 页面的初始数据
   */
  data: {
    recordType: 'expense',
    amount: 0,
    selectedCategoryId: '',
    categories: [],
    selectedDate: formatDate(new Date(), 'YYYY-MM-DD'), // 默认为当前日期
    selectedTime: formatDate(new Date(), 'HH:mm'),      // 默认为当前时间
    description: '',
    receiptImage: '',
    quickNotes: [],
    loading: false,
    loadingText: '加载中...',
    isEditing: false,
    editingId: '',
    canSave: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: Record<string, string | undefined>) {
    console.log('记账页面加载', options);
    this.initPage(options);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('记账页面显示');
    this.loadCategories();
  },

  /**
   * 初始化页面
   */
  async initPage(options: Record<string, string | undefined>) {
    try {
      // 检查登录状态
      if (!authService.isLoggedIn()) {
        console.log('用户未登录，跳转到登录页');
        wx.redirectTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 设置默认日期时间
      this.setDefaultDateTime();

      // 处理页面参数
      this.handlePageOptions(options);

      // 预加载所有分类到缓存（确保缓存完整数据）
      await categoryService.preloadCategories();

      // 加载当前类型的分类数据
      await this.loadCategories();
      
      // 如果是编辑模式，加载交易数据
      if (this.data.isEditing && this.data.editingId) {
        await this.loadTransactionData(this.data.editingId);
      }
      
      // 加载快速备注
      this.loadQuickNotes();
      
    } catch (error) {
      console.error('初始化页面失败:', error);
      this.showError('页面加载失败，请重试');
    }
  },

  /**
   * 处理页面参数
   */
  handlePageOptions(options: Record<string, string | undefined>) {
    // 设置记账类型
    if (options.type === 'income' || options.type === 'expense') {
      this.setData({ recordType: options.type });
    }
    
    // 设置编辑模式
    if (options.id) {
      this.setData({
        isEditing: true,
        editingId: options.id
      });
      
      // 更新导航栏标题
      wx.setNavigationBarTitle({
        title: '编辑记录'
      });
    }
    
    // 设置预选分类
    if (options.categoryId) {
      this.setData({ selectedCategoryId: options.categoryId });
    }
  },

  /**
   * 设置默认日期时间
   */
  setDefaultDateTime() {
    const now = new Date();
    const date = formatDate(now, 'YYYY-MM-DD');
    const time = formatDate(now, 'HH:mm');
    
    this.setData({
      selectedDate: date,
      selectedTime: time
    });
  },

  /**
   * 加载分类数据 - 优先使用缓存，缓存不存在时从后端获取
   */
  async loadCategories() {
    try {
      console.log('开始加载分类，类型:', this.data.recordType);

      // 优先使用缓存，如果缓存中有完整数据就使用缓存
      const categories = await categoryService.getCategories(this.data.recordType, true);
      console.log('获取到的分类数据:', categories);

      this.setData({ categories });

      // 如果没有选中分类且有分类数据，选择第一个
      if (!this.data.selectedCategoryId && categories.length > 0) {
        this.setData({ selectedCategoryId: categories[0].id });
      }

      this.checkCanSave();
    } catch (error) {
      console.error('加载分类失败:', error);
      this.showError('加载分类失败，请重试');
    }
  },

  /**
   * 加载交易数据（编辑模式）
   */
  async loadTransactionData(id: string) {
    try {
      this.setData({ loading: true, loadingText: '加载数据中...' });
      
      const transaction = await transactionService.getTransaction(id);
      
      this.setData({
        recordType: transaction.type,
        amount: transaction.amount,
        selectedCategoryId: transaction.categoryId,
        description: transaction.description,
        selectedDate: transaction.date,
        selectedTime: formatDate(new Date(transaction.createTime), 'HH:mm'),
        receiptImage: transaction.imageUrl || ''
      });
      
      this.checkCanSave();
    } catch (error) {
      console.error('加载交易数据失败:', error);
      this.showError('加载数据失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载快速备注
   */
  loadQuickNotes() {
    const quickNotes = this.data.recordType === 'expense' 
      ? ['餐饮', '交通', '购物', '娱乐', '日用品', '其他']
      : ['工资', '奖金', '兼职', '投资', '礼金', '其他'];
    
    this.setData({ quickNotes });
  },

  /**
   * 切换记账类型
   */
  onSwitchType(e: WechatMiniprogram.TouchEvent) {
    const type = e.currentTarget.dataset.type as 'income' | 'expense';
    
    if (type !== this.data.recordType) {
      this.setData({
        recordType: type,
        selectedCategoryId: '', // 重置分类选择
        categories: [] // 清空分类列表
      });
      
      // 重新加载分类和快速备注
      this.loadCategories();
      this.loadQuickNotes();
    }
  },

  /**
   * 金额变化
   */
  onAmountChange(e: WechatMiniprogram.CustomEvent) {
    const amountStr = e.detail.value;
    const amount = parseFloat(amountStr) || 0;
    this.setData({ amount });
    this.checkCanSave();
  },

  /**
   * 分类选择
   */
  onCategorySelect(e: WechatMiniprogram.CustomEvent) {
    const categoryId = e.detail.categoryId;
    this.setData({ selectedCategoryId: categoryId });
    this.checkCanSave();
  },

  /**
   * 添加分类
   */
  onAddCategory() {
    wx.navigateTo({
      url: `/pages/category-edit/category-edit?type=${this.data.recordType}&mode=add`
    });
  },



  /**
   * 日期变化
   */
  onDateChange(e: WechatMiniprogram.PickerChange) {
    this.setData({
      selectedDate: e.detail.value
    });
  },



  /**
   * 时间变化
   */
  onTimeChange(e: WechatMiniprogram.PickerChange) {
    this.setData({
      selectedTime: e.detail.value
    });
  },

  /**
   * 备注输入
   */
  onDescriptionInput(e: WechatMiniprogram.Input) {
    this.setData({ description: e.detail.value });
  },

  /**
   * 快速备注
   */
  onQuickNote(e: WechatMiniprogram.TouchEvent) {
    const note = e.currentTarget.dataset.note as string;
    this.setData({ description: note });
  },

  /**
   * 上传凭证
   */
  async onUploadReceipt() {
    try {
      this.setData({ loading: true, loadingText: '上传中...' });
      
      const result = await uploadService.chooseAndUploadImage({
        maxSize: 5 * 1024 * 1024, // 5MB
        quality: 0.8
      });
      
      this.setData({ receiptImage: result.url });
    } catch (error) {
      console.error('上传凭证失败:', error);
      this.showError('上传失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 预览凭证
   */
  onPreviewReceipt() {
    if (this.data.receiptImage) {
      uploadService.previewImage(this.data.receiptImage);
    }
  },

  /**
   * 删除凭证
   */
  onDeleteReceipt() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张凭证吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ receiptImage: '' });
        }
      }
    });
  },

  /**
   * 检查是否可以保存
   */
  checkCanSave() {
    const { amount, selectedCategoryId } = this.data;
    const canSave = amount > 0 && selectedCategoryId !== '';
    this.setData({ canSave });
  },

  /**
   * 格式化日期显示
   */
  formatDate(date: string): string {
    const today = formatDate(new Date(), 'YYYY-MM-DD');
    const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000), 'YYYY-MM-DD');
    
    if (date === today) {
      return '今天';
    } else if (date === yesterday) {
      return '昨天';
    } else {
      return formatDate(new Date(date), 'MM月DD日');
    }
  },

  /**
   * 保存记录
   */
  async onSave() {
    if (!this.data.canSave) {
      return;
    }

    try {
      this.setData({ loading: true, loadingText: '保存中...' });
      
      const requestData = this.buildRequestData();
      console.log('准备发送的数据:', requestData);

      if (this.data.isEditing) {
        await transactionService.updateTransaction(this.data.editingId, requestData);
      } else {
        await transactionService.createTransaction(requestData);
        // 创建成功后清空表单，准备下一次输入
        this.resetForm();
      }

      // 保存成功，清除相关缓存，确保数据实时更新
      this.clearRelatedCache();

      // 通知其他页面数据已更新
      this.notifyDataUpdate();

      // 显示成功提示
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 1500
      });

      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    } catch (error) {
      console.error('保存记录失败:', error);
      this.showError('保存失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 构建请求数据
   */
  buildRequestData(): CreateTransactionRequest | UpdateTransactionRequest {
    const { recordType, amount, selectedCategoryId, description, selectedDate, receiptImage } = this.data;

    // 确保日期不为空，如果为空则使用当前日期
    let finalDate = selectedDate;
    if (!finalDate) {
      finalDate = formatDate(new Date(), 'YYYY-MM-DD');
      console.warn('日期为空，使用当前日期:', finalDate);
    }

    return {
      type: recordType,
      amount,
      categoryId: selectedCategoryId,
      description: description.trim(),
      date: finalDate,
      imageUrl: receiptImage || undefined
    };
  },

  /**
   * 取消
   */
  onCancel() {
    if (this.hasUnsavedChanges()) {
      wx.showModal({
        title: '确认离开',
        content: '当前有未保存的内容，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 检查是否有未保存的更改
   */
  hasUnsavedChanges(): boolean {
    const { amount, selectedCategoryId, description, receiptImage } = this.data;
    return amount > 0 || selectedCategoryId !== '' || description.trim() !== '' || receiptImage !== '';
  },

  /**
   * 重置表单
   */
  resetForm() {
    const now = new Date();
    this.setData({
      recordType: 'expense',
      amount: 0,
      selectedCategoryId: '',
      description: '',
      selectedDate: formatDate(now, 'YYYY-MM-DD'),
      selectedTime: formatDate(now, 'HH:mm'),
      receiptImage: '',
      canSave: false
    });
  },

  /**
   * 显示错误信息
   */
  showError(message: string) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 清除相关缓存，确保数据实时更新
   */
  clearRelatedCache() {
    try {
      // 清除统计数据缓存 - 使用正确的缓存前缀
      const cacheKeys = [
        'stats_cache_',  // 统计服务的缓存前缀
        'transaction_list_',
        'home_cache_'  // 首页缓存
      ];

      cacheKeys.forEach(keyPrefix => {
        // 获取所有存储的key
        const info = wx.getStorageInfoSync();
        info.keys.forEach(key => {
          if (key.startsWith(keyPrefix)) {
            wx.removeStorageSync(key);
            console.log('清除缓存:', key);
          }
        });
      });

      console.log('相关缓存已清除，确保数据实时更新');
    } catch (error) {
      console.error('清除缓存失败:', error);
    }
  },

  /**
   * 通知其他页面数据已更新
   */
  notifyDataUpdate() {
    try {
      // 设置全局标记，表示数据已更新
      wx.setStorageSync('data_updated_flag', Date.now());

      // 发送全局事件通知
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.emit('dataUpdated');
      }

      console.log('已通知其他页面数据更新');
    } catch (error) {
      console.error('通知数据更新失败:', error);
    }
  }
});
