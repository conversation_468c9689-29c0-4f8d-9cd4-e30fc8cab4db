<!--记账页面-->
<view class="add-record-page">
  <!-- 顶部类型切换 -->
  <view class="type-switcher">
    <view 
      class="type-tab {{recordType === 'expense' ? 'active' : ''}}"
      bindtap="onSwitchType"
      data-type="expense"
    >
      <text class="tab-icon">💸</text>
      <text class="tab-text">支出</text>
    </view>
    <view 
      class="type-tab {{recordType === 'income' ? 'active' : ''}}"
      bindtap="onSwitchType"
      data-type="income"
    >
      <text class="tab-icon">💰</text>
      <text class="tab-text">收入</text>
    </view>
  </view>

  <!-- 金额输入区域 -->
  <view class="amount-section">
    <amount-input
      value="{{amount}}"
      placeholder="0.00"
      auto-focus="{{true}}"
      bind:change="onAmountChange"
    />
  </view>

  <!-- 分类选择 -->
  <view class="category-section card">
    <view class="section-header">
      <text class="section-title">选择分类</text>
      <text class="add-category-btn" bindtap="onAddCategory">
        <text class="icon">+</text>
        <text>添加</text>
      </text>
    </view>
    
    <category-selector
      categories="{{categories}}"
      selected-id="{{selectedCategoryId}}"
      type="{{recordType}}"
      show-add-button="{{false}}"
      bind:select="onCategorySelect"
      bind:add="onAddCategory"
    />
  </view>

  <!-- 日期时间选择 -->
  <view class="datetime-section card">
    <view class="section-header">
      <text class="section-title">记账时间</text>
    </view>
    
    <view class="datetime-content">
      <picker mode="date" value="{{selectedDate}}" bindchange="onDateChange">
        <view class="datetime-item">
          <text class="datetime-label">日期</text>
          <view class="datetime-value">
            <text>{{formatDate(selectedDate)}}</text>
            <text class="icon">📅</text>
          </view>
        </view>
      </picker>
      
      <picker mode="time" value="{{selectedTime}}" bindchange="onTimeChange">
        <view class="datetime-item">
          <text class="datetime-label">时间</text>
          <view class="datetime-value">
            <text>{{selectedTime}}</text>
            <text class="icon">🕐</text>
          </view>
        </view>
      </picker>
    </view>
  </view>

  <!-- 备注输入 -->
  <view class="note-section card">
    <view class="section-header">
      <text class="section-title">备注</text>
      <text class="note-count">{{description.length}}/100</text>
    </view>
    
    <view class="note-content">
      <textarea
        class="note-input"
        placeholder="添加备注信息（可选）"
        value="{{description}}"
        maxlength="100"
        auto-height
        bindinput="onDescriptionInput"
      />
      
      <!-- 快速备注 -->
      <view class="quick-notes" wx:if="{{quickNotes.length > 0}}">
        <text class="quick-notes-title">快速备注</text>
        <view class="quick-notes-list">
          <view 
            class="quick-note-item"
            wx:for="{{quickNotes}}" 
            wx:key="*this"
            bindtap="onQuickNote"
            data-note="{{item}}"
          >
            {{item}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 凭证上传 -->
  <view class="receipt-section card">
    <view class="section-header">
      <text class="section-title">上传凭证</text>
      <text class="optional-text">（可选）</text>
    </view>
    
    <view class="receipt-content">
      <view wx:if="{{!receiptImage}}" class="upload-area" bindtap="onUploadReceipt">
        <view class="upload-icon">📷</view>
        <text class="upload-text">点击上传凭证</text>
        <text class="upload-hint">支持拍照或从相册选择</text>
      </view>
      
      <view wx:else class="receipt-preview">
        <image 
          class="receipt-image" 
          src="{{receiptImage}}" 
          mode="aspectFill"
          bindtap="onPreviewReceipt"
        />
        <view class="receipt-actions">
          <view class="action-btn" bindtap="onUploadReceipt">
            <text class="icon">🔄</text>
            <text>重新上传</text>
          </view>
          <view class="action-btn delete" bindtap="onDeleteReceipt">
            <text class="icon">🗑️</text>
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <view class="action-buttons">
      <view class="btn btn-outline" bindtap="onCancel">
        取消
      </view>
      <view 
        class="btn btn-primary {{canSave ? '' : 'btn-disabled'}}" 
        bindtap="onSave"
      >
        {{isEditing ? '更新' : '保存'}}
      </view>
    </view>
  </view>

  <!-- 安全区域 -->
  <view class="safe-area-bottom"></view>
</view>





<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>
