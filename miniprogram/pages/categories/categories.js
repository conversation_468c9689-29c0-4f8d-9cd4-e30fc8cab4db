"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// 分类管理页面逻辑
const helpers_1 = require("../../utils/helpers");
const category_service_1 = __importDefault(require("../../services/category.service"));
Page({
    /**
     * 页面的初始数据
     */
    data: {
        selectedType: 'expense',
        categories: [],
        defaultCategories: [],
        customCategories: [],
        defaultCategoriesCount: 0,
        customCategoriesCount: 0,
        // 弹窗相关
        showActionModal: false,
        showBudgetModal: false,
        selectedCategory: null,
        budgetAmount: '',
        // 状态
        loading: false,
        loadingText: '加载中...'
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        console.log('分类管理页面加载');
        this.loadCategories();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        console.log('分类管理页面显示');
        this.loadCategories();
    },
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        console.log('下拉刷新');
        this.loadCategories().finally(() => {
            wx.stopPullDownRefresh();
        });
    },
    /**
     * 加载分类数据
     */
    async loadCategories() {
        try {
            this.setData({ loading: true, loadingText: '加载中...' });
            const categories = await category_service_1.default.getCategories(this.data.selectedType);
            // 分离默认分类和自定义分类
            const defaultCategories = categories.filter(cat => cat.isDefault);
            const customCategories = categories.filter(cat => !cat.isDefault);
            this.setData({
                categories,
                defaultCategories,
                customCategories,
                defaultCategoriesCount: defaultCategories.length,
                customCategoriesCount: customCategories.length
            });
        }
        catch (error) {
            console.error('加载分类失败:', error);
            this.showError('加载分类失败，请重试');
        }
        finally {
            this.setData({ loading: false });
        }
    },
    /**
     * 切换分类类型
     */
    onSwitchType(e) {
        const type = e.currentTarget.dataset.type;
        if (type !== this.data.selectedType) {
            this.setData({ selectedType: type });
            this.loadCategories();
        }
    },
    /**
     * 分类详情
     */
    onCategoryDetail(e) {
        const category = e.currentTarget.dataset.category;
        // 跳转到分类详情页面（显示该分类的交易记录）
        wx.navigateTo({
            url: `/pages/category-detail/category-detail?id=${category.id}&name=${category.name}`
        });
    },
    /**
     * 长按分类
     */
    onCategoryLongPress(e) {
        const category = e.currentTarget.dataset.category;
        this.setData({
            showActionModal: true,
            selectedCategory: category,
            budgetAmount: category.budget ? category.budget.toString() : ''
        });
    },
    /**
     * 关闭操作菜单
     */
    onCloseActionModal() {
        this.setData({ showActionModal: false });
    },
    /**
     * 查看分类统计
     */
    onViewCategoryStats(e) {
        const category = e.currentTarget.dataset.category;
        this.setData({ showActionModal: false });
        // 跳转到统计页面，并筛选该分类
        wx.navigateTo({
            url: `/pages/category-stats/category-stats?id=${category.id}&name=${category.name}`
        });
    },
    /**
     * 设置预算
     */
    onSetBudget(e) {
        const category = e.currentTarget.dataset.category;
        this.setData({
            showActionModal: false,
            showBudgetModal: true,
            selectedCategory: category,
            budgetAmount: category.budget ? category.budget.toString() : ''
        });
    },
    /**
     * 关闭预算弹窗
     */
    onCloseBudgetModal() {
        this.setData({ showBudgetModal: false });
    },
    /**
     * 预算输入
     */
    onBudgetInput(e) {
        this.setData({ budgetAmount: e.detail.value });
    },
    /**
     * 保存预算
     */
    async onSaveBudget() {
        const { selectedCategory, budgetAmount } = this.data;
        if (!selectedCategory)
            return;
        try {
            this.setData({ loading: true, loadingText: '保存中...' });
            const budget = parseFloat(budgetAmount) || 0;
            const updateData = { budget };
            await category_service_1.default.updateCategory(selectedCategory.id, updateData);
            // 更新本地数据
            this.updateLocalCategory(selectedCategory.id, { budget });
            this.setData({ showBudgetModal: false });
            wx.showToast({
                title: '预算设置成功',
                icon: 'success',
                duration: 1500
            });
        }
        catch (error) {
            console.error('保存预算失败:', error);
            this.showError('保存失败，请重试');
        }
        finally {
            this.setData({ loading: false });
        }
    },
    /**
     * 编辑分类
     */
    onEditCategory(e) {
        const category = e.currentTarget.dataset.category;
        this.setData({ showActionModal: false });
        wx.navigateTo({
            url: `/pages/category-edit/category-edit?id=${category.id}&mode=edit`
        });
    },
    /**
     * 删除分类
     */
    onDeleteCategory(e) {
        const category = e.currentTarget.dataset.category;
        this.setData({ showActionModal: false });
        wx.showModal({
            title: '确认删除',
            content: `确定要删除分类"${category.name}"吗？删除后该分类下的记录将变为"其他"分类。`,
            confirmText: '删除',
            confirmColor: '#FF4D4F',
            success: async (res) => {
                if (res.confirm) {
                    try {
                        this.setData({ loading: true, loadingText: '删除中...' });
                        await category_service_1.default.deleteCategory(category.id);
                        // 重新加载分类列表
                        await this.loadCategories();
                        wx.showToast({
                            title: '删除成功',
                            icon: 'success',
                            duration: 1500
                        });
                    }
                    catch (error) {
                        console.error('删除分类失败:', error);
                        this.showError('删除失败，请重试');
                    }
                    finally {
                        this.setData({ loading: false });
                    }
                }
            }
        });
    },
    /**
     * 添加分类
     */
    onAddCategory() {
        wx.navigateTo({
            url: `/pages/category-edit/category-edit?type=${this.data.selectedType}&mode=add`
        });
    },
    /**
     * 更新本地分类数据
     */
    updateLocalCategory(categoryId, updates) {
        const updateCategories = (categories) => {
            return categories.map(cat => cat.id === categoryId ? { ...cat, ...updates } : cat);
        };
        this.setData({
            categories: updateCategories(this.data.categories),
            defaultCategories: updateCategories(this.data.defaultCategories),
            customCategories: updateCategories(this.data.customCategories)
        });
    },
    /**
     * 格式化金额
     */
    formatAmount(amount, showSymbol = true) {
        return (0, helpers_1.formatAmount)(amount, showSymbol);
    },
    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
        // 阻止事件冒泡
    },
    /**
     * 显示错误信息
     */
    showError(message) {
        wx.showToast({
            title: message,
            icon: 'none',
            duration: 2000
        });
    }
});
