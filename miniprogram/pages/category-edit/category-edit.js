"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// 分类编辑页面逻辑
const category_service_1 = __importDefault(require("../../services/category.service"));
Page({
    /**
     * 页面的初始数据
     */
    data: {
        isEditing: false,
        categoryId: '',
        categoryName: '',
        categoryType: 'expense',
        selectedIcon: '💰',
        selectedColor: '#1890ff',
        budgetAmount: '',
        iconOptions: [
            { class: '💰', name: '钱袋' },
            { class: '🍔', name: '食物' },
            { class: '🚗', name: '交通' },
            { class: '🏠', name: '住房' },
            { class: '👕', name: '服饰' },
            { class: '🎮', name: '娱乐' },
            { class: '📱', name: '通讯' },
            { class: '💊', name: '医疗' },
            { class: '📚', name: '教育' },
            { class: '🎁', name: '礼物' },
            { class: '✈️', name: '旅行' },
            { class: '💼', name: '工作' },
            { class: '💸', name: '工资' },
            { class: '🏆', name: '奖金' },
            { class: '📈', name: '投资' },
            { class: '🧾', name: '退款' },
            { class: '🎯', name: '兼职' },
            { class: '🎲', name: '其他' }
        ],
        colorOptions: [
            '#1890ff', // 蓝色
            '#52c41a', // 绿色
            '#fa8c16', // 橙色
            '#f5222d', // 红色
            '#722ed1', // 紫色
            '#13c2c2', // 青色
            '#eb2f96', // 粉色
            '#faad14', // 黄色
            '#2f54eb', // 深蓝色
            '#fa541c', // 火红色
            '#a0d911', // 酸橙色
            '#1890ff' // 蓝色
        ],
        canSave: false,
        loading: false,
        loadingText: '加载中...'
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log('分类编辑页面加载', options);
        // 设置页面标题
        const isEditing = options.mode === 'edit';
        wx.setNavigationBarTitle({
            title: isEditing ? '编辑分类' : '添加分类'
        });
        // 设置分类类型
        if (options.type === 'income' || options.type === 'expense') {
            this.setData({ categoryType: options.type });
        }
        // 如果是编辑模式，加载分类数据
        if (isEditing && options.id) {
            this.setData({
                isEditing: true,
                categoryId: options.id
            });
            this.loadCategoryData(options.id);
        }
        this.checkCanSave();
    },
    /**
     * 加载分类数据
     */
    async loadCategoryData(id) {
        try {
            this.setData({ loading: true, loadingText: '加载中...' });
            const category = await category_service_1.default.getCategory(id);
            this.setData({
                categoryName: category.name,
                categoryType: category.type,
                selectedIcon: category.icon,
                selectedColor: category.color,
                budgetAmount: category.budget ? category.budget.toString() : ''
            });
            this.checkCanSave();
        }
        catch (error) {
            console.error('加载分类数据失败:', error);
            this.showError('加载失败，请重试');
        }
        finally {
            this.setData({ loading: false });
        }
    },
    /**
     * 名称输入
     */
    onNameInput(e) {
        this.setData({ categoryName: e.detail.value });
        this.checkCanSave();
    },
    /**
     * 选择类型
     */
    onSelectType(e) {
        const type = e.currentTarget.dataset.type;
        this.setData({ categoryType: type });
    },
    /**
     * 选择图标
     */
    onSelectIcon(e) {
        const icon = e.currentTarget.dataset.icon;
        this.setData({ selectedIcon: icon.class });
    },
    /**
     * 选择颜色
     */
    onSelectColor(e) {
        const color = e.currentTarget.dataset.color;
        this.setData({ selectedColor: color });
    },
    /**
     * 预算输入
     */
    onBudgetInput(e) {
        this.setData({ budgetAmount: e.detail.value });
    },
    /**
     * 检查是否可以保存
     */
    checkCanSave() {
        const { categoryName } = this.data;
        const canSave = categoryName.trim().length > 0;
        this.setData({ canSave });
    },
    /**
     * 保存分类
     */
    async onSave() {
        if (!this.data.canSave)
            return;
        try {
            this.setData({ loading: true, loadingText: '保存中...' });
            const { isEditing, categoryId, categoryName, categoryType, selectedIcon, selectedColor, budgetAmount } = this.data;
            const budget = parseFloat(budgetAmount) || undefined;
            if (isEditing) {
                // 更新分类
                const updateData = {
                    name: categoryName.trim(),
                    type: categoryType,
                    icon: selectedIcon,
                    color: selectedColor,
                    budget
                };
                await category_service_1.default.updateCategory(categoryId, updateData);
            }
            else {
                // 创建分类
                const createData = {
                    name: categoryName.trim(),
                    type: categoryType,
                    icon: selectedIcon,
                    color: selectedColor,
                    budget
                };
                await category_service_1.default.createCategory(createData);
            }
            wx.showToast({
                title: isEditing ? '更新成功' : '创建成功',
                icon: 'success',
                duration: 1500
            });
            // 返回上一页
            setTimeout(() => {
                wx.navigateBack();
            }, 1500);
        }
        catch (error) {
            console.error('保存分类失败:', error);
            this.showError('保存失败，请重试');
        }
        finally {
            this.setData({ loading: false });
        }
    },
    /**
     * 取消
     */
    onCancel() {
        if (this.hasUnsavedChanges()) {
            wx.showModal({
                title: '确认离开',
                content: '当前有未保存的内容，确定要离开吗？',
                success: (res) => {
                    if (res.confirm) {
                        wx.navigateBack();
                    }
                }
            });
        }
        else {
            wx.navigateBack();
        }
    },
    /**
     * 检查是否有未保存的更改
     */
    hasUnsavedChanges() {
        const { isEditing, categoryName } = this.data;
        if (isEditing) {
            // 编辑模式下，检查是否有修改
            // 这里简化处理，实际应该比较所有字段
            return categoryName.trim().length > 0;
        }
        else {
            // 新建模式下，检查是否有输入内容
            return categoryName.trim().length > 0;
        }
    },
    /**
     * 显示错误信息
     */
    showError(message) {
        wx.showToast({
            title: message,
            icon: 'none',
            duration: 2000
        });
    }
});
