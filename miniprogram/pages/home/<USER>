"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// 首页逻辑
const helpers_1 = require("../../utils/helpers");
const statistics_service_1 = __importDefault(require("../../services/statistics.service"));
const transaction_service_1 = __importDefault(require("../../services/transaction.service"));
const auth_service_1 = __importDefault(require("../../services/auth.service"));
Page({
    data: {
        userInfo: null,
        greetingText: '早上好',
        currentMonth: {
            year: new Date().getFullYear(),
            month: new Date().getMonth() + 1
        },
        overview: {
            totalIncome: 0,
            totalExpense: 0,
            balance: 0,
            transactionCount: 0,
            avgDailyExpense: 0
        },
        recentTransactions: [],
        categoryStats: [],
        loading: false
    },
    onLoad() {
        console.log('首页加载');
        this.initPage();
    },
    onShow() {
        console.log('首页显示');
        // 检查登录状态
        if (!auth_service_1.default.isLoggedIn()) {
            wx.redirectTo({ url: '/pages/login/login' });
            return;
        }
        // 检查数据是否需要更新
        this.checkAndRefreshData();
    },
    async initPage() {
        // 检查登录状态
        if (!auth_service_1.default.isLoggedIn()) {
            wx.redirectTo({ url: '/pages/login/login' });
            return;
        }
        // 设置问候语
        this.setGreeting();
        // 设置当前月份
        this.setCurrentMonth();
        // 加载数据
        await this.loadData();
    },
    async loadData() {
        try {
            this.setData({ loading: true });
            // 并行加载数据
            await Promise.all([
                this.loadOverviewData(),
                this.loadRecentTransactions(),
                this.loadCategoryStats()
            ]);
        }
        catch (error) {
            console.error('加载首页数据失败:', error);
            wx.showToast({
                title: '数据加载失败',
                icon: 'error'
            });
        }
        finally {
            this.setData({ loading: false });
        }
    },
    setGreeting() {
        const hour = new Date().getHours();
        let greeting = '早上好';
        if (hour >= 6 && hour < 12) {
            greeting = '早上好';
        }
        else if (hour >= 12 && hour < 18) {
            greeting = '下午好';
        }
        else {
            greeting = '晚上好';
        }
        this.setData({ greetingText: greeting });
    },
    setCurrentMonth() {
        const now = new Date();
        this.setData({
            currentMonth: {
                year: now.getFullYear(),
                month: now.getMonth() + 1
            }
        });
    },
    async loadOverviewData() {
        try {
            const { start, end } = (0, helpers_1.getCurrentMonth)();
            const overview = await statistics_service_1.default.getOverview(start, end);
            // 计算日均支出
            const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
            const avgDailyExpense = overview.totalExpense / daysInMonth;
            this.setData({
                overview: {
                    totalIncome: overview.totalIncome,
                    totalExpense: overview.totalExpense,
                    balance: overview.totalIncome - overview.totalExpense,
                    transactionCount: overview.transactionCount,
                    avgDailyExpense: avgDailyExpense
                }
            });
            console.log('首页概览数据:', this.data.overview);
        }
        catch (error) {
            console.error('加载概览数据失败:', error);
        }
    },
    async loadRecentTransactions() {
        try {
            const response = await transaction_service_1.default.getTransactions({
                page: 1,
                pageSize: 5 // 只获取最近5条
            });
            console.log('首页最近交易数据:', response);
            this.setData({
                recentTransactions: response.list
            });
        }
        catch (error) {
            console.error('加载最近交易失败:', error);
        }
    },
    async loadCategoryStats() {
        try {
            const { start, end } = (0, helpers_1.getCurrentMonth)();
            const stats = await statistics_service_1.default.getCategoryStatistics(start, end, 'expense');
            console.log('首页分类统计数据:', stats);
            // 只取前5个分类
            const topStats = stats.slice(0, 5).map(stat => ({
                categoryName: stat.categoryName,
                amount: stat.amount,
                percentage: stat.percentage
            }));
            this.setData({
                categoryStats: topStats
            });
        }
        catch (error) {
            console.error('加载分类统计失败:', error);
        }
    },
    formatAmount(amount) {
        return '¥' + amount.toFixed(2);
    },
    /**
     * 快速记录支出
     */
    onQuickExpense() {
        wx.navigateTo({
            url: '/pages/add-record/add-record?type=expense'
        });
    },
    /**
     * 快速记录收入
     */
    onQuickIncome() {
        wx.navigateTo({
            url: '/pages/add-record/add-record?type=income'
        });
    },
    /**
     * 搜索功能
     */
    onSearch() {
        wx.navigateTo({
            url: '/pages/search/search'
        });
    },
    /**
     * 通知功能
     */
    onNotification() {
        wx.showToast({
            title: '暂无新通知',
            icon: 'none'
        });
    },
    onQuickRecord(e) {
        const type = e.currentTarget.dataset.type;
        wx.navigateTo({
            url: `/pages/add-record/add-record?type=${type}`
        });
    },
    onViewAllRecords() {
        wx.switchTab({
            url: '/pages/records/records'
        });
    },
    /**
     * 查看详细统计
     */
    onViewStatistics() {
        wx.switchTab({
            url: '/pages/statistics/statistics'
        });
    },
    /**
     * 检查并刷新数据
     */
    checkAndRefreshData() {
        try {
            // 检查数据更新标记
            const lastUpdateFlag = wx.getStorageSync('data_updated_flag');
            const lastLoadTime = wx.getStorageSync('home_last_load_time') || 0;
            // 如果有更新标记且时间比上次加载时间新，则刷新数据
            if (lastUpdateFlag && lastUpdateFlag > lastLoadTime) {
                console.log('检测到数据更新，刷新首页数据');
                this.loadData();
                // 更新加载时间
                wx.setStorageSync('home_last_load_time', Date.now());
            }
            else {
                console.log('数据无更新，跳过刷新');
            }
        }
        catch (error) {
            console.error('检查数据更新失败:', error);
            // 出错时也刷新数据
            this.loadData();
        }
    }
});
