<!--首页-->
<wxs module="utils">
  function formatAmount(amount) {
    if (amount === null || amount === undefined) return '¥0.00';
    return '¥' + Number(amount).toFixed(2);
  }

  function formatDate(dateStr) {
    if (!dateStr) return '';
    var date = getDate(dateStr);
    var month = date.getMonth() + 1;
    var day = date.getDate();
    return month + '月' + day + '日';
  }

  function formatPercentage(value) {
    if (value === null || value === undefined) return '0.00%';
    return Number(value).toFixed(2) + '%';
  }

  module.exports = {
    formatAmount: formatAmount,
    formatDate: formatDate,
    formatPercentage: formatPercentage
  };
</wxs>

<view class="home-page">
  <!-- 顶部用户信息 -->
  <view class="header">
    <view class="user-info">
      <image 
        class="avatar" 
        src="{{userInfo.avatarUrl || '/assets/images/default-avatar.png'}}"
        mode="aspectFill"
      />
      <view class="greeting">
        <text class="welcome">{{greetingText}}</text>
        <text class="username">{{userInfo.nickName || '记账用户'}}</text>
      </view>
    </view>
    
    <view class="header-actions">
      <view class="action-btn" bindtap="onSearch">
        <text class="icon">🔍</text>
      </view>
      <view class="action-btn" bindtap="onNotification">
        <text class="icon">🔔</text>
      </view>
    </view>
  </view>

  <!-- 月度概览卡片 -->
  <view class="overview-card card">
    <view class="card-header">
      <text class="title">{{currentMonth.year}}年{{currentMonth.month}}月</text>
      <view class="period-selector" bindtap="onSelectPeriod">
        <text class="period-text">本月</text>
        <text class="icon">▼</text>
      </view>
    </view>
    
    <view class="overview-content">
      <view class="balance-section">
        <view class="balance-item">
          <text class="label">结余</text>
          <text class="amount balance {{overview.balance >= 0 ? 'positive' : 'negative'}}">
            {{utils.formatAmount(overview.balance)}}
          </text>
        </view>
      </view>
      
      <view class="income-expense-section">
        <view class="income-expense-item">
          <view class="item-header">
            <text class="icon income-icon">📈</text>
            <text class="label">收入</text>
          </view>
          <text class="amount income">{{utils.formatAmount(overview.totalIncome)}}</text>
        </view>

        <view class="divider"></view>

        <view class="income-expense-item">
          <view class="item-header">
            <text class="icon expense-icon">📉</text>
            <text class="label">支出</text>
          </view>
          <text class="amount expense">{{utils.formatAmount(overview.totalExpense)}}</text>
        </view>
      </view>
      
      <view class="stats-section">
        <view class="stat-item">
          <text class="stat-value">{{overview.transactionCount}}</text>
          <text class="stat-label">笔交易</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{utils.formatAmount(overview.avgDailyExpense)}}</text>
          <text class="stat-label">日均支出</text>
        </view>
      </view>
    </view>
  </view>



  <!-- 最近交易 -->
  <view class="recent-transactions card">
    <view class="card-header">
      <text class="title">最近交易</text>
      <text class="more-btn" bindtap="onViewAllTransactions">查看全部</text>
    </view>
    
    <view class="transaction-list" wx:if="{{recentTransactions.length > 0}}">
      <view 
        class="transaction-item"
        wx:for="{{recentTransactions}}" 
        wx:key="id"
        bindtap="onTransactionDetail"
        data-transaction="{{item}}"
      >
        <view class="transaction-icon" style="background-color: {{item.categoryColor}};">
          <text class="icon {{item.categoryIcon}}"></text>
        </view>
        
        <view class="transaction-info">
          <view class="transaction-main">
            <text class="category-name">{{item.categoryName}}</text>
            <text class="amount {{item.type}}">
              {{item.type === 'expense' ? '-' : '+'}}{{utils.formatAmount(item.amount)}}
            </text>
          </view>
          <view class="transaction-meta">
            <text class="description">{{item.description || '无备注'}}</text>
            <text class="date">{{utils.formatDate(item.date)}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <view wx:else class="empty-transactions">
      <view class="empty-icon">📝</view>
      <text class="empty-text">暂无交易记录</text>
      <view class="btn btn-primary btn-small" bindtap="onQuickExpense">
        开始记账
      </view>
    </view>
  </view>

  <!-- 分类支出统计 -->
  <view class="category-stats card" wx:if="{{categoryStats.length > 0}}">
    <view class="card-header">
      <text class="title">本月支出分析</text>
      <text class="more-btn" bindtap="onViewStatistics">详细分析</text>
    </view>
    
    <view class="category-list">
      <view 
        class="category-stat-item"
        wx:for="{{categoryStats}}" 
        wx:key="categoryId"
        wx:if="{{index < 5}}"
      >
        <view class="category-info">
          <view class="category-icon" style="background-color: {{item.categoryColor}};">
            <text class="icon {{item.categoryIcon}}"></text>
          </view>
          <text class="category-name">{{item.categoryName}}</text>
        </view>
        
        <view class="category-amount">
          <text class="amount">{{utils.formatAmount(item.amount)}}</text>
          <text class="percentage">占比 {{utils.formatPercentage(item.percentage)}}</text>
        </view>
        
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            style="width: {{item.percentage}}%; background-color: {{item.categoryColor}};"
          ></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
