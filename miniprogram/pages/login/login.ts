// 登录页面逻辑
import authService from '../../services/auth.service';



Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    showPrivacyModal: false,
    showTermsModal: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    console.log('登录页面加载');

    // 检查运行环境
    const systemInfo = wx.getSystemInfoSync();
    console.log('系统信息:', systemInfo);
    console.log('微信版本:', systemInfo.version);
    console.log('基础库版本:', systemInfo.SDKVersion);

    // 检查是否支持getUserProfile
    console.log('是否支持getUserProfile:', !!wx.getUserProfile);
    console.log('canIUse getUserProfile:', wx.canIUse('getUserProfile'));

    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('登录页面显示');
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    // 如果已经登录，直接跳转到首页
    if (authService.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/home/<USER>'
      });
    }
  },

  /**
   * 快速登录（静默登录）
   */
  async onSilentLogin() {
    if (this.data.loading) return;

    try {
      this.setData({ loading: true });

      wx.showLoading({
        title: '登录中...',
        mask: true,
      });

      const result = await authService.silentLogin();
      console.log('快速登录成功:', result);

      wx.hideLoading();
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500,
      });

      // 延迟跳转
      setTimeout(() => {
        this.redirectToHome();
      }, 1500);

    } catch (error) {
      wx.hideLoading();
      console.error('快速登录失败:', error);

      let errorMessage = '登录失败，请重试';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      wx.showModal({
        title: '登录失败',
        content: errorMessage,
        showCancel: false,
        confirmText: '重试',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 微信授权登录
   * 必须由用户点击触发，直接在点击事件中调用getUserProfile
   */
  onWechatLogin() {
    if (this.data.loading) return;

    console.log('点击微信授权登录按钮');

    // 检查是否支持getUserProfile
    if (!wx.getUserProfile) {
      console.error('当前微信版本不支持getUserProfile');
      wx.showModal({
        title: '版本过低',
        content: '当前微信版本不支持获取用户信息，请升级微信版本或使用快速登录',
        cancelText: '快速登录',
        confirmText: '我知道了',
        success: (res) => {
          if (res.cancel) {
            this.onSilentLogin();
          }
        },
      });
      return;
    }

    console.log('开始调用getUserProfile');

    // 必须在用户点击事件的同步回调中调用getUserProfile
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (userProfileRes) => {
        console.log('获取用户信息成功:', userProfileRes);

        // 检查是否在模拟器中（模拟器返回的是默认信息）
        const isSimulator = userProfileRes.userInfo.nickName === '微信用户' &&
                           userProfileRes.userInfo.avatarUrl === '';

        if (isSimulator) {
          console.log('检测到模拟器环境，使用模拟数据');
          // 在模拟器中使用模拟的用户信息
          const mockUserInfo: WechatMiniprogram.UserInfo = {
            nickName: '测试用户',
            avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
            gender: 1 as 0 | 1 | 2,
            language: 'zh_CN',
            city: '深圳',
            province: '广东',
            country: '中国'
          };
          this.performWechatLogin(mockUserInfo);
        } else {
          // 真机环境，使用真实用户信息
          console.log('真机环境，使用真实用户信息');
          this.performWechatLogin(userProfileRes.userInfo);
        }
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error);

        wx.showModal({
          title: '授权失败',
          content: `获取用户信息失败: ${error.errMsg || '未知错误'}。您可以选择快速登录继续使用。`,
          cancelText: '快速登录',
          confirmText: '重新授权',
          success: (res) => {
            if (res.cancel) {
              // 用户选择快速登录
              this.onSilentLogin();
            }
            // 如果用户选择重新授权，不做任何操作，让用户再次点击按钮
          },
        });
      },
    });
  },

  /**
   * 执行微信登录（获取到用户信息后）
   */
  async performWechatLogin(userInfo: WechatMiniprogram.UserInfo) {
    if (this.data.loading) return;

    try {
      this.setData({ loading: true });

      wx.showLoading({
        title: '登录中...',
        mask: true,
      });

      // 获取微信登录code
      const loginResult = await new Promise<WechatMiniprogram.LoginSuccessCallbackResult>((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject,
        });
      });

      if (!loginResult.code) {
        throw new Error('获取微信登录code失败');
      }

      // 使用authService的新方法
      const result = await authService.wechatLoginWithUserInfo(userInfo);

      wx.hideLoading();
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500,
      });

      console.log('微信授权登录成功:', result);

      // 延迟跳转
      setTimeout(() => {
        this.redirectToHome();
      }, 1500);

    } catch (error) {
      wx.hideLoading();
      console.error('微信授权登录失败:', error);

      let errorMessage = '登录失败，请重试';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      wx.showModal({
        title: '登录失败',
        content: errorMessage,
        cancelText: '快速登录',
        confirmText: '重试',
        success: (res) => {
          if (res.cancel) {
            // 用户选择快速登录
            this.onSilentLogin();
          }
        },
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 测试getUserProfile是否工作
   */
  testGetUserProfile() {
    console.log('=== 开始测试getUserProfile ===');

    // 检查API是否存在
    if (!wx.getUserProfile) {
      console.error('wx.getUserProfile 不存在');
      wx.showToast({
        title: 'getUserProfile不支持',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    console.log('wx.getUserProfile 存在，开始调用...');

    wx.getUserProfile({
      desc: '测试获取用户信息',
      success: (res) => {
        console.log('=== getUserProfile 成功 ===', res);

        const isSimulator = res.userInfo.nickName === '微信用户' && res.userInfo.avatarUrl === '';
        const envText = isSimulator ? '模拟器环境' : '真机环境';

        wx.showModal({
          title: '测试成功',
          content: `环境：${envText}\n昵称：${res.userInfo.nickName}\n头像：${res.userInfo.avatarUrl ? '有' : '无'}`,
          showCancel: false,
        });
      },
      fail: (err) => {
        console.error('=== getUserProfile 失败 ===', err);
        wx.showModal({
          title: '测试失败',
          content: `错误信息：${err.errMsg}`,
          showCancel: false,
        });
      },
    });
  },

  /**
   * 跳转到首页
   */
  redirectToHome() {
    wx.switchTab({
      url: '/pages/index/index',
      fail: () => {
        // 如果不是tabBar页面，使用reLaunch
        wx.reLaunch({
          url: '/pages/home/<USER>',
        });
      },
    });
  },

  /**
   * 体验模式
   */
  onDemoMode() {
    wx.showModal({
      title: '体验模式',
      content: '体验模式下的数据不会保存，是否继续？',
      confirmText: '继续体验',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 设置体验模式标识
          wx.setStorageSync('demo_mode', true);
          
          // 创建模拟用户信息
          const demoUserInfo: WechatMiniprogram.UserInfo = {
            nickName: '体验用户',
            avatarUrl: '/assets/images/demo-avatar.png',
            gender: 0,
            city: '',
            province: '',
            country: '',
            language: 'zh_CN'
          };
          
          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', demoUserInfo);

          // 跳转到首页
          this.redirectToHome();
        }
      }
    });
  },

  /**
   * 显示隐私政策
   */
  onShowPrivacy() {
    this.setData({ showPrivacyModal: true });
  },

  /**
   * 关闭隐私政策
   */
  onClosePrivacyModal() {
    this.setData({ showPrivacyModal: false });
  },

  /**
   * 显示用户协议
   */
  onShowTerms() {
    this.setData({ showTermsModal: true });
  },

  /**
   * 关闭用户协议
   */
  onCloseTermsModal() {
    this.setData({ showTermsModal: false });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
});
