<!--登录页面-->
<view class="login-page">
  <!-- 背景装饰 -->
  <view class="background-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <!-- 主要内容 -->
  <view class="login-content">
    <!-- Logo和标题 -->
    <view class="logo-section">
      <view class="logo">
        <text class="logo-icon">💰</text>
      </view>
      <text class="app-name">记账本</text>
      <text class="app-slogan">让记账变得简单有趣</text>
    </view>

    <!-- 功能介绍 -->
    <view class="features-section">
      <view class="feature-item">
        <view class="feature-icon">📊</view>
        <view class="feature-text">
          <text class="feature-title">智能统计</text>
          <text class="feature-desc">多维度数据分析，让你的财务状况一目了然</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">🏷️</view>
        <view class="feature-text">
          <text class="feature-title">分类管理</text>
          <text class="feature-desc">自定义分类标签，个性化记账体验</text>
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-icon">☁️</view>
        <view class="feature-text">
          <text class="feature-title">云端同步</text>
          <text class="feature-desc">数据安全备份，多设备无缝同步</text>
        </view>
      </view>
    </view>

    <!-- 登录按钮 -->
    <view class="login-section">
      <!-- 微信授权登录 -->
      <button
        class="login-btn primary"
        bindtap="onWechatLogin"
        disabled="{{loading}}"
      >
        <view class="btn-content">
          <text class="btn-icon" wx:if="{{!loading}}">🔑</text>
          <view class="loading-spinner" wx:else></view>
          <text class="btn-text">{{loading ? '登录中...' : '微信授权登录'}}</text>
        </view>
      </button>

      <!-- 快速登录 -->
      <button
        class="login-btn secondary"
        bindtap="onSilentLogin"
        disabled="{{loading}}"
      >
        <view class="btn-content">
          <text class="btn-icon" wx:if="{{!loading}}">⚡</text>
          <view class="loading-spinner" wx:else></view>
          <text class="btn-text">{{loading ? '登录中...' : '快速登录'}}</text>
        </view>
      </button>

      <view class="login-tips">
        <text class="tips-text">登录即表示同意</text>
        <text class="tips-link" bindtap="onShowPrivacy">《隐私政策》</text>
        <text class="tips-text">和</text>
        <text class="tips-link" bindtap="onShowTerms">《用户协议》</text>
      </view>



      <view class="login-desc">
        <text class="desc-text">• 微信授权登录：获取头像昵称，体验更佳</text>
        <text class="desc-text">• 快速登录：无需授权，立即开始使用</text>
      </view>
    </view>

    <!-- 体验模式 -->
    <view class="demo-section">
      <view class="demo-divider">
        <view class="divider-line"></view>
        <text class="divider-text">或</text>
        <view class="divider-line"></view>
      </view>
      
      <view class="demo-btn" bindtap="onDemoMode">
        <text class="demo-icon">👀</text>
        <text class="demo-text">体验模式</text>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer-info">
    <text class="version-text">版本 v1.0.0</text>
    <text class="copyright-text">© 2023 记账本</text>
  </view>
</view>

<!-- 隐私政策弹窗 -->
<view class="policy-modal {{showPrivacyModal ? 'show' : ''}}" bindtap="onClosePrivacyModal">
  <view class="policy-content" catchtap="stopPropagation">
    <view class="policy-header">
      <text class="policy-title">隐私政策</text>
      <text class="close-btn" bindtap="onClosePrivacyModal">✕</text>
    </view>
    
    <scroll-view class="policy-body" scroll-y>
      <view class="policy-section">
        <text class="section-title">1. 信息收集</text>
        <text class="section-content">
          我们仅收集您主动提供的记账数据，包括交易金额、分类、备注等信息。我们不会收集您的敏感个人信息。
        </text>
      </view>
      
      <view class="policy-section">
        <text class="section-title">2. 信息使用</text>
        <text class="section-content">
          您的记账数据仅用于为您提供记账服务，包括数据统计、分析等功能。我们不会将您的数据用于其他商业目的。
        </text>
      </view>
      
      <view class="policy-section">
        <text class="section-title">3. 信息保护</text>
        <text class="section-content">
          我们采用行业标准的安全措施保护您的数据安全，包括数据加密、访问控制等技术手段。
        </text>
      </view>
      
      <view class="policy-section">
        <text class="section-title">4. 信息共享</text>
        <text class="section-content">
          我们不会向第三方分享、出售或披露您的个人信息，除非获得您的明确同意或法律要求。
        </text>
      </view>
    </scroll-view>
    
    <view class="policy-footer">
      <view class="btn btn-primary" bindtap="onClosePrivacyModal">
        我已了解
      </view>
    </view>
  </view>
</view>

<!-- 用户协议弹窗 -->
<view class="policy-modal {{showTermsModal ? 'show' : ''}}" bindtap="onCloseTermsModal">
  <view class="policy-content" catchtap="stopPropagation">
    <view class="policy-header">
      <text class="policy-title">用户协议</text>
      <text class="close-btn" bindtap="onCloseTermsModal">✕</text>
    </view>
    
    <scroll-view class="policy-body" scroll-y>
      <view class="policy-section">
        <text class="section-title">1. 服务说明</text>
        <text class="section-content">
          记账本是一款个人财务管理工具，帮助用户记录和分析个人收支情况。
        </text>
      </view>
      
      <view class="policy-section">
        <text class="section-title">2. 用户责任</text>
        <text class="section-content">
          用户应确保提供的记账数据真实准确，不得利用本应用进行违法违规活动。
        </text>
      </view>
      
      <view class="policy-section">
        <text class="section-title">3. 服务变更</text>
        <text class="section-content">
          我们保留随时修改或终止服务的权利，但会提前通知用户重要变更。
        </text>
      </view>
      
      <view class="policy-section">
        <text class="section-title">4. 免责声明</text>
        <text class="section-content">
          本应用仅作为记账工具使用，不构成任何投资建议。用户应自行承担使用风险。
        </text>
      </view>
    </scroll-view>
    
    <view class="policy-footer">
      <view class="btn btn-primary" bindtap="onCloseTermsModal">
        我已了解
      </view>
    </view>
  </view>
</view>
