/* 登录页面样式 */
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0 48rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 主要内容 */
.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 120rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.logo-icon {
  font-size: 80rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.app-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
}

/* 功能介绍 */
.features-section {
  width: 100%;
  margin-bottom: 80rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 48rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}

.feature-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 登录区域 */
.login-section {
  width: 100%;
  margin-bottom: 48rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
}

.login-btn.primary {
  background: white;
  color: #333;
}

.login-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.login-btn.test {
  background: rgba(255, 165, 0, 0.8);
  color: white;
  border: 2rpx solid rgba(255, 165, 0, 0.5);
}

.login-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.login-btn[disabled] {
  opacity: 0.8;
  transform: none !important;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--primary-color);
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-tips {
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.tips-text {
  color: rgba(255, 255, 255, 0.8);
}

.tips-link {
  color: white;
  text-decoration: underline;
  margin: 0 4rpx;
}

.login-desc {
  margin-top: 24rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.desc-text {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.desc-text:last-child {
  margin-bottom: 0;
}

/* 体验模式 */
.demo-section {
  width: 100%;
}

.demo-divider {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  margin: 0 24rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.demo-btn {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.demo-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.98);
}

.demo-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.demo-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  padding: 32rpx 0;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
}

.version-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.copyright-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 政策弹窗 */
.policy-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 48rpx;
  box-sizing: border-box;
}

.policy-modal.show {
  opacity: 1;
  visibility: visible;
}

.policy-content {
  width: 100%;
  max-width: 640rpx;
  max-height: 80vh;
  background-color: white;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.policy-modal.show .policy-content {
  transform: scale(1);
}

.policy-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.policy-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: var(--text-color-secondary);
}

.policy-body {
  flex: 1;
  padding: 32rpx;
  max-height: 60vh;
}

.policy-section {
  margin-bottom: 32rpx;
}

.policy-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16rpx;
}

.section-content {
  font-size: 26rpx;
  color: var(--text-color-secondary);
  line-height: 1.6;
}

.policy-footer {
  padding: 32rpx;
  border-top: 1rpx solid var(--border-color);
}

.policy-footer .btn {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  background-color: var(--primary-color);
  color: white;
  border: none;
}
