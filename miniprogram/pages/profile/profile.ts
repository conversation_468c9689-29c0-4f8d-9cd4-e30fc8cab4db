// 个人中心页面逻辑
import { formatAmount, formatDate, getCurrentMonth } from '../../utils/helpers';
import authService from '../../services/auth.service';
import statisticsService from '../../services/statistics.service';

// 货币选项接口
interface CurrencyOption {
  code: string;
  name: string;
  symbol: string;
}

// 页面数据接口
interface PageData {
  userInfo: WechatMiniprogram.UserInfo | null;
  userId: string;
  memberInfo: {
    isVip: boolean;
    expireTime?: string;
  };
  userStats: {
    totalDays: number;
    totalRecords: number;
    totalCategories: number;
  };
  
  // 设置相关
  currentTheme: string;
  currentCurrency: string;
  reminderEnabled: boolean;
  lastBackupTime: number;
  appVersion: string;
  
  // 弹窗相关
  showThemeModal: boolean;
  showCurrencyModal: boolean;
  currencyOptions: CurrencyOption[];
  
  // 状态
  loading: boolean;
  loadingText: string;
}

Page<PageData>({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    userId: '',
    memberInfo: {
      isVip: false
    },
    userStats: {
      totalDays: 0,
      totalRecords: 0,
      totalCategories: 0
    },
    
    // 设置相关
    currentTheme: 'light',
    currentCurrency: 'CNY',
    reminderEnabled: false,
    lastBackupTime: 0,
    appVersion: '1.0.0',
    
    // 弹窗相关
    showThemeModal: false,
    showCurrencyModal: false,
    currencyOptions: [
      { code: 'CNY', name: '人民币', symbol: '¥' },
      { code: 'USD', name: '美元', symbol: '$' },
      { code: 'EUR', name: '欧元', symbol: '€' },
      { code: 'JPY', name: '日元', symbol: '¥' },
      { code: 'GBP', name: '英镑', symbol: '£' },
      { code: 'HKD', name: '港币', symbol: 'HK$' },
      { code: 'TWD', name: '台币', symbol: 'NT$' }
    ],
    
    // 状态
    loading: false,
    loadingText: '加载中...'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    console.log('个人中心页面加载');
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('个人中心页面显示');
    this.loadUserData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.loadUserData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 获取用户信息
      this.getUserInfo();
      
      // 加载设置
      this.loadSettings();
      
      // 设置当前月份
      this.setCurrentMonth();
      
      // 加载用户数据
      await this.loadUserData();
    } catch (error) {
      console.error('初始化页面失败:', error);
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    const userInfo = authService.getUserInfo();
    const userId = authService.getUserId() || 'U' + Date.now().toString().slice(-8);
    
    this.setData({
      userInfo,
      userId
    });
  },

  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const settings = wx.getStorageSync('app_settings') || {};
      
      this.setData({
        currentTheme: settings.theme || 'light',
        currentCurrency: settings.currency || 'CNY',
        reminderEnabled: settings.reminderEnabled || false,
        lastBackupTime: settings.lastBackupTime || 0
      });
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  },

  /**
   * 设置当前月份
   */
  setCurrentMonth() {
    const current = getCurrentMonth();
    this.setData({
      currentMonth: `${current.year}年${current.month}月`
    });
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    try {
      this.setData({ loading: true, loadingText: '加载中...' });
      
      // 加载用户统计数据
      await this.loadUserStats();
      
    } catch (error) {
      console.error('加载用户数据失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载用户统计
   */
  async loadUserStats() {
    try {
      console.log('加载用户统计数据');
      const stats = await statisticsService.getUserStatistics();
      console.log('用户统计数据:', stats);

      const userStats = {
        totalDays: stats.totalDays,
        totalRecords: stats.totalRecords,
        totalCategories: stats.totalCategories
      };

      this.setData({ userStats });
    } catch (error) {
      console.error('加载用户统计失败:', error);
      // 使用默认值
      this.setData({
        userStats: {
          totalDays: 0,
          totalRecords: 0,
          totalCategories: 0
        }
      });
    }
  },



  /**
   * 更换头像
   */
  onChangeAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 这里应该上传头像到服务器
        console.log('选择头像:', res.tempFilePaths[0]);
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 数据备份
   */
  async onDataBackup() {
    try {
      this.setData({ loading: true, loadingText: '备份中...' });
      
      // 模拟备份过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const now = Date.now();
      this.setData({ lastBackupTime: now });
      
      // 保存备份时间
      const settings = wx.getStorageSync('app_settings') || {};
      settings.lastBackupTime = now;
      wx.setStorageSync('app_settings', settings);
      
      wx.showToast({
        title: '备份成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('数据备份失败:', error);
      wx.showToast({
        title: '备份失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 数据恢复
   */
  onDataRestore() {
    wx.showModal({
      title: '数据恢复',
      content: '恢复数据将覆盖当前所有数据，确定要继续吗？',
      confirmText: '恢复',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 数据导出
   */
  onDataExport() {
    wx.showActionSheet({
      itemList: ['导出Excel', '导出CSV', '导出PDF'],
      success: (res) => {
        const types = ['Excel', 'CSV', 'PDF'];
        wx.showToast({
          title: `导出${types[res.tapIndex]}功能开发中`,
          icon: 'none'
        });
      }
    });
  },

  /**
   * 主题设置
   */
  onThemeSettings() {
    this.setData({ showThemeModal: true });
  },

  /**
   * 关闭主题弹窗
   */
  onCloseThemeModal() {
    this.setData({ showThemeModal: false });
  },

  /**
   * 选择主题
   */
  onSelectTheme(e: WechatMiniprogram.TouchEvent) {
    const theme = e.currentTarget.dataset.theme as string;
    
    this.setData({ 
      currentTheme: theme,
      showThemeModal: false 
    });
    
    // 保存设置
    this.saveSettings({ theme });
    
    wx.showToast({
      title: `已切换到${theme === 'light' ? '浅色' : '深色'}模式`,
      icon: 'success'
    });
  },

  /**
   * 货币设置
   */
  onCurrencySettings() {
    this.setData({ showCurrencyModal: true });
  },

  /**
   * 关闭货币弹窗
   */
  onCloseCurrencyModal() {
    this.setData({ showCurrencyModal: false });
  },

  /**
   * 选择货币
   */
  onSelectCurrency(e: WechatMiniprogram.TouchEvent) {
    const currency = e.currentTarget.dataset.currency as CurrencyOption;
    
    this.setData({ 
      currentCurrency: currency.code,
      showCurrencyModal: false 
    });
    
    // 保存设置
    this.saveSettings({ currency: currency.code });
    
    wx.showToast({
      title: `已切换到${currency.name}`,
      icon: 'success'
    });
  },

  /**
   * 提醒设置
   */
  onReminderSettings() {
    wx.navigateTo({
      url: '/pages/reminder-settings/reminder-settings'
    });
  },

  /**
   * 提醒开关切换
   */
  onReminderToggle(e: WechatMiniprogram.SwitchChange) {
    const enabled = e.detail.value;
    
    this.setData({ reminderEnabled: enabled });
    
    // 保存设置
    this.saveSettings({ reminderEnabled: enabled });
    
    if (enabled) {
      // 请求通知权限
      wx.requestSubscribeMessage({
        tmplIds: ['template_id'], // 替换为实际的模板ID
        success: (res) => {
          console.log('订阅消息结果:', res);
        }
      });
    }
  },

  /**
   * 使用指南
   */
  onUserGuide() {
    wx.navigateTo({
      url: '/pages/user-guide/user-guide'
    });
  },

  /**
   * 意见反馈
   */
  onFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  /**
   * 关于我们
   */
  onAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  /**
   * 退出登录
   */
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '退出登录后需要重新登录才能使用，确定要退出吗？',
      confirmText: '退出',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          authService.logout();
          
          // 跳转到登录页
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
  },

  /**
   * 保存设置
   */
  saveSettings(updates: Record<string, any>) {
    try {
      const settings = wx.getStorageSync('app_settings') || {};
      Object.assign(settings, updates);
      wx.setStorageSync('app_settings', settings);
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  },

  /**
   * 格式化金额
   */
  formatAmount(amount: number, showSymbol: boolean = true): string {
    return formatAmount(amount, showSymbol);
  },

  /**
   * 格式化备份时间
   */
  formatBackupTime(timestamp: number): string {
    if (!timestamp) return '从未备份';
    
    const now = Date.now();
    const diff = now - timestamp;
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    
    if (days === 0) {
      return '今天';
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return formatDate(new Date(timestamp), 'MM-DD');
    }
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
});
