<!--个人中心页面-->
<view class="profile-page">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image 
        class="avatar" 
        src="{{userInfo.avatarUrl || '/assets/images/default-avatar.png'}}"
        mode="aspectFill"
        bindtap="onChangeAvatar"
      />
      <view class="user-details">
        <text class="username">{{userInfo.nickName || '记账用户'}}</text>
        <text class="user-id">ID: {{userId}}</text>
        <view class="member-badge" wx:if="{{memberInfo.isVip}}">
          <text class="badge-icon">👑</text>
          <text class="badge-text">VIP会员</text>
        </view>
      </view>
    </view>
    
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-value">{{userStats.totalDays}}</text>
        <text class="stat-label">记账天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{userStats.totalRecords}}</text>
        <text class="stat-label">记录条数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{userStats.totalCategories}}</text>
        <text class="stat-label">使用分类</text>
      </view>
    </view>
  </view>



  <!-- 功能菜单 -->
  <view class="menu-section">
    <!-- 数据管理 -->
    <view class="menu-group">
      <view class="group-title">数据管理</view>
      <view class="menu-list">
        <view class="menu-item" bindtap="onDataBackup">
          <view class="menu-icon backup">☁️</view>
          <view class="menu-content">
            <text class="menu-title">数据备份</text>
            <text class="menu-desc">备份数据到云端</text>
          </view>
          <view class="menu-extra">
            <text class="last-backup" wx:if="{{lastBackupTime}}">
              {{formatBackupTime(lastBackupTime)}}
            </text>
            <text class="menu-arrow">›</text>
          </view>
        </view>
        
        <view class="menu-item" bindtap="onDataRestore">
          <view class="menu-icon restore">📥</view>
          <view class="menu-content">
            <text class="menu-title">数据恢复</text>
            <text class="menu-desc">从云端恢复数据</text>
          </view>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" bindtap="onDataExport">
          <view class="menu-icon export">📊</view>
          <view class="menu-content">
            <text class="menu-title">数据导出</text>
            <text class="menu-desc">导出Excel报表</text>
          </view>
          <text class="menu-arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 应用设置 -->
    <view class="menu-group">
      <view class="group-title">应用设置</view>
      <view class="menu-list">
        <view class="menu-item" bindtap="onThemeSettings">
          <view class="menu-icon theme">🎨</view>
          <view class="menu-content">
            <text class="menu-title">主题设置</text>
            <text class="menu-desc">{{currentTheme === 'light' ? '浅色模式' : '深色模式'}}</text>
          </view>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" bindtap="onCurrencySettings">
          <view class="menu-icon currency">💰</view>
          <view class="menu-content">
            <text class="menu-title">货币设置</text>
            <text class="menu-desc">{{currentCurrency}}</text>
          </view>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" bindtap="onReminderSettings">
          <view class="menu-icon reminder">⏰</view>
          <view class="menu-content">
            <text class="menu-title">记账提醒</text>
            <text class="menu-desc">{{reminderEnabled ? '已开启' : '已关闭'}}</text>
          </view>
          <switch 
            class="menu-switch" 
            checked="{{reminderEnabled}}"
            bindchange="onReminderToggle"
          />
        </view>
      </view>
    </view>

    <!-- 帮助与反馈 -->
    <view class="menu-group">
      <view class="group-title">帮助与反馈</view>
      <view class="menu-list">
        <view class="menu-item" bindtap="onUserGuide">
          <view class="menu-icon guide">📖</view>
          <view class="menu-content">
            <text class="menu-title">使用指南</text>
            <text class="menu-desc">了解如何使用记账本</text>
          </view>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" bindtap="onFeedback">
          <view class="menu-icon feedback">💬</view>
          <view class="menu-content">
            <text class="menu-title">意见反馈</text>
            <text class="menu-desc">告诉我们您的建议</text>
          </view>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" bindtap="onAbout">
          <view class="menu-icon about">ℹ️</view>
          <view class="menu-content">
            <text class="menu-title">关于我们</text>
            <text class="menu-desc">版本 v{{appVersion}}</text>
          </view>
          <text class="menu-arrow">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <view class="logout-btn" bindtap="onLogout">
      <text class="logout-text">退出登录</text>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 主题选择弹窗 -->
<view class="theme-modal {{showThemeModal ? 'show' : ''}}" bindtap="onCloseThemeModal">
  <view class="theme-content" catchtap="stopPropagation">
    <view class="theme-header">
      <text class="theme-title">选择主题</text>
      <text class="close-btn" bindtap="onCloseThemeModal">✕</text>
    </view>
    
    <view class="theme-options">
      <view 
        class="theme-option {{currentTheme === 'light' ? 'selected' : ''}}"
        bindtap="onSelectTheme"
        data-theme="light"
      >
        <view class="theme-preview light-preview">
          <view class="preview-header"></view>
          <view class="preview-content"></view>
        </view>
        <text class="theme-name">浅色模式</text>
        <text class="theme-check" wx:if="{{currentTheme === 'light'}}">✓</text>
      </view>
      
      <view 
        class="theme-option {{currentTheme === 'dark' ? 'selected' : ''}}"
        bindtap="onSelectTheme"
        data-theme="dark"
      >
        <view class="theme-preview dark-preview">
          <view class="preview-header"></view>
          <view class="preview-content"></view>
        </view>
        <text class="theme-name">深色模式</text>
        <text class="theme-check" wx:if="{{currentTheme === 'dark'}}">✓</text>
      </view>
    </view>
  </view>
</view>

<!-- 货币选择弹窗 -->
<view class="currency-modal {{showCurrencyModal ? 'show' : ''}}" bindtap="onCloseCurrencyModal">
  <view class="currency-content" catchtap="stopPropagation">
    <view class="currency-header">
      <text class="currency-title">选择货币</text>
      <text class="close-btn" bindtap="onCloseCurrencyModal">✕</text>
    </view>
    
    <view class="currency-options">
      <view 
        class="currency-option {{currentCurrency === item.code ? 'selected' : ''}}"
        wx:for="{{currencyOptions}}" 
        wx:key="code"
        bindtap="onSelectCurrency"
        data-currency="{{item}}"
      >
        <text class="currency-symbol">{{item.symbol}}</text>
        <view class="currency-info">
          <text class="currency-name">{{item.name}}</text>
          <text class="currency-code">{{item.code}}</text>
        </view>
        <text class="currency-check" wx:if="{{currentCurrency === item.code}}">✓</text>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>
