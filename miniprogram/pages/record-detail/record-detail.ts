// 记录详情页面逻辑
import { formatAmount, formatDate, getRelativeTime, getCurrentMonth } from '../../utils/helpers';
import transactionService, { Transaction } from '../../services/transaction.service';
import statisticsService from '../../services/statistics.service';

// 页面数据接口
interface PageData {
  record: Transaction | null;
  categoryMonthlyStats: {
    count: number;
    amount: number;
  };
  monthlyStats: {
    count: number;
    amount: number;
  };
  categoryBudget: number;
  budgetUsage: number;
  similarRecords: Transaction[];
  showShareModal: boolean;
  loading: boolean;
  loadingText: string;
}

Page<PageData>({
  /**
   * 页面的初始数据
   */
  data: {
    record: null,
    categoryMonthlyStats: {
      count: 0,
      amount: 0
    },
    monthlyStats: {
      count: 0,
      amount: 0
    },
    categoryBudget: 0,
    budgetUsage: 0,
    similarRecords: [],
    showShareModal: false,
    loading: false,
    loadingText: '加载中...'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: Record<string, string | undefined>) {
    console.log('记录详情页面加载', options);
    
    if (options.id) {
      this.loadRecordDetail(options.id);
    } else {
      wx.showToast({
        title: '记录ID不存在',
        icon: 'none'
      });
      wx.navigateBack();
    }
  },

  /**
   * 加载记录详情
   */
  async loadRecordDetail(id: string) {
    try {
      this.setData({ loading: true, loadingText: '加载中...' });
      
      const record = await transactionService.getTransaction(id);
      this.setData({ record });
      
      // 并行加载相关数据
      await Promise.all([
        this.loadCategoryStats(record),
        this.loadMonthlyStats(record),
        this.loadSimilarRecords(record)
      ]);
      
    } catch (error) {
      console.error('加载记录详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      wx.navigateBack();
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载分类统计
   */
  async loadCategoryStats(record: Transaction) {
    try {
      // 获取当前月份的开始和结束日期
      const { start, end } = getCurrentMonth();

      // 获取该分类的月度统计
      const categoryStats = await statisticsService.getCategoryStatistics(start, end, record.type);
      const currentCategoryStats = categoryStats.find(stat => stat.categoryId === record.categoryId);

      const categoryMonthlyStats = {
        count: currentCategoryStats?.transactionCount || 0,
        amount: currentCategoryStats?.amount || 0
      };

      // 预算相关（暂时使用固定值，后续可以从用户设置中获取）
      const categoryBudget = currentCategoryStats?.budget || 2000;
      const budgetUsage = categoryBudget > 0 ?
        Number(((categoryMonthlyStats.amount / categoryBudget) * 100).toFixed(1)) : 0;

      this.setData({
        categoryMonthlyStats,
        categoryBudget,
        budgetUsage
      });
    } catch (error) {
      console.error('加载分类统计失败:', error);
      // 使用默认值
      this.setData({
        categoryMonthlyStats: { count: 0, amount: 0 },
        categoryBudget: 0,
        budgetUsage: 0
      });
    }
  },

  /**
   * 加载月度统计
   */
  async loadMonthlyStats(record: Transaction) {
    try {
      // 获取当前月份的开始和结束日期
      const { start, end } = getCurrentMonth();

      // 获取月度概览统计
      const overview = await statisticsService.getOverview(start, end);

      const monthlyStats = {
        count: record.type === 'income' ?
          Math.round(overview.transactionCount * 0.3) : // 假设收入占30%
          Math.round(overview.transactionCount * 0.7),  // 支出占70%
        amount: record.type === 'income' ? overview.totalIncome : overview.totalExpense
      };

      this.setData({ monthlyStats });
    } catch (error) {
      console.error('加载月度统计失败:', error);
    }
  },

  /**
   * 加载相似记录
   */
  async loadSimilarRecords(record: Transaction) {
    try {
      // 这里应该调用API获取相似记录
      // 暂时使用模拟数据
      const similarRecords: Transaction[] = [];
      
      this.setData({ similarRecords });
    } catch (error) {
      console.error('加载相似记录失败:', error);
    }
  },

  /**
   * 格式化金额
   */
  formatAmount(amount: number, showSymbol: boolean = true): string {
    return formatAmount(amount, showSymbol);
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(date: string | number, time?: number): string {
    if (typeof date === 'string' && time) {
      return `${formatDate(new Date(date), 'YYYY-MM-DD')} ${formatDate(new Date(time), 'HH:mm')}`;
    } else {
      return formatDate(new Date(date), 'YYYY-MM-DD HH:mm');
    }
  },

  /**
   * 格式化相对日期
   */
  formatRelativeDate(date: string): string {
    return getRelativeTime(date);
  },

  /**
   * 预览图片
   */
  onPreviewImage() {
    const { record } = this.data;
    if (record?.imageUrl) {
      wx.previewImage({
        urls: [record.imageUrl],
        current: record.imageUrl
      });
    }
  },

  /**
   * 图片加载成功
   */
  onImageLoad() {
    console.log('图片加载成功');
  },

  /**
   * 图片加载失败
   */
  onImageError() {
    console.log('图片加载失败');
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
  },

  /**
   * 保存图片
   */
  onSaveImage() {
    const { record } = this.data;
    if (!record?.imageUrl) return;
    
    wx.downloadFile({
      url: record.imageUrl,
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: () => {
            wx.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 分享图片
   */
  onShareImage() {
    this.setData({ showShareModal: true });
  },

  /**
   * 关闭分享弹窗
   */
  onCloseShareModal() {
    this.setData({ showShareModal: false });
  },

  /**
   * 分享给朋友
   */
  onShareToFriend() {
    this.setData({ showShareModal: false });
    // 这里实现分享给朋友的逻辑
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 分享到朋友圈
   */
  onShareToMoments() {
    this.setData({ showShareModal: false });
    // 这里实现分享到朋友圈的逻辑
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 生成图片
   */
  onGenerateImage() {
    this.setData({ showShareModal: false });
    // 这里实现生成分享图片的逻辑
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 编辑记录
   */
  onEdit() {
    const { record } = this.data;
    if (record) {
      wx.navigateTo({
        url: `/pages/add-record/add-record?id=${record.id}`
      });
    }
  },

  /**
   * 复制记录
   */
  onCopy() {
    const { record } = this.data;
    if (record) {
      wx.navigateTo({
        url: `/pages/add-record/add-record?type=${record.type}&categoryId=${record.categoryId}&amount=${record.amount}&description=${record.description}`
      });
    }
  },

  /**
   * 删除记录
   */
  onDelete() {
    const { record } = this.data;
    if (!record) return;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: async (res) => {
        if (res.confirm) {
          try {
            this.setData({ loading: true, loadingText: '删除中...' });
            
            await transactionService.deleteTransaction(record.id);
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
            // 返回上一页
            wx.navigateBack();
            
          } catch (error) {
            console.error('删除记录失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          } finally {
            this.setData({ loading: false });
          }
        }
      }
    });
  },

  /**
   * 查看更多相似记录
   */
  onViewMoreSimilar() {
    const { record } = this.data;
    if (record) {
      wx.navigateTo({
        url: `/pages/records/records?categoryId=${record.categoryId}`
      });
    }
  },

  /**
   * 点击相似记录
   */
  onSimilarRecord(e: WechatMiniprogram.TouchEvent) {
    const record = e.currentTarget.dataset.record as Transaction;
    wx.redirectTo({
      url: `/pages/record-detail/record-detail?id=${record.id}`
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
});
