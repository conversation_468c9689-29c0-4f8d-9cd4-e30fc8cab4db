<!--记录详情页面-->
<wxs module="utils">
  function formatAmount(amount) {
    if (amount === null || amount === undefined) return '0.00';
    return '¥' + Number(amount).toFixed(2);
  }

  function formatDateTime(date, time) {
    if (!date) return '';
    if (time) {
      // 如果有时间戳，使用时间戳
      var dateObj = getDate(time);
      return dateObj.getFullYear() + '-' +
             (dateObj.getMonth() + 1).toString().padStart(2, '0') + '-' +
             dateObj.getDate().toString().padStart(2, '0') + ' ' +
             dateObj.getHours().toString().padStart(2, '0') + ':' +
             dateObj.getMinutes().toString().padStart(2, '0');
    } else {
      // 只有日期
      return date;
    }
  }

  module.exports = {
    formatAmount: formatAmount,
    formatDateTime: formatDateTime
  };
</wxs>

<view class="record-detail-page">
  <!-- 记录信息卡片 -->
  <view class="record-card card">
    <view class="record-header">
      <view class="record-icon" style="background-color: {{record.categoryColor}};">
        <text class="icon">{{record.categoryIcon}}</text>
      </view>
      <view class="record-info">
        <text class="category-name">{{record.categoryName}}</text>
        <text class="record-type">{{record.type === 'income' ? '收入' : '支出'}}</text>
      </view>
      <view class="record-amount">
        <text class="amount {{record.type}}">
          {{record.type === 'expense' ? '-' : '+'}}{{utils.formatAmount(record.amount)}}
        </text>
      </view>
    </view>
    
    <view class="record-meta">
      <view class="meta-item">
        <text class="meta-label">记录时间</text>
        <text class="meta-value">{{utils.formatDateTime(record.date, record.createTime)}}</text>
      </view>

      <view class="meta-item" wx:if="{{record.description}}">
        <text class="meta-label">备注信息</text>
        <text class="meta-value">{{record.description}}</text>
      </view>

      <view class="meta-item">
        <text class="meta-label">创建时间</text>
        <text class="meta-value">{{utils.formatDateTime(record.createTime)}}</text>
      </view>

      <view class="meta-item" wx:if="{{record.updateTime && record.updateTime !== record.createTime}}">
        <text class="meta-label">修改时间</text>
        <text class="meta-value">{{utils.formatDateTime(record.updateTime)}}</text>
      </view>
    </view>
  </view>

  <!-- 凭证图片 -->
  <view class="receipt-section card" wx:if="{{record.imageUrl}}">
    <view class="section-header">
      <text class="section-title">凭证图片</text>
      <view class="image-actions">
        <view class="action-btn" bindtap="onSaveImage">
          <text class="icon">💾</text>
          <text>保存</text>
        </view>
        <view class="action-btn" bindtap="onShareImage">
          <text class="icon">📤</text>
          <text>分享</text>
        </view>
      </view>
    </view>
    
    <view class="receipt-image-container">
      <image 
        class="receipt-image" 
        src="{{record.imageUrl}}" 
        mode="aspectFit"
        bindtap="onPreviewImage"
        bindload="onImageLoad"
        binderror="onImageError"
      />
    </view>
  </view>

  <!-- 相关统计 -->
  <view class="stats-section card">
    <view class="section-header">
      <text class="section-title">相关统计</text>
    </view>
    
    <view class="stats-content">
      <view class="stat-item">
        <text class="stat-label">本月该分类</text>
        <text class="stat-value">{{categoryMonthlyStats.count}}笔</text>
        <text class="stat-amount">{{utils.formatAmount(categoryMonthlyStats.amount)}}</text>
      </view>

      <view class="stat-item">
        <text class="stat-label">本月总{{record.type === 'income' ? '收入' : '支出'}}</text>
        <text class="stat-value">{{monthlyStats.count}}笔</text>
        <text class="stat-amount">{{utils.formatAmount(monthlyStats.amount)}}</text>
      </view>

      <view class="stat-item" wx:if="{{categoryBudget}}">
        <text class="stat-label">分类预算</text>
        <text class="stat-value">{{budgetUsage}}%</text>
        <text class="stat-amount">{{utils.formatAmount(categoryBudget)}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-buttons">
      <view class="action-btn-large edit" bindtap="onEdit">
        <text class="btn-icon">✏️</text>
        <text class="btn-text">编辑</text>
      </view>
      
      <view class="action-btn-large copy" bindtap="onCopy">
        <text class="btn-icon">📋</text>
        <text class="btn-text">复制</text>
      </view>
      
      <view class="action-btn-large delete" bindtap="onDelete">
        <text class="btn-icon">🗑️</text>
        <text class="btn-text">删除</text>
      </view>
    </view>
  </view>

  <!-- 相似记录 -->
  <view class="similar-records card" wx:if="{{similarRecords.length > 0}}">
    <view class="section-header">
      <text class="section-title">相似记录</text>
      <text class="more-btn" bindtap="onViewMoreSimilar">查看更多</text>
    </view>
    
    <view class="similar-list">
      <view 
        class="similar-item"
        wx:for="{{similarRecords}}" 
        wx:key="id"
        bindtap="onSimilarRecord"
        data-record="{{item}}"
      >
        <view class="similar-info">
          <text class="similar-amount {{item.type}}">
            {{item.type === 'expense' ? '-' : '+'}}{{formatAmount(item.amount, false)}}
          </text>
          <text class="similar-desc">{{item.description || '无备注'}}</text>
        </view>
        <text class="similar-date">{{formatRelativeDate(item.date)}}</text>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 分享弹窗 -->
<view class="share-modal {{showShareModal ? 'show' : ''}}" bindtap="onCloseShareModal">
  <view class="share-content" catchtap="stopPropagation">
    <view class="share-header">
      <text class="share-title">分享记录</text>
      <text class="close-btn" bindtap="onCloseShareModal">✕</text>
    </view>
    
    <view class="share-options">
      <view class="share-option" bindtap="onShareToFriend">
        <view class="share-icon friend">👥</view>
        <text class="share-text">分享给朋友</text>
      </view>
      
      <view class="share-option" bindtap="onShareToMoments">
        <view class="share-icon moments">🌟</view>
        <text class="share-text">分享到朋友圈</text>
      </view>
      
      <view class="share-option" bindtap="onGenerateImage">
        <view class="share-icon image">🖼️</view>
        <text class="share-text">生成图片</text>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>
