// 账单列表页面逻辑
import { formatAmount, formatDate, getRelativeTime, getCurrentMonth } from '../../utils/helpers';
import transactionService, { Transaction, TransactionQuery } from '../../services/transaction.service';
import categoryService, { Category } from '../../services/category.service';
import authService from '../../services/auth.service';

// 分组后的交易记录
interface GroupedRecord {
  date: string;
  dayIncome: number;
  dayExpense: number;
  records: Transaction[];
}

// 筛选选项
interface FilterOption {
  label: string;
  value: string;
  selected: boolean;
}

// 页面数据接口
interface PageData {
  groupedRecords: GroupedRecord[];
  summary: {
    totalIncome: number;
    totalExpense: number;
    balance: number;
  };
  showSummary: boolean;
  
  // 筛选相关
  typeFilter: string;
  dateFilter: string;
  categoryFilter: string;
  typeFilterText: string;
  dateFilterText: string;
  categoryFilterText: string;
  
  // 弹窗相关
  showFilterModal: boolean;
  showActionModal: boolean;
  currentFilterType: string;
  currentFilterTitle: string;
  currentFilterOptions: FilterOption[];
  selectedRecord: Transaction | null;
  
  // 分页相关
  currentPage: number;
  pageSize: number;
  hasMore: boolean;
  loading: boolean;
  loadingText: string;
  
  // 其他
  emptyText: string;
  categories: Category[];
}

Page<PageData>({
  /**
   * 页面的初始数据
   */
  data: {
    groupedRecords: [],
    summary: {
      totalIncome: 0,
      totalExpense: 0,
      balance: 0
    },
    showSummary: true,
    
    // 筛选相关
    typeFilter: 'all',
    dateFilter: 'month',
    categoryFilter: 'all',
    typeFilterText: '全部',
    dateFilterText: '本月',
    categoryFilterText: '全部',
    
    // 弹窗相关
    showFilterModal: false,
    showActionModal: false,
    currentFilterType: '',
    currentFilterTitle: '',
    currentFilterOptions: [],
    selectedRecord: null,
    
    // 分页相关
    currentPage: 1,
    pageSize: 20,
    hasMore: true,
    loading: false,
    loadingText: '加载中...',
    
    // 其他
    emptyText: '暂无交易记录',
    categories: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    console.log('账单列表页面加载');
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('账单列表页面显示');
    this.refreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉加载更多');
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData();
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 检查登录状态
      if (!authService.isLoggedIn()) {
        console.log('用户未登录，跳转到登录页');
        wx.redirectTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 加载分类数据
      await this.loadCategories();

      // 加载交易记录
      await this.loadData();
    } catch (error) {
      console.error('初始化页面失败:', error);
      this.showError('页面加载失败，请重试');
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      groupedRecords: []
    });
    
    await this.loadData();
  },

  /**
   * 加载数据
   */
  async loadData() {
    try {
      this.setData({ loading: true, loadingText: '加载中...' });

      // 先加载交易记录，再计算统计
      await this.loadTransactions();
      await this.loadSummary();

    } catch (error) {
      console.error('加载数据失败:', error);
      this.showError('数据加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载更多数据
   */
  async loadMoreData() {
    try {
      this.setData({ loading: true, loadingText: '加载更多...' });
      
      const nextPage = this.data.currentPage + 1;
      const query = this.buildQuery();
      query.page = nextPage;
      
      const response = await transactionService.getTransactions(query);
      
      if (response.list.length > 0) {
        // 合并新数据
        const newGroupedRecords = this.groupTransactionsByDate(response.list);
        const mergedRecords = this.mergeGroupedRecords(this.data.groupedRecords, newGroupedRecords);
        
        this.setData({
          groupedRecords: mergedRecords,
          currentPage: nextPage,
          hasMore: response.list.length === this.data.pageSize
        });
      } else {
        this.setData({ hasMore: false });
      }
      
    } catch (error) {
      console.error('加载更多数据失败:', error);
      this.showError('加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载分类数据
   */
  async loadCategories() {
    try {
      const categories = await categoryService.getCategories();
      this.setData({ categories });
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  },

  /**
   * 加载交易记录
   */
  async loadTransactions() {
    const query = this.buildQuery();
    console.log('查询参数:', query);

    const response = await transactionService.getTransactions(query);
    console.log('服务端响应:', response);

    const groupedRecords = this.groupTransactionsByDate(response.list);
    console.log('分组后的记录:', groupedRecords);

    this.setData({
      groupedRecords,
      hasMore: response.list.length === this.data.pageSize,
      emptyText: this.getEmptyText()
    });
  },

  /**
   * 加载统计概览
   */
  async loadSummary() {
    // 根据当前筛选条件计算概览
    const { groupedRecords } = this.data;
    let totalIncome = 0;
    let totalExpense = 0;

    groupedRecords.forEach(group => {
      totalIncome += group.dayIncome;
      totalExpense += group.dayExpense;
    });

    console.log('统计数据:', { totalIncome, totalExpense, balance: totalIncome - totalExpense });

    this.setData({
      summary: {
        totalIncome,
        totalExpense,
        balance: totalIncome - totalExpense
      }
    });
  },

  /**
   * 构建查询参数
   */
  buildQuery(): TransactionQuery {
    const query: TransactionQuery = {
      page: this.data.currentPage,
      pageSize: this.data.pageSize
    };
    
    // 类型筛选
    if (this.data.typeFilter !== 'all') {
      query.type = this.data.typeFilter as 'income' | 'expense';
    }
    
    // 分类筛选
    if (this.data.categoryFilter !== 'all') {
      query.categoryId = this.data.categoryFilter;
    }
    
    // 日期筛选
    const dateRange = this.getDateRange(this.data.dateFilter);
    if (dateRange) {
      query.startDate = dateRange.start;
      query.endDate = dateRange.end;
    }
    
    return query;
  },

  /**
   * 获取日期范围
   */
  getDateRange(filter: string): { start: string; end: string } | null {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    
    switch (filter) {
      case 'today':
        const today = formatDate(now, 'YYYY-MM-DD');
        return { start: today, end: today };
        
      case 'week':
        const weekStart = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
        return {
          start: formatDate(weekStart, 'YYYY-MM-DD'),
          end: formatDate(now, 'YYYY-MM-DD')
        };
        
      case 'month':
        const monthStart = new Date(year, month, 1);
        const monthEnd = new Date(year, month + 1, 0);
        return {
          start: formatDate(monthStart, 'YYYY-MM-DD'),
          end: formatDate(monthEnd, 'YYYY-MM-DD')
        };
        
      case 'year':
        return {
          start: `${year}-01-01`,
          end: `${year}-12-31`
        };
        
      default:
        return null;
    }
  },

  /**
   * 按日期分组交易记录
   */
  groupTransactionsByDate(transactions: Transaction[]): GroupedRecord[] {
    const groups: Record<string, GroupedRecord> = {};
    
    transactions.forEach(transaction => {
      const date = transaction.date;
      
      if (!groups[date]) {
        groups[date] = {
          date,
          dayIncome: 0,
          dayExpense: 0,
          records: []
        };
      }
      
      groups[date].records.push(transaction);
      
      if (transaction.type === 'income') {
        groups[date].dayIncome += transaction.amount;
      } else {
        groups[date].dayExpense += transaction.amount;
      }
    });
    
    // 按日期排序（最新的在前）
    return Object.values(groups).sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
  },

  /**
   * 合并分组记录（用于加载更多）
   */
  mergeGroupedRecords(existing: GroupedRecord[], newRecords: GroupedRecord[]): GroupedRecord[] {
    const merged = [...existing];
    
    newRecords.forEach(newGroup => {
      const existingIndex = merged.findIndex(group => group.date === newGroup.date);
      
      if (existingIndex >= 0) {
        // 合并同一天的记录
        merged[existingIndex].records.push(...newGroup.records);
        merged[existingIndex].dayIncome += newGroup.dayIncome;
        merged[existingIndex].dayExpense += newGroup.dayExpense;
      } else {
        // 添加新的日期分组
        merged.push(newGroup);
      }
    });
    
    // 重新排序
    return merged.sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
  },

  /**
   * 获取空状态文本
   */
  getEmptyText(): string {
    const { typeFilter, dateFilter, categoryFilter } = this.data;
    
    if (typeFilter !== 'all' || dateFilter !== 'month' || categoryFilter !== 'all') {
      return '没有符合条件的记录';
    }
    
    return '暂无交易记录';
  },

  /**
   * 格式化金额
   */
  formatAmount(amount: number, showSymbol: boolean = true): string {
    return formatAmount(amount, showSymbol);
  },

  /**
   * 格式化日期头部
   */
  formatDateHeader(date: string): string {
    const today = formatDate(new Date(), 'YYYY-MM-DD');
    const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000), 'YYYY-MM-DD');
    
    if (date === today) {
      return '今天';
    } else if (date === yesterday) {
      return '昨天';
    } else {
      const dateObj = new Date(date);
      const now = new Date();
      
      if (dateObj.getFullYear() === now.getFullYear()) {
        return formatDate(dateObj, 'MM月DD日');
      } else {
        return formatDate(dateObj, 'YYYY年MM月DD日');
      }
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp: number): string {
    return formatDate(new Date(timestamp), 'HH:mm');
  },

  /**
   * 筛选类型
   */
  onFilterType() {
    const options: FilterOption[] = [
      { label: '全部', value: 'all', selected: this.data.typeFilter === 'all' },
      { label: '收入', value: 'income', selected: this.data.typeFilter === 'income' },
      { label: '支出', value: 'expense', selected: this.data.typeFilter === 'expense' }
    ];
    
    this.showFilterModal('type', '选择类型', options);
  },

  /**
   * 筛选日期
   */
  onFilterDate() {
    const options: FilterOption[] = [
      { label: '今天', value: 'today', selected: this.data.dateFilter === 'today' },
      { label: '本周', value: 'week', selected: this.data.dateFilter === 'week' },
      { label: '本月', value: 'month', selected: this.data.dateFilter === 'month' },
      { label: '本年', value: 'year', selected: this.data.dateFilter === 'year' },
      { label: '全部', value: 'all', selected: this.data.dateFilter === 'all' }
    ];
    
    this.showFilterModal('date', '选择时间', options);
  },

  /**
   * 筛选分类
   */
  onFilterCategory() {
    const options: FilterOption[] = [
      { label: '全部', value: 'all', selected: this.data.categoryFilter === 'all' }
    ];
    
    // 添加分类选项
    this.data.categories.forEach(category => {
      options.push({
        label: category.name,
        value: category.id,
        selected: this.data.categoryFilter === category.id
      });
    });
    
    this.showFilterModal('category', '选择分类', options);
  },

  /**
   * 显示筛选弹窗
   */
  showFilterModal(type: string, title: string, options: FilterOption[]) {
    this.setData({
      showFilterModal: true,
      currentFilterType: type,
      currentFilterTitle: title,
      currentFilterOptions: options
    });
  },

  /**
   * 关闭筛选弹窗
   */
  onCloseFilterModal() {
    this.setData({ showFilterModal: false });
  },

  /**
   * 选择筛选选项
   */
  onSelectFilterOption(e: WechatMiniprogram.TouchEvent) {
    const option = e.currentTarget.dataset.option as FilterOption;
    const options = this.data.currentFilterOptions.map(item => ({
      ...item,
      selected: item.value === option.value
    }));
    
    this.setData({ currentFilterOptions: options });
  },

  /**
   * 重置筛选
   */
  onResetFilter() {
    const options = this.data.currentFilterOptions.map(item => ({
      ...item,
      selected: item.value === 'all'
    }));
    
    this.setData({ currentFilterOptions: options });
  },

  /**
   * 确认筛选
   */
  async onConfirmFilter() {
    const selectedOption = this.data.currentFilterOptions.find(item => item.selected);
    if (!selectedOption) return;
    
    const { currentFilterType } = this.data;
    const updateData: any = { showFilterModal: false };
    
    // 更新筛选条件
    if (currentFilterType === 'type') {
      updateData.typeFilter = selectedOption.value;
      updateData.typeFilterText = selectedOption.label;
    } else if (currentFilterType === 'date') {
      updateData.dateFilter = selectedOption.value;
      updateData.dateFilterText = selectedOption.label;
    } else if (currentFilterType === 'category') {
      updateData.categoryFilter = selectedOption.value;
      updateData.categoryFilterText = selectedOption.label;
    }
    
    this.setData(updateData);
    
    // 重新加载数据
    await this.refreshData();
  },

  /**
   * 搜索
   */
  onSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    });
  },

  /**
   * 记录详情
   */
  onRecordDetail(e: WechatMiniprogram.TouchEvent) {
    const record = e.currentTarget.dataset.record as Transaction;
    wx.navigateTo({
      url: `/pages/record-detail/record-detail?id=${record.id}`
    });
  },

  /**
   * 长按记录
   */
  onRecordLongPress(e: WechatMiniprogram.TouchEvent) {
    const record = e.currentTarget.dataset.record as Transaction;
    this.setData({
      showActionModal: true,
      selectedRecord: record
    });
  },

  /**
   * 关闭操作菜单
   */
  onCloseActionModal() {
    this.setData({ showActionModal: false });
  },

  /**
   * 编辑记录
   */
  onEditRecord(e: WechatMiniprogram.TouchEvent) {
    const record = e.currentTarget.dataset.record as Transaction;
    this.setData({ showActionModal: false });
    
    wx.navigateTo({
      url: `/pages/add-record/add-record?id=${record.id}`
    });
  },

  /**
   * 复制记录
   */
  onDuplicateRecord(e: WechatMiniprogram.TouchEvent) {
    const record = e.currentTarget.dataset.record as Transaction;
    this.setData({ showActionModal: false });
    
    wx.navigateTo({
      url: `/pages/add-record/add-record?type=${record.type}&categoryId=${record.categoryId}&amount=${record.amount}&description=${record.description}`
    });
  },

  /**
   * 删除记录
   */
  onDeleteRecord(e: WechatMiniprogram.TouchEvent) {
    const record = e.currentTarget.dataset.record as Transaction;
    this.setData({ showActionModal: false });
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            await transactionService.deleteTransaction(record.id);
            await this.refreshData();
          } catch (error) {
            console.error('删除记录失败:', error);
            this.showError('删除失败，请重试');
          }
        }
      }
    });
  },

  /**
   * 添加记录
   */
  onAddRecord() {
    wx.switchTab({
      url: '/pages/add-record/add-record'
    });
  },

  /**
   * 加载更多
   */
  onLoadMore() {
    this.loadMoreData();
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 显示错误信息
   */
  showError(message: string) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
});
