<!--账单列表页面-->
<wxs module="utils">
  function formatAmount(amount) {
    if (amount === null || amount === undefined) return '0.00';
    return '¥' + Number(amount).toFixed(2);
  }

  function formatDate(dateStr) {
    if (!dateStr) return '';
    var date = getDate(dateStr);
    var month = date.getMonth() + 1;
    var day = date.getDate();
    return month + '月' + day + '日';
  }

  function formatTime(timestamp) {
    if (!timestamp) return '';
    var date = getDate(timestamp);
    var hours = date.getHours().toString();
    var minutes = date.getMinutes().toString();
    if (hours.length === 1) hours = '0' + hours;
    if (minutes.length === 1) minutes = '0' + minutes;
    return hours + ':' + minutes;
  }

  module.exports = {
    formatAmount: formatAmount,
    formatDate: formatDate,
    formatTime: formatTime
  };
</wxs>

<view class="records-page">
  <!-- 顶部筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item" bindtap="onFilterType">
      <text class="filter-label">类型</text>
      <text class="filter-value">{{typeFilterText}}</text>
      <text class="filter-arrow">▼</text>
    </view>
    
    <view class="filter-item" bindtap="onFilterDate">
      <text class="filter-label">时间</text>
      <text class="filter-value">{{dateFilterText}}</text>
      <text class="filter-arrow">▼</text>
    </view>
    
    <view class="filter-item" bindtap="onFilterCategory">
      <text class="filter-label">分类</text>
      <text class="filter-value">{{categoryFilterText}}</text>
      <text class="filter-arrow">▼</text>
    </view>
    
    <view class="search-btn" bindtap="onSearch">
      <text class="icon">🔍</text>
    </view>
  </view>

  <!-- 统计概览 -->
  <view class="summary-card card" wx:if="{{showSummary}}">
    <view class="summary-content">
      <view class="summary-item income">
        <text class="label">收入</text>
        <text class="amount">{{utils.formatAmount(summary.totalIncome)}}</text>
      </view>
      <view class="summary-item expense">
        <text class="label">支出</text>
        <text class="amount">{{utils.formatAmount(summary.totalExpense)}}</text>
      </view>
      <view class="summary-item balance">
        <text class="label">结余</text>
        <text class="amount {{summary.balance >= 0 ? 'positive' : 'negative'}}">{{utils.formatAmount(summary.balance)}}</text>
      </view>
    </view>
  </view>

  <!-- 交易记录列表 -->
  <view class="records-container">
    <view wx:if="{{groupedRecords.length > 0}}">
      <view 
        class="date-group"
        wx:for="{{groupedRecords}}" 
        wx:key="date"
      >
        <!-- 日期分组头部 -->
        <view class="date-header">
          <text class="date-text">{{utils.formatDate(item.date)}}</text>
          <view class="date-summary">
            <text class="income-text" wx:if="{{item.dayIncome > 0}}">
              收入 {{utils.formatAmount(item.dayIncome)}}
            </text>
            <text class="expense-text" wx:if="{{item.dayExpense > 0}}">
              支出 {{utils.formatAmount(item.dayExpense)}}
            </text>
          </view>
        </view>
        
        <!-- 当日交易列表 -->
        <view class="records-list">
          <view 
            class="record-item"
            wx:for="{{item.records}}" 
            wx:key="id"
            wx:for-item="record"
            bindtap="onRecordDetail"
            bindlongpress="onRecordLongPress"
            data-record="{{record}}"
          >
            <view class="record-icon" style="background-color: {{record.categoryColor}};">
              <text class="icon">{{record.categoryIcon}}</text>
            </view>

            <view class="record-info">
              <view class="record-main">
                <text class="category-name">{{record.categoryName}}</text>
                <text class="amount {{record.type}}">
                  {{record.type === 'expense' ? '-' : '+'}}{{utils.formatAmount(record.amount)}}
                </text>
              </view>
              <view class="record-meta">
                <text class="description">{{record.description || '无备注'}}</text>
                <text class="time">{{utils.formatTime(record.createTime)}}</text>
              </view>
            </view>
            
            <!-- 滑动操作按钮 -->
            <view class="record-actions" wx:if="{{record.showActions}}">
              <view class="action-btn edit" bindtap="onEditRecord" data-record="{{record}}">
                编辑
              </view>
              <view class="action-btn delete" bindtap="onDeleteRecord" data-record="{{record}}">
                删除
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <view class="empty-icon">📝</view>
      <text class="empty-text">{{emptyText}}</text>
      <view class="btn btn-primary btn-small" bindtap="onAddRecord">
        开始记账
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !loading}}">
    <view class="btn btn-outline btn-small" bindtap="onLoadMore">
      加载更多
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-modal {{showFilterModal ? 'show' : ''}}" bindtap="onCloseFilterModal">
  <view class="filter-content" catchtap="stopPropagation">
    <view class="filter-header">
      <text class="filter-title">{{currentFilterTitle}}</text>
      <text class="close-btn" bindtap="onCloseFilterModal">✕</text>
    </view>
    
    <view class="filter-options">
      <view 
        class="filter-option {{item.selected ? 'selected' : ''}}"
        wx:for="{{currentFilterOptions}}" 
        wx:key="value"
        bindtap="onSelectFilterOption"
        data-option="{{item}}"
      >
        <text class="option-text">{{item.label}}</text>
        <text class="option-check" wx:if="{{item.selected}}">✓</text>
      </view>
    </view>
    
    <view class="filter-actions">
      <view class="btn btn-outline" bindtap="onResetFilter">
        重置
      </view>
      <view class="btn btn-primary" bindtap="onConfirmFilter">
        确定
      </view>
    </view>
  </view>
</view>

<!-- 操作菜单 -->
<view class="action-modal {{showActionModal ? 'show' : ''}}" bindtap="onCloseActionModal">
  <view class="action-content" catchtap="stopPropagation">
    <view class="action-header">
      <text class="action-title">选择操作</text>
    </view>
    
    <view class="action-options">
      <view class="action-option" bindtap="onEditRecord" data-record="{{selectedRecord}}">
        <text class="option-icon">✏️</text>
        <text class="option-text">编辑</text>
      </view>
      <view class="action-option" bindtap="onDuplicateRecord" data-record="{{selectedRecord}}">
        <text class="option-icon">📋</text>
        <text class="option-text">复制</text>
      </view>
      <view class="action-option delete" bindtap="onDeleteRecord" data-record="{{selectedRecord}}">
        <text class="option-icon">🗑️</text>
        <text class="option-text">删除</text>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>
