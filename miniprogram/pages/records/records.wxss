/* 账单列表页面样式 */
.records-page {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 24rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 12rpx;
  border-radius: 20rpx;
  background-color: var(--background-color);
  margin-right: 16rpx;
  transition: all 0.3s ease;
}

.filter-item:last-child {
  margin-right: 0;
}

.filter-item:active {
  background-color: var(--border-color);
}

.filter-label {
  font-size: 24rpx;
  color: var(--text-color-secondary);
  margin-right: 8rpx;
}

.filter-value {
  font-size: 26rpx;
  color: var(--text-color);
  margin-right: 4rpx;
}

.filter-arrow {
  font-size: 20rpx;
  color: var(--text-color-light);
}

.search-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  transition: all 0.3s ease;
}

.search-btn:active {
  transform: scale(0.9);
}

.search-btn .icon {
  font-size: 28rpx;
  color: white;
}

/* 统计概览 */
.summary-card {
  margin: 24rpx 32rpx;
  padding: 32rpx;
}

.summary-content {
  display: flex;
  justify-content: space-around;
  align-items: stretch; /* 确保所有项目高度一致 */
}

.summary-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* 确保内容从顶部开始 */
}

.summary-item .label {
  display: block;
  font-size: 24rpx;
  color: var(--text-color-secondary);
  margin-bottom: 8rpx;
  height: 32rpx; /* 固定标签高度 */
  line-height: 32rpx; /* 确保标签垂直居中 */
}

.summary-item .amount {
  font-size: 32rpx;
  font-weight: 600;
  line-height: 40rpx; /* 统一行高 */
  height: 40rpx; /* 固定数字高度 */
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

.summary-item.income .amount {
  color: var(--success-color);
}

.summary-item.expense .amount {
  color: var(--error-color);
}

.summary-item.balance .amount.positive {
  color: var(--success-color);
}

.summary-item.balance .amount.negative {
  color: var(--error-color);
}

/* 记录容器 */
.records-container {
  padding: 0 32rpx;
}

/* 日期分组 */
.date-group {
  margin-bottom: 32rpx;
}

.date-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0 16rpx;
  border-bottom: 1rpx solid var(--border-color);
  margin-bottom: 16rpx;
}

.date-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-color);
}

.date-summary {
  display: flex;
  gap: 24rpx;
}

.income-text {
  font-size: 24rpx;
  color: var(--success-color);
}

.expense-text {
  font-size: 24rpx;
  color: var(--error-color);
}

/* 记录列表 */
.records-list {
  background-color: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  position: relative;
  transition: all 0.3s ease;
  background-color: white;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: var(--background-color);
}

.record-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.record-icon .icon {
  font-size: 32rpx;
  color: white;
}

.record-info {
  flex: 1;
  min-width: 0;
}

.record-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-color);
}

.record-main .amount {
  font-size: 32rpx;
  font-weight: 600;
}

.record-main .amount.income {
  color: var(--success-color);
}

.record-main .amount.expense {
  color: var(--error-color);
}

.record-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.description {
  font-size: 24rpx;
  color: var(--text-color-secondary);
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time {
  font-size: 24rpx;
  color: var(--text-color-light);
}

/* 滑动操作按钮 */
.record-actions {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  background-color: white;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.record-item.show-actions .record-actions {
  transform: translateX(0);
}

.action-btn {
  width: 120rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: white;
  font-weight: 500;
}

.action-btn.edit {
  background-color: var(--primary-color);
}

.action-btn.delete {
  background-color: var(--error-color);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-color-light);
  margin-bottom: 32rpx;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  padding: 32rpx;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-modal.show {
  opacity: 1;
  visibility: visible;
}

.filter-content {
  width: 100%;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.filter-modal.show .filter-content {
  transform: translateY(0);
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: var(--text-color-secondary);
}

.filter-options {
  margin-bottom: 32rpx;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid var(--border-color);
}

.filter-option:last-child {
  border-bottom: none;
}

.filter-option.selected {
  color: var(--primary-color);
}

.option-text {
  font-size: 30rpx;
}

.option-check {
  font-size: 24rpx;
  color: var(--primary-color);
}

.filter-actions {
  display: flex;
  gap: 24rpx;
}

.filter-actions .btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 操作菜单 */
.action-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.action-modal.show {
  opacity: 1;
  visibility: visible;
}

.action-content {
  width: 100%;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.action-modal.show .action-content {
  transform: translateY(0);
}

.action-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.action-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
}

.action-options {
  /* 样式继承自上面的定义 */
}

.action-option {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.3s ease;
}

.action-option:last-child {
  border-bottom: none;
}

.action-option:active {
  background-color: var(--background-color);
}

.action-option.delete {
  color: var(--error-color);
}

.option-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
}

.option-text {
  font-size: 30rpx;
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background-color: white;
  padding: 48rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-color-secondary);
}
