"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// 搜索页面逻辑
const helpers_1 = require("../../utils/helpers");
const transaction_service_1 = __importDefault(require("../../services/transaction.service"));
Page({
    /**
     * 页面的初始数据
     */
    data: {
        searchKeyword: '',
        searchResults: [],
        suggestions: [],
        hotKeywords: ['餐饮', '交通', '购物', '娱乐', '工资', '奖金'],
        sortBy: 'date',
        loading: false
    },
    // 防抖搜索函数
    debouncedSearch: null,
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        console.log('搜索页面加载');
        // 初始化防抖搜索
        this.debouncedSearch = (0, helpers_1.debounce)(this.performSearch.bind(this), 300);
        // 加载搜索历史
        this.loadSearchHistory();
    },
    /**
     * 加载搜索历史
     */
    loadSearchHistory() {
        try {
            const history = wx.getStorageSync('search_history') || [];
            this.setData({ suggestions: history.slice(0, 10) });
        }
        catch (error) {
            console.error('加载搜索历史失败:', error);
        }
    },
    /**
     * 保存搜索历史
     */
    saveSearchHistory(keyword) {
        try {
            const history = wx.getStorageSync('search_history') || [];
            // 移除重复项
            const filteredHistory = history.filter((item) => item !== keyword);
            // 添加到开头
            filteredHistory.unshift(keyword);
            // 限制数量
            const newHistory = filteredHistory.slice(0, 20);
            wx.setStorageSync('search_history', newHistory);
            this.setData({ suggestions: newHistory.slice(0, 10) });
        }
        catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    },
    /**
     * 搜索输入
     */
    onSearchInput(e) {
        const keyword = e.detail.value;
        this.setData({ searchKeyword: keyword });
        if (keyword.trim()) {
            // 使用防抖搜索
            this.debouncedSearch(keyword.trim());
        }
        else {
            this.setData({ searchResults: [] });
        }
    },
    /**
     * 搜索确认
     */
    onSearchConfirm(e) {
        const keyword = e.detail.value.trim();
        if (keyword) {
            this.performSearch(keyword);
            this.saveSearchHistory(keyword);
        }
    },
    /**
     * 执行搜索
     */
    async performSearch(keyword) {
        try {
            this.setData({ loading: true });
            const query = {
                keyword,
                page: 1,
                pageSize: 50
            };
            const response = await transaction_service_1.default.getTransactions(query);
            // 根据排序方式排序
            const sortedResults = this.sortResults(response.list);
            this.setData({
                searchResults: sortedResults,
                loading: false
            });
        }
        catch (error) {
            console.error('搜索失败:', error);
            this.setData({ loading: false });
            wx.showToast({
                title: '搜索失败',
                icon: 'none'
            });
        }
    },
    /**
     * 排序结果
     */
    sortResults(results) {
        const { sortBy } = this.data;
        return [...results].sort((a, b) => {
            if (sortBy === 'date') {
                return new Date(b.date).getTime() - new Date(a.date).getTime();
            }
            else {
                return b.amount - a.amount;
            }
        });
    },
    /**
     * 清除搜索
     */
    onClearSearch() {
        this.setData({
            searchKeyword: '',
            searchResults: []
        });
    },
    /**
     * 取消搜索
     */
    onCancel() {
        wx.navigateBack();
    },
    /**
     * 选择搜索建议
     */
    onSelectSuggestion(e) {
        const keyword = e.currentTarget.dataset.keyword;
        this.setData({ searchKeyword: keyword });
        this.performSearch(keyword);
        this.saveSearchHistory(keyword);
    },
    /**
     * 清除搜索历史
     */
    onClearHistory() {
        wx.showModal({
            title: '确认清除',
            content: '确定要清除所有搜索历史吗？',
            success: (res) => {
                if (res.confirm) {
                    try {
                        wx.removeStorageSync('search_history');
                        this.setData({ suggestions: [] });
                    }
                    catch (error) {
                        console.error('清除搜索历史失败:', error);
                    }
                }
            }
        });
    },
    /**
     * 排序方式变更
     */
    onSortChange(e) {
        const sort = e.currentTarget.dataset.sort;
        if (sort !== this.data.sortBy) {
            this.setData({ sortBy: sort });
            // 重新排序结果
            const sortedResults = this.sortResults(this.data.searchResults);
            this.setData({ searchResults: sortedResults });
        }
    },
    /**
     * 查看结果详情
     */
    onResultDetail(e) {
        const record = e.currentTarget.dataset.record;
        wx.navigateTo({
            url: `/pages/record-detail/record-detail?id=${record.id}`
        });
    },
    /**
     * 按类型筛选
     */
    onFilterByType() {
        wx.showActionSheet({
            itemList: ['全部', '收入', '支出'],
            success: (res) => {
                const types = ['all', 'income', 'expense'];
                const selectedType = types[res.tapIndex];
                if (selectedType !== 'all') {
                    // 重新搜索并筛选类型
                    this.performSearchWithFilter({ type: selectedType });
                }
            }
        });
    },
    /**
     * 按分类筛选
     */
    onFilterByCategory() {
        wx.navigateTo({
            url: '/pages/category-filter/category-filter'
        });
    },
    /**
     * 按金额筛选
     */
    onFilterByAmount() {
        wx.showModal({
            title: '金额筛选',
            content: '请输入金额范围（格式：最小值-最大值）',
            editable: true,
            placeholderText: '例如：100-1000',
            success: (res) => {
                if (res.confirm && res.content) {
                    const range = res.content.split('-');
                    if (range.length === 2) {
                        const minAmount = parseFloat(range[0]);
                        const maxAmount = parseFloat(range[1]);
                        if (!isNaN(minAmount) && !isNaN(maxAmount)) {
                            this.performSearchWithFilter({ minAmount, maxAmount });
                        }
                    }
                }
            }
        });
    },
    /**
     * 按日期筛选
     */
    onFilterByDate() {
        wx.showActionSheet({
            itemList: ['今天', '本周', '本月', '自定义'],
            success: (res) => {
                const now = new Date();
                let startDate;
                let endDate;
                switch (res.tapIndex) {
                    case 0: // 今天
                        startDate = endDate = (0, helpers_1.formatDate)(now, 'YYYY-MM-DD');
                        break;
                    case 1: // 本周
                        const weekStart = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
                        startDate = (0, helpers_1.formatDate)(weekStart, 'YYYY-MM-DD');
                        endDate = (0, helpers_1.formatDate)(now, 'YYYY-MM-DD');
                        break;
                    case 2: // 本月
                        startDate = (0, helpers_1.formatDate)(new Date(now.getFullYear(), now.getMonth(), 1), 'YYYY-MM-DD');
                        endDate = (0, helpers_1.formatDate)(new Date(now.getFullYear(), now.getMonth() + 1, 0), 'YYYY-MM-DD');
                        break;
                    case 3: // 自定义
                        // 这里可以打开日期选择器
                        wx.showToast({
                            title: '功能开发中',
                            icon: 'none'
                        });
                        return;
                }
                this.performSearchWithFilter({ startDate, endDate });
            }
        });
    },
    /**
     * 带筛选条件的搜索
     */
    async performSearchWithFilter(filters) {
        try {
            this.setData({ loading: true });
            const query = {
                keyword: this.data.searchKeyword,
                page: 1,
                pageSize: 50,
                ...filters
            };
            const response = await transaction_service_1.default.getTransactions(query);
            const sortedResults = this.sortResults(response.list);
            this.setData({
                searchResults: sortedResults,
                loading: false
            });
        }
        catch (error) {
            console.error('筛选搜索失败:', error);
            this.setData({ loading: false });
            wx.showToast({
                title: '搜索失败',
                icon: 'none'
            });
        }
    },
    /**
     * 格式化金额
     */
    formatAmount(amount, showSymbol = true) {
        return (0, helpers_1.formatAmount)(amount, showSymbol);
    },
    /**
     * 格式化日期
     */
    formatDate(date) {
        return (0, helpers_1.formatDate)(new Date(date), 'MM-DD');
    }
});
