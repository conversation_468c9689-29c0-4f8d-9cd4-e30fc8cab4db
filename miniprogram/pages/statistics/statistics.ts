// 统计分析页面逻辑
import { formatAmount, formatDate, getCurrentMonth } from '../../utils/helpers';
import statisticsService, { StatisticsOverview, CategoryStatistics, TrendAnalysis } from '../../services/statistics.service';
import authService from '../../services/auth.service';

// 在微信小程序中使用 require 导入 echarts
// const echarts = require('../../ec-canvas/echarts');

// 页面数据接口
interface PageData {
  // 时间选择
  selectedPeriod: 'month' | 'year' | 'custom';
  currentYear: number;
  currentMonth: number;
  currentPeriodText: string;
  customStartDate: string;
  customEndDate: string;
  showDateModal: boolean;
  
  // 概览数据
  overview: {
    totalIncome: number;
    totalExpense: number;
    balance: number;
    incomeCount: number;
    expenseCount: number;
    totalCount: number;
  };
  
  // 图表相关
  activeChart: 'category' | 'trend';
  categoryChartType: 'pie' | 'bar';
  categoryType: 'income' | 'expense';
  trendType: 'income' | 'expense' | 'balance';
  
  // 数据
  categoryStats: CategoryStatistics[];
  sortedCategoryStats: CategoryStatistics[];
  categoryTotal: number;
  trendData: TrendAnalysis | null;
  trendSummary: {
    avg: number;
    max: number;
    min: number;
    growth: number;
    current: number;
  };

  // ECharts配置
  categoryChart: any;
  trendChart: any;
  
  // 排序
  sortBy: 'amount' | 'count';
  
  // 状态
  loading: boolean;
  loadingText: string;
}

Page<PageData>({
  // ECharts实例
  categoryChartInstance: null as any,
  trendChartInstance: null as any,
  /**
   * 页面的初始数据
   */
  data: {
    // 时间选择
    selectedPeriod: 'month',
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    currentPeriodText: '',
    customStartDate: '',
    customEndDate: '',
    showDateModal: false,
    
    // 概览数据
    overview: {
      totalIncome: 0,
      totalExpense: 0,
      balance: 0,
      incomeCount: 0,
      expenseCount: 0,
      totalCount: 0
    },
    
    // 图表相关
    activeChart: 'category',
    categoryChartType: 'pie',
    categoryType: 'expense',
    trendType: 'expense',
    
    // 数据
    categoryStats: [],
    sortedCategoryStats: [],
    categoryTotal: 0,
    trendData: null,
    trendSummary: {
      avg: 0,
      max: 0,
      min: 0,
      growth: 0,
      current: 0
    },

    // ECharts配置
    categoryChart: {},
    trendChart: {},
    
    // 排序
    sortBy: 'amount',
    
    // 状态
    loading: false,
    loadingText: '加载中...'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    console.log('统计分析页面加载');
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('统计分析页面显示');
    this.checkAndRefreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 检查登录状态
      if (!authService.isLoggedIn()) {
        console.log('用户未登录，跳转到登录页');
        wx.redirectTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 设置当前时间
      this.updatePeriodText();

      // 设置自定义日期默认值
      this.setDefaultCustomDates();

      // 加载数据
      await this.loadData();
    } catch (error) {
      console.error('初始化页面失败:', error);
      this.showError('页面加载失败，请重试');
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    await this.loadData();
  },

  /**
   * 加载数据
   */
  async loadData() {
    try {
      this.setData({ loading: true, loadingText: '加载中...' });

      // 并行加载数据
      await Promise.all([
        this.loadOverviewData(),
        this.loadCategoryStats(),
        this.loadTrendData()
      ]);

      // 数据加载完成后初始化ECharts
      this.initECharts();

    } catch (error) {
      console.error('加载数据失败:', error);
      this.showError('数据加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载概览数据
   */
  async loadOverviewData() {
    try {
      const dateRange = this.getCurrentDateRange();
      console.log('统计页面加载概览数据 - 日期范围:', dateRange);

      const overview = await statisticsService.getOverview(dateRange.start, dateRange.end);
      console.log('统计页面概览数据:', overview);

      // 使用真实的交易数量
      this.setData({
        overview: {
          totalIncome: overview.totalIncome,
          totalExpense: overview.totalExpense,
          balance: overview.totalIncome - overview.totalExpense,
          incomeCount: overview.incomeCount || 0,
          expenseCount: overview.expenseCount || 0,
          totalCount: overview.transactionCount || 0
        }
      });
    } catch (error) {
      console.error('加载概览数据失败:', error);
    }
  },

  /**
   * 加载分类统计
   */
  async loadCategoryStats() {
    try {
      const dateRange = this.getCurrentDateRange();
      console.log('统计页面加载分类统计 - 日期范围:', dateRange, '类型:', this.data.categoryType);

      const stats = await statisticsService.getCategoryStatistics(dateRange.start, dateRange.end, this.data.categoryType);
      console.log('统计页面分类统计数据:', stats);

      // 确保数据有效性
      if (stats && stats.length > 0) {
        // 计算总金额
        const total = stats.reduce((sum, item) => sum + item.amount, 0);

        this.setData({
          categoryStats: stats,
          categoryTotal: total
        });
        this.sortCategoryStats();
      } else {
        console.log('没有分类统计数据');
        this.setData({
          categoryStats: [],
          categoryTotal: 0
        });
      }
    } catch (error) {
      console.error('加载分类统计失败:', error);
      this.setData({ categoryStats: [] });
    }
  },

  /**
   * 加载趋势数据
   */
  async loadTrendData() {
    try {
      const period = this.data.selectedPeriod === 'year' ? 'month' : 'week';
      const count = this.data.selectedPeriod === 'year' ? 12 : 8;

      console.log('统计页面加载趋势数据 - period:', period, 'count:', count, 'type:', this.data.trendType);

      const trendData = await statisticsService.getTrendAnalysis(period, count, this.data.trendType);
      console.log('统计页面趋势数据:', trendData);

      if (trendData && trendData.data && trendData.summary) {
        // 计算当前值（最新一期的数据）
        let current = 0;
        if (trendData.data.length > 0) {
          const latestData = trendData.data[trendData.data.length - 1];
          switch (this.data.trendType) {
            case 'expense':
              current = latestData.expense;
              break;
            case 'income':
              current = latestData.income;
              break;
            case 'balance':
              current = latestData.balance;
              break;
          }
        }

        this.setData({
          trendData,
          trendSummary: {
            ...trendData.summary,
            current
          }
        });

        // 延迟更新趋势图表，确保数据已设置
        setTimeout(() => {
          this.updateTrendChart();
        }, 100);
      } else {
        console.log('趋势数据为空');
        this.setData({
          trendData: null,
          trendSummary: {
            avg: 0,
            max: 0,
            min: 0,
            growth: 0,
            current: 0
          }
        });
      }
    } catch (error) {
      console.error('加载趋势数据失败:', error);
      this.setData({
        trendData: null,
        trendSummary: {
          avg: 0,
          max: 0,
          min: 0,
          growth: 0
        }
      });
    }
  },

  /**
   * 获取当前日期范围
   */
  getCurrentDateRange(): { start: string; end: string } {
    const { selectedPeriod, currentYear, currentMonth, customStartDate, customEndDate } = this.data;
    
    switch (selectedPeriod) {
      case 'month':
        const monthStart = new Date(currentYear, currentMonth - 1, 1);
        const monthEnd = new Date(currentYear, currentMonth, 0);
        return {
          start: formatDate(monthStart, 'YYYY-MM-DD'),
          end: formatDate(monthEnd, 'YYYY-MM-DD')
        };
        
      case 'year':
        return {
          start: `${currentYear}-01-01`,
          end: `${currentYear}-12-31`
        };
        
      case 'custom':
        return {
          start: customStartDate,
          end: customEndDate
        };
        
      default:
        const current = getCurrentMonth();
        return {
          start: current.start,
          end: current.end
        };
    }
  },

  /**
   * 更新周期文本
   */
  updatePeriodText() {
    const { selectedPeriod, currentYear, currentMonth, customStartDate, customEndDate } = this.data;
    let text = '';
    
    switch (selectedPeriod) {
      case 'month':
        text = `${currentYear}年${currentMonth}月`;
        break;
      case 'year':
        text = `${currentYear}年`;
        break;
      case 'custom':
        text = `${customStartDate} 至 ${customEndDate}`;
        break;
    }
    
    this.setData({ currentPeriodText: text });
  },

  /**
   * 设置默认自定义日期
   */
  setDefaultCustomDates() {
    const current = getCurrentMonth();
    this.setData({
      customStartDate: current.start,
      customEndDate: current.end
    });
  },

  /**
   * 排序分类统计
   */
  sortCategoryStats() {
    const { categoryStats, sortBy } = this.data;
    
    const sorted = [...categoryStats].sort((a, b) => {
      if (sortBy === 'amount') {
        return b.amount - a.amount;
      } else {
        return b.transactionCount - a.transactionCount;
      }
    });
    
    this.setData({ sortedCategoryStats: sorted });
  },

  /**
   * 初始化ECharts
   */
  initECharts() {
    this.initCategoryChart();
    this.initTrendChart();
  },

  /**
   * 初始化分类图表
   */
  initCategoryChart() {
    console.log('初始化分类图表');
    this.setData({
      categoryChart: {
        onInit: (chart) => {
          console.log('分类图表初始化回调执行', chart);
          this.categoryChartInstance = chart;
          // 延迟更新，确保数据已加载
          setTimeout(() => {
            this.updateCategoryChart();
          }, 200);
        }
      }
    });
  },

  //  initCategoryChart() {
  //   console.log('初始化分类图表');
  //   this.setData({
  //     categoryChart: {
  //       onInit: this.initChartFix
  //     }
  //   });
  // },

//   initChartFix(canvas, width, height, dpr) {
//   const chart = echarts.init(canvas, null, {
//     width: width,
//     height: height,
//     devicePixelRatio: dpr // 像素比
//   });
//   this.categoryChartInstance = chart;
//   canvas.setChart(chart);

//   var option = {
//         series: [{
//           type: 'pie',
//           radius: ['30%', '70%'], // 内外半径控制环形/饼图
//           data: [
//             { value: 55, name: '北京', itemStyle: { color: '#FF6B6B' } },
//             { value: 20, name: '上海', itemStyle: { color: '#4ECDC4' } },
//             // ...更多数据
//           ],
//           label: { show: false } // 隐藏标签
//         }]
//       };
//   chart.setOption(option);
//   return chart;
// },

  /**
   * 初始化趋势图表
   */
  initTrendChart() {
    console.log('初始化趋势图表');
    this.setData({
      trendChart: {
        onInit: (chart: any) => {
          console.log('趋势图表初始化回调', chart);
          this.trendChartInstance = chart;
          // 延迟更新，确保数据已加载
          setTimeout(() => {
            this.updateTrendChart();
          }, 200);
        }
      }
    });
  },

  /**
   * 更新分类图表
   */
  updateCategoryChart() {
    console.log('更新分类图表', {
      hasInstance: !!this.categoryChartInstance,
      dataLength: this.data.categoryStats.length,
      chartType: this.data.categoryChartType
    });

    if (!this.categoryChartInstance) {
      console.log('图表实例未初始化');
      return;
    }

    if (!this.data.categoryStats.length) {
      console.log('没有分类数据');
      return;
    }

    try {
      const option = this.data.categoryChartType === 'pie'
        ? this.getPieChartOption()
        : this.getBarChartOption();

      console.log('设置图表选项:', option);
      this.categoryChartInstance.setOption(option, true);
    } catch (error) {
      console.error('更新分类图表失败:', error);
    }
  },

  /**
   * 获取饼图配置
   */
  getPieChartOption() {
    const { categoryStats, categoryType } = this.data;
    console.log('生成饼图配置，数据:', categoryStats);

    // 简化的饼图配置
    const option = {
      title: {
        text: `${categoryType === 'expense' ? '支出' : '收入'}分布`,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      series: [{
        name: categoryType === 'expense' ? '支出' : '收入',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: categoryStats.map(item => ({
          value: item.amount,
          name: item.categoryName,
          itemStyle: {
            color: item.categoryColor
          }
        }))
      }]
    };

    console.log('饼图配置:', option);
    return option;
  },

  /**
   * 获取柱状图配置
   */
  getBarChartOption() {
    const { categoryStats, categoryType } = this.data;
    console.log('生成柱状图配置，数据:', categoryStats);

    // 简化的柱状图配置
    const option = {
      title: {
        text: `${categoryType === 'expense' ? '支出' : '收入'}排行`,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '20%'
      },
      xAxis: {
        type: 'category',
        data: categoryStats.map(item => item.categoryName),
        axisLabel: {
          interval: 0,
          rotate: 30,
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        type: 'bar',
        data: categoryStats.map(item => ({
          value: item.amount,
          itemStyle: {
            color: item.categoryColor
          }
        }))
      }]
    };

    console.log('柱状图配置:', option);
    return option;
  },

  /**
   * 更新趋势图表
   */
  updateTrendChart() {
    console.log('更新趋势图表', {
      hasInstance: !!this.trendChartInstance,
      hasData: !!this.data.trendData?.data?.length,
      trendType: this.data.trendType
    });

    if (!this.trendChartInstance) {
      console.log('趋势图表实例未初始化');
      return;
    }

    if (!this.data.trendData?.data?.length) {
      console.log('没有趋势数据');
      return;
    }

    try {
      const option = this.getTrendChartOption();
      console.log('设置趋势图表选项:', option);
      this.trendChartInstance.setOption(option, true);
    } catch (error) {
      console.error('更新趋势图表失败:', error);
    }
  },

  /**
   * 获取趋势图配置
   */
  getTrendChartOption() {
    const { trendData, trendType } = this.data;
    if (!trendData?.data) return {};

    const periods = trendData.data.map(item => item.period);
    let seriesData: any[] = [];
    let yAxisName = '';
    let title = '';

    switch (trendType) {
      case 'expense':
        seriesData = [{
          name: '支出',
          type: 'line',
          data: trendData.data.map(item => item.expense),
          itemStyle: { color: '#ff6b6b' },
          areaStyle: { color: 'rgba(255, 107, 107, 0.1)' }
        }];
        yAxisName = '支出金额';
        title = '支出趋势';
        break;
      case 'income':
        seriesData = [{
          name: '收入',
          type: 'line',
          data: trendData.data.map(item => item.income),
          itemStyle: { color: '#51cf66' },
          areaStyle: { color: 'rgba(81, 207, 102, 0.1)' }
        }];
        yAxisName = '收入金额';
        title = '收入趋势';
        break;
      case 'balance':
        seriesData = [
          {
            name: '收入',
            type: 'line',
            data: trendData.data.map(item => item.income),
            itemStyle: { color: '#51cf66' }
          },
          {
            name: '支出',
            type: 'line',
            data: trendData.data.map(item => item.expense),
            itemStyle: { color: '#ff6b6b' }
          },
          {
            name: '结余',
            type: 'line',
            data: trendData.data.map(item => item.balance),
            itemStyle: { color: '#339af0' },
            areaStyle: { color: 'rgba(51, 154, 240, 0.1)' }
          }
        ];
        yAxisName = '金额';
        title = '收支趋势';
        break;
    }

    return {
      backgroundColor: '#ffffff',
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          color: '#333',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            result += `${param.seriesName}: ¥${param.value.toFixed(2)}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: seriesData.map(s => s.name),
        top: 50
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: periods,
        axisLabel: {
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value',
        name: yAxisName,
        axisLabel: {
          formatter: '¥{value}'
        }
      },
      series: seriesData
    };
  },





  /**
   * 格式化金额
   */
  formatAmount(amount: number, showSymbol: boolean = true): string {
    return formatAmount(amount, showSymbol);
  },

  /**
   * 选择时间周期
   */
  onSelectPeriod(e: WechatMiniprogram.TouchEvent) {
    const period = e.currentTarget.dataset.period as 'month' | 'year' | 'custom';
    
    if (period === 'custom') {
      this.setData({ showDateModal: true });
    } else {
      this.setData({ selectedPeriod: period });
      this.updatePeriodText();
      this.refreshData();
    }
  },

  /**
   * 上一个周期
   */
  onPrevPeriod() {
    const { selectedPeriod, currentYear, currentMonth } = this.data;
    
    if (selectedPeriod === 'month') {
      if (currentMonth === 1) {
        this.setData({
          currentYear: currentYear - 1,
          currentMonth: 12
        });
      } else {
        this.setData({
          currentMonth: currentMonth - 1
        });
      }
    } else if (selectedPeriod === 'year') {
      this.setData({
        currentYear: currentYear - 1
      });
    }
    
    this.updatePeriodText();
    this.refreshData();
  },

  /**
   * 下一个周期
   */
  onNextPeriod() {
    const { selectedPeriod, currentYear, currentMonth } = this.data;
    const now = new Date();
    
    if (selectedPeriod === 'month') {
      if (currentMonth === 12) {
        this.setData({
          currentYear: currentYear + 1,
          currentMonth: 1
        });
      } else {
        this.setData({
          currentMonth: currentMonth + 1
        });
      }
    } else if (selectedPeriod === 'year') {
      this.setData({
        currentYear: currentYear + 1
      });
    }
    
    this.updatePeriodText();
    this.refreshData();
  },

  /**
   * 切换图表类型
   */
  onSwitchChart(e: WechatMiniprogram.TouchEvent) {
    const chart = e.currentTarget.dataset.chart as 'category' | 'trend';
    this.setData({ activeChart: chart });

    // 延迟绘制图表，确保DOM已更新
    setTimeout(() => {
      this.drawCharts();
    }, 100);
  },

  /**
   * 切换分类类型（收入/支出）
   */
  onSwitchCategoryType(e: WechatMiniprogram.TouchEvent) {
    const type = e.currentTarget.dataset.type as 'income' | 'expense';
    console.log('切换分类类型:', type);

    this.setData({ categoryType: type });

    // 重新加载分类统计数据
    this.loadCategoryStats();
  },

  /**
   * 切换分类图表类型
   */
  onSwitchCategoryChart(e: WechatMiniprogram.TouchEvent) {
    const type = e.currentTarget.dataset.type as 'pie' | 'bar';
    console.log('切换图表类型:', type);

    this.setData({ categoryChartType: type });

    // 延迟更新图表
    setTimeout(() => {
      this.updateCategoryChart();
    }, 100);
  },

  /**
   * 切换趋势类型
   */
  onSwitchTrendType(e: WechatMiniprogram.TouchEvent) {
    const type = e.currentTarget.dataset.type as 'income' | 'expense' | 'balance';
    this.setData({ trendType: type });

    // 重新加载趋势数据
    this.loadTrendData();
  },

  /**
   * 切换排序方式
   */
  onSortChange(e: WechatMiniprogram.TouchEvent) {
    const sort = e.currentTarget.dataset.sort as 'amount' | 'count';
    this.setData({ sortBy: sort });
    this.sortCategoryStats();
  },

  /**
   * 分类详情
   */
  onCategoryDetail(e: WechatMiniprogram.TouchEvent) {
    const category = e.currentTarget.dataset.category as CategoryStatistics;
    wx.navigateTo({
      url: `/pages/category-detail/category-detail?id=${category.categoryId}`
    });
  },

  /**
   * 图表触摸事件
   */
  onChartTouch(e: WechatMiniprogram.TouchEvent) {
    // 处理图表交互
    console.log('图表触摸事件', e);
  },

  /**
   * 开始日期变化
   */
  onStartDateChange(e: WechatMiniprogram.PickerChange) {
    this.setData({ customStartDate: e.detail.value });
  },

  /**
   * 结束日期变化
   */
  onEndDateChange(e: WechatMiniprogram.PickerChange) {
    this.setData({ customEndDate: e.detail.value });
  },

  /**
   * 关闭日期弹窗
   */
  onCloseDateModal() {
    this.setData({ showDateModal: false });
  },

  /**
   * 确认自定义日期
   */
  onConfirmCustomDate() {
    this.setData({
      selectedPeriod: 'custom',
      showDateModal: false
    });
    this.updatePeriodText();
    this.refreshData();
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 显示错误信息
   */
  showError(message: string) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 检查并刷新数据
   */
  checkAndRefreshData() {
    try {
      // 检查数据更新标记
      const lastUpdateFlag = wx.getStorageSync('data_updated_flag');
      const lastLoadTime = wx.getStorageSync('statistics_last_load_time') || 0;

      // 如果有更新标记且时间比上次加载时间新，则刷新数据
      if (lastUpdateFlag && lastUpdateFlag > lastLoadTime) {
        console.log('检测到数据更新，刷新统计页面数据');
        this.refreshData();
        // 更新加载时间
        wx.setStorageSync('statistics_last_load_time', Date.now());
      } else {
        console.log('统计数据无更新，跳过刷新');
      }
    } catch (error) {
      console.error('检查统计数据更新失败:', error);
      // 出错时也刷新数据
      this.refreshData();
    }
  }
});
