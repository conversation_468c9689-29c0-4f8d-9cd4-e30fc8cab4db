<!-- 统计分析页面 -->
<wxs module="utils">
  function formatAmount(amount) {
    if (amount === null || amount === undefined) return '¥0.00';
    return '¥' + Number(amount).toFixed(2);
  }
  
  function formatPercentage(value) {
    if (value === null || value === undefined) return '0.00%';
    return Number(value).toFixed(2) + '%';
  }
  
  module.exports = {
    formatAmount: formatAmount,
    formatPercentage: formatPercentage
  };
</wxs>

<view class="statistics-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">统计分析</text>
  </view>

  <!-- 时间选择器 -->
  <view class="time-selector card">
    <view class="selector-tabs">
      <view 
        class="selector-tab {{selectedPeriod === 'month' ? 'active' : ''}}"
        bindtap="onPeriodChange"
        data-period="month"
      >
        本月
      </view>
      <view 
        class="selector-tab {{selectedPeriod === 'year' ? 'active' : ''}}"
        bindtap="onPeriodChange"
        data-period="year"
      >
        本年
      </view>
      <view 
        class="selector-tab {{selectedPeriod === 'custom' ? 'active' : ''}}"
        bindtap="onPeriodChange"
        data-period="custom"
      >
        自定义
      </view>
    </view>
    
    <view class="current-period">
      <text class="period-text">{{currentPeriodText}}</text>
    </view>
  </view>

  <!-- 概览数据 -->
  <view class="overview-section card">
    <view class="overview-grid">
      <view class="overview-item income">
        <text class="overview-label">总收入</text>
        <text class="overview-value">{{utils.formatAmount(overview.totalIncome)}}</text>
      </view>
      <view class="overview-item expense">
        <text class="overview-label">总支出</text>
        <text class="overview-value">{{utils.formatAmount(overview.totalExpense)}}</text>
      </view>
      <view class="overview-item balance">
        <text class="overview-label">结余</text>
        <text class="overview-value {{overview.balance >= 0 ? 'positive' : 'negative'}}">
          {{utils.formatAmount(overview.balance)}}
        </text>
      </view>
      <view class="overview-item count">
        <text class="overview-label">交易笔数</text>
        <text class="overview-value">{{overview.transactionCount}}笔</text>
      </view>
    </view>
  </view>

  <!-- 图表类型切换 -->
  <view class="chart-tabs">
    <view 
      class="chart-tab {{activeChart === 'category' ? 'active' : ''}}"
      bindtap="onSwitchChart"
      data-chart="category"
    >
      分类分析
    </view>
    <view 
      class="chart-tab {{activeChart === 'trend' ? 'active' : ''}}"
      bindtap="onSwitchChart"
      data-chart="trend"
    >
      趋势分析
    </view>
  </view>

  <!-- 分类分析 -->
  <view class="chart-section" wx:if="{{activeChart === 'category'}}">
    <!-- 分类类型切换 -->
    <view class="category-header">
      <view class="category-tabs">
        <view
          class="category-tab {{categoryType === 'expense' ? 'active' : ''}}"
          bindtap="onSwitchCategoryType"
          data-type="expense"
        >
          <view class="tab-content">
            <text class="tab-icon">💸</text>
            <text class="tab-label">支出</text>
          </view>
        </view>
        <view
          class="category-tab {{categoryType === 'income' ? 'active' : ''}}"
          bindtap="onSwitchCategoryType"
          data-type="income"
        >
          <view class="tab-content">
            <text class="tab-icon">💰</text>
            <text class="tab-label">收入</text>
          </view>
        </view>
      </view>

      <!-- 总览数据 -->
      <view class="category-summary">
        <view class="summary-item">
          <text class="summary-value">{{utils.formatAmount(categoryTotal)}}</text>
          <text class="summary-label">总{{categoryType === 'expense' ? '支出' : '收入'}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-value">{{categoryStats.length}}</text>
          <text class="summary-label">分类数</text>
        </view>
      </view>
    </view>

    <!-- 图表类型切换 -->
    <view class="chart-type-selector">
      <view class="selector-wrapper">
        <view
          class="chart-type-btn {{categoryChartType === 'pie' ? 'active' : ''}}"
          bindtap="onSwitchCategoryChart"
          data-type="pie"
        >
          <text class="btn-icon">🥧</text>
          <text class="btn-text">饼图</text>
        </view>
        <view
          class="chart-type-btn {{categoryChartType === 'bar' ? 'active' : ''}}"
          bindtap="onSwitchCategoryChart"
          data-type="bar"
        >
          <text class="btn-icon">📊</text>
          <text class="btn-text">柱状图</text>
        </view>
      </view>
    </view>

    <!-- ECharts图表容器 -->
    <view class="echarts-container" wx:if="{{categoryStats.length > 0}}">
      <ec-canvas
        id="category-chart"
        canvas-id="category-chart"
        ec="{{ categoryChart }}"
        class="chart-canvas"
        force-use-old-canvas="true" 
      ></ec-canvas>
    </view>

    <!-- 分类数据列表 -->
    <view class="category-data-section">
      <view class="data-header">
        <text class="data-title">分类明细</text>
        <view class="sort-controls">
          <text
            class="sort-btn {{sortBy === 'amount' ? 'active' : ''}}"
            bindtap="onSortChange"
            data-sort="amount"
          >
            金额
          </text>
          <text
            class="sort-btn {{sortBy === 'count' ? 'active' : ''}}"
            bindtap="onSortChange"
            data-sort="count"
          >
            次数
          </text>
        </view>
      </view>

      <view class="category-data-list" wx:if="{{categoryStats.length > 0}}">
        <view
          class="data-item"
          wx:for="{{sortedCategoryStats}}"
          wx:key="categoryId"
        >
          <view class="item-icon" style="background: {{item.categoryColor}};">
            <text class="icon-text {{item.categoryIcon}}"></text>
          </view>
          <view class="item-info">
            <view class="info-main">
              <text class="category-name">{{item.categoryName}}</text>
              <text class="category-amount">{{utils.formatAmount(item.amount)}}</text>
            </view>
            <view class="info-detail">
              <text class="transaction-count">{{item.transactionCount}}笔</text>
              <text class="percentage">{{utils.formatPercentage(item.percentage)}}</text>
            </view>
          </view>
          <view class="item-progress">
            <view
              class="progress-bar"
              style="width: {{item.percentage}}%; background: {{item.categoryColor}};"
            ></view>
          </view>
        </view>
      </view>

      <view class="empty-data" wx:else>
        <text class="empty-icon">📊</text>
        <text class="empty-text">暂无{{categoryType === 'expense' ? '支出' : '收入'}}数据</text>
        <text class="empty-hint">添加一些交易记录来查看分析</text>
      </view>
    </view>
  </view>

  <!-- 趋势分析 -->
  <view class="chart-section" wx:if="{{activeChart === 'trend'}}">
    <!-- 趋势类型选择 -->
    <view class="trend-header">
      <view class="trend-tabs">
        <view
          class="trend-tab {{trendType === 'expense' ? 'active' : ''}}"
          bindtap="onSwitchTrendType"
          data-type="expense"
        >
          <text class="tab-icon">📉</text>
          <text class="tab-label">支出</text>
        </view>
        <view
          class="trend-tab {{trendType === 'income' ? 'active' : ''}}"
          bindtap="onSwitchTrendType"
          data-type="income"
        >
          <text class="tab-icon">📈</text>
          <text class="tab-label">收入</text>
        </view>
        <view
          class="trend-tab {{trendType === 'balance' ? 'active' : ''}}"
          bindtap="onSwitchTrendType"
          data-type="balance"
        >
          <text class="tab-icon">⚖️</text>
          <text class="tab-label">结余</text>
        </view>
      </view>
    </view>

    <!-- 关键指标 -->
    <view class="trend-metrics">
      <view class="metrics-grid">
        <view class="metric-item primary">
          <text class="metric-value">{{utils.formatAmount(trendSummary.current || 0)}}</text>
          <text class="metric-label">本期{{trendType === 'expense' ? '支出' : trendType === 'income' ? '收入' : '结余'}}</text>
        </view>
        <view class="metric-item">
          <text class="metric-value {{trendSummary.growth >= 0 ? 'positive' : 'negative'}}">
            {{trendSummary.growth >= 0 ? '+' : ''}}{{trendSummary.growth.toFixed(1)}}%
          </text>
          <text class="metric-label">环比变化</text>
        </view>
        <view class="metric-item">
          <text class="metric-value">{{utils.formatAmount(trendSummary.avg || 0)}}</text>
          <text class="metric-label">期间平均</text>
        </view>
        <view class="metric-item">
          <text class="metric-value">{{utils.formatAmount(trendSummary.max || 0)}}</text>
          <text class="metric-label">最高值</text>
        </view>
      </view>
    </view>

    <!-- ECharts趋势图表 -->
    <view class="trend-chart-section">
      <view class="chart-header">
        <text class="chart-title">{{trendType === 'expense' ? '支出' : trendType === 'income' ? '收入' : '结余'}}趋势</text>
        <text class="chart-period">最近{{trendData && trendData.data ? trendData.data.length : 0}}期</text>
      </view>
      <view class="echarts-container" wx:if="{{trendData && trendData.data && trendData.data.length > 0}}">
        <ec-canvas
          id="trend-chart"
          canvas-id="trend-chart"
          ec="{{ trendChart }}"
          class="chart-canvas"
        ></ec-canvas>
      </view>
      <view class="empty-chart" wx:else>
        <text class="empty-icon">📈</text>
        <text class="empty-text">暂无趋势数据</text>
      </view>
    </view>

    <!-- 数据详情 -->
    <view class="trend-data-section" wx:if="{{trendData && trendData.data && trendData.data.length > 0}}">
      <view class="data-header">
        <text class="data-title">数据详情</text>
        <text class="data-subtitle">最近{{trendData.data.length}}期数据</text>
      </view>
      <view class="trend-data-list">
        <view
          class="trend-data-item"
          wx:for="{{trendData.data}}"
          wx:key="period"
          wx:for-index="idx"
        >
          <view class="data-period">
            <text class="period-text">{{item.period}}</text>
          </view>
          <view class="data-values">
            <view class="value-row" wx:if="{{trendType === 'expense' || trendType === 'balance'}}">
              <text class="value-label">支出</text>
              <text class="value-amount expense">{{utils.formatAmount(item.expense)}}</text>
            </view>
            <view class="value-row" wx:if="{{trendType === 'income' || trendType === 'balance'}}">
              <text class="value-label">收入</text>
              <text class="value-amount income">{{utils.formatAmount(item.income)}}</text>
            </view>
            <view class="value-row" wx:if="{{trendType === 'balance'}}">
              <text class="value-label">结余</text>
              <text class="value-amount {{item.balance >= 0 ? 'positive' : 'negative'}}">
                {{utils.formatAmount(item.balance)}}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>
