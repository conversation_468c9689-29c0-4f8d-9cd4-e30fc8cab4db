/* 统计分析页面样式 */
.statistics-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: env(safe-area-inset-bottom);
}

.page-header {
  text-align: center;
  padding: 40rpx 20rpx 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

/* 时间选择器 */
.time-selector {
  margin: 0 20rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.selector-tabs {
  display: flex;
  background: #f8f9fa;
}

.selector-tab {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #6c757d;
  position: relative;
  transition: all 0.3s ease;
}

.selector-tab.active {
  color: #007bff;
  background: white;
  font-weight: 600;
}

.current-period {
  padding: 20rpx;
  text-align: center;
  border-top: 1rpx solid #e9ecef;
}

.period-text {
  font-size: 24rpx;
  color: #6c757d;
}

/* 概览数据 */
.overview-section {
  margin: 0 20rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.overview-item {
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.overview-item.income {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.overview-item.expense {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.overview-item.balance {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.overview-item.count {
  background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
}

.overview-label {
  display: block;
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}

.overview-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.overview-value.positive {
  color: #28a745;
}

.overview-value.negative {
  color: #dc3545;
}

/* 图表类型切换 */
.chart-tabs {
  display: flex;
  margin: 0 20rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.chart-tab {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #6c757d;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.chart-tab.active {
  color: white;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  font-weight: 600;
}

/* 图表区域 */
.chart-section {
  margin: 0 20rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 分类分析 */
.category-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.category-tabs {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.category-tab {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.category-tab.active {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.tab-content {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.tab-icon {
  font-size: 32rpx;
}

.tab-label {
  font-size: 28rpx;
  font-weight: 600;
}

.category-summary {
  display: flex;
  gap: 20rpx;
}

.summary-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.summary-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.summary-label {
  display: block;
  font-size: 24rpx;
  color: #6c757d;
}

/* 图表类型选择器 */
.chart-type-selector {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.selector-wrapper {
  display: flex;
  gap: 20rpx;
}

.chart-type-btn {
  flex: 1;
  height: 60rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  transition: all 0.3s ease;
}

.chart-type-btn.active {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 24rpx;
  font-weight: 600;
}

/* ECharts容器 */
.echarts-container {
  height: 600rpx;
  padding: 20rpx;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 分类数据列表 */
.category-data-section {
  padding: 30rpx;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.data-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.sort-controls {
  display: flex;
  gap: 20rpx;
}

.sort-btn {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  font-size: 24rpx;
  color: #6c757d;
  transition: all 0.3s ease;
}

.sort-btn.active {
  background: #007bff;
  color: white;
}

.category-data-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 32rpx;
  color: white;
}

.item-info {
  flex: 1;
}

.info-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.category-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #007bff;
}

.info-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-count {
  font-size: 24rpx;
  color: #6c757d;
}

.percentage {
  font-size: 24rpx;
  color: #28a745;
  font-weight: 600;
}

.item-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: #e9ecef;
}

.progress-bar {
  height: 100%;
  border-radius: 3rpx;
  transition: width 0.5s ease;
}

/* 趋势分析 */
.trend-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.trend-tabs {
  display: flex;
  gap: 15rpx;
}

.trend-tab {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.trend-tab.active {
  background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
  color: white;
}

/* 趋势指标 */
.trend-metrics {
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.metric-item {
  text-align: center;
  padding: 25rpx 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.metric-item.primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.metric-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.metric-value.positive {
  color: #28a745;
}

.metric-value.negative {
  color: #dc3545;
}

.metric-item.primary .metric-value {
  color: white;
}

.metric-label {
  display: block;
  font-size: 24rpx;
  color: #6c757d;
}

.metric-item.primary .metric-label {
  color: rgba(255, 255, 255, 0.8);
}

/* 趋势图表区域 */
.trend-chart-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.chart-period {
  font-size: 24rpx;
  color: #6c757d;
}

.empty-chart {
  text-align: center;
  padding: 100rpx 20rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #6c757d;
}

/* 趋势数据详情 */
.trend-data-section {
  padding: 30rpx;
}

.data-subtitle {
  font-size: 24rpx;
  color: #6c757d;
}

.trend-data-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
}

.trend-data-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border-left: 6rpx solid #007bff;
}

.data-period {
  width: 120rpx;
  margin-right: 20rpx;
}

.period-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #2c3e50;
}

.data-values {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.value-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.value-label {
  font-size: 24rpx;
  color: #6c757d;
}

.value-amount {
  font-size: 26rpx;
  font-weight: 600;
}

.value-amount.expense {
  color: #dc3545;
}

.value-amount.income {
  color: #28a745;
}

.value-amount.positive {
  color: #28a745;
}

.value-amount.negative {
  color: #dc3545;
}

/* 空状态 */
.empty-data {
  text-align: center;
  padding: 80rpx 20rpx;
}

.empty-data .empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-data .empty-text {
  display: block;
  font-size: 28rpx;
  color: #6c757d;
  margin-bottom: 10rpx;
}

.empty-hint {
  display: block;
  font-size: 24rpx;
  color: #adb5bd;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6c757d;
}
