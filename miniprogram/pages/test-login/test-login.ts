/**
 * 登录测试页面
 * 用于调试登录功能
 */

import authService from '../../services/auth.service';

Page({
  data: {
    isLoading: false,
    loginStatus: '未登录',
    token: '',
    userInfo: null as any,
    logs: [] as string[],
  },

  onLoad() {
    this.checkStatus();
  },

  /**
   * 检查当前状态
   */
  checkStatus() {
    const isLoggedIn = authService.isLoggedIn();
    const token = authService.getToken();
    const userInfo = authService.getUserInfo();
    
    this.setData({
      loginStatus: isLoggedIn ? '已登录' : '未登录',
      token: token || '无',
      userInfo: userInfo || null,
    });
    
    this.addLog(`状态检查: ${isLoggedIn ? '已登录' : '未登录'}`);
  },

  /**
   * 测试快速登录
   */
  async testSilentLogin() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    this.addLog('开始测试快速登录...');
    
    try {
      const result = await authService.silentLogin();
      this.addLog('快速登录成功: ' + JSON.stringify(result));
      this.checkStatus();
    } catch (error) {
      this.addLog('快速登录失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 测试微信授权登录
   */
  testWechatLogin() {
    if (this.data.isLoading) return;

    this.addLog('开始测试微信授权登录...');

    // 必须在用户点击事件的同步回调中调用getUserProfile
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (userProfileRes) => {
        this.addLog('获取用户信息成功: ' + JSON.stringify(userProfileRes.userInfo));
        this.performTestWechatLogin(userProfileRes.userInfo);
      },
      fail: (error) => {
        this.addLog('获取用户信息失败: ' + JSON.stringify(error));
        this.setData({ isLoading: false });
      },
    });
  },

  /**
   * 执行微信登录测试（获取到用户信息后）
   */
  async performTestWechatLogin(userInfo: WechatMiniprogram.UserInfo) {
    this.setData({ isLoading: true });

    try {
      // 模拟authService.wechatLogin的逻辑，但在测试页面中
      this.addLog('获取微信登录code...');

      const loginResult = await new Promise<WechatMiniprogram.LoginSuccessCallbackResult>((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject,
        });
      });

      if (!loginResult.code) {
        throw new Error('获取微信登录code失败');
      }

      this.addLog('获取code成功: ' + loginResult.code);

      // 这里可以调用实际的登录接口进行测试
      this.addLog('微信授权登录测试完成（未实际调用后端接口）');
      this.addLog('用户信息: ' + JSON.stringify(userInfo));

      this.checkStatus();
    } catch (error) {
      this.addLog('微信授权登录失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 测试自动登录
   */
  async testAutoLogin() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    this.addLog('开始测试自动登录...');
    
    try {
      const result = await authService.autoLogin();
      this.addLog('自动登录结果: ' + (result ? '成功' : '失败'));
      this.checkStatus();
    } catch (error) {
      this.addLog('自动登录失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 测试Token刷新
   */
  async testRefreshToken() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    this.addLog('开始测试Token刷新...');
    
    try {
      const result = await authService.refreshToken();
      this.addLog('Token刷新结果: ' + (result ? '成功' : '失败'));
      this.checkStatus();
    } catch (error) {
      this.addLog('Token刷新失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 登出
   */
  logout() {
    authService.logout();
    this.addLog('已登出');
    this.checkStatus();
  },

  /**
   * 清除日志
   */
  clearLogs() {
    this.setData({ logs: [] });
  },

  /**
   * 添加日志
   */
  addLog(message: string) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    
    this.setData({
      logs: [logMessage, ...this.data.logs.slice(0, 19)] // 保留最近20条
    });
    
    console.log(logMessage);
  },

  /**
   * 复制日志
   */
  copyLogs() {
    const logsText = this.data.logs.join('\n');
    wx.setClipboardData({
      data: logsText,
      success: () => {
        wx.showToast({
          title: '日志已复制',
          icon: 'success',
        });
      },
    });
  },
});
