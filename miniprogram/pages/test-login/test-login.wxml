<!--登录测试页面-->
<view class="test-page">
  <view class="header">
    <text class="title">登录功能测试</text>
  </view>

  <!-- 当前状态 -->
  <view class="status-section">
    <view class="status-item">
      <text class="label">登录状态:</text>
      <text class="value {{loginStatus === '已登录' ? 'success' : 'error'}}">{{loginStatus}}</text>
    </view>
    
    <view class="status-item">
      <text class="label">Token:</text>
      <text class="value token">{{token}}</text>
    </view>
    
    <view class="status-item" wx:if="{{userInfo}}">
      <text class="label">用户信息:</text>
      <text class="value">{{userInfo.nickName}}</text>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="button-section">
    <button 
      class="test-btn primary" 
      bindtap="testSilentLogin"
      disabled="{{isLoading}}"
    >
      测试快速登录
    </button>
    
    <button 
      class="test-btn primary" 
      bindtap="testWechatLogin"
      disabled="{{isLoading}}"
    >
      测试微信授权登录
    </button>
    
    <button 
      class="test-btn secondary" 
      bindtap="testAutoLogin"
      disabled="{{isLoading}}"
    >
      测试自动登录
    </button>
    
    <button 
      class="test-btn secondary" 
      bindtap="testRefreshToken"
      disabled="{{isLoading}}"
    >
      测试Token刷新
    </button>
    
    <button 
      class="test-btn danger" 
      bindtap="logout"
      disabled="{{isLoading}}"
    >
      登出
    </button>
  </view>

  <!-- 日志区域 -->
  <view class="log-section">
    <view class="log-header">
      <text class="log-title">测试日志</text>
      <view class="log-actions">
        <button class="mini-btn" bindtap="copyLogs">复制</button>
        <button class="mini-btn" bindtap="clearLogs">清除</button>
      </view>
    </view>
    
    <scroll-view class="log-content" scroll-y>
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        <text class="log-text">{{item}}</text>
      </view>
      
      <view class="log-empty" wx:if="{{logs.length === 0}}">
        <text>暂无日志</text>
      </view>
    </scroll-view>
  </view>
</view>
