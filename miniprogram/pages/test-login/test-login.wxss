/* 登录测试页面样式 */
.test-page {
  padding: 32rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 48rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 状态区域 */
.status-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.value.success {
  color: #52c41a;
  font-weight: bold;
}

.value.error {
  color: #ff4d4f;
  font-weight: bold;
}

.value.token {
  font-family: monospace;
  font-size: 24rpx;
  background: #f5f5f5;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

/* 按钮区域 */
.button-section {
  margin-bottom: 32rpx;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
}

.test-btn:last-child {
  margin-bottom: 0;
}

.test-btn.primary {
  background: #1890ff;
  color: white;
}

.test-btn.secondary {
  background: #52c41a;
  color: white;
}

.test-btn.danger {
  background: #ff4d4f;
  color: white;
}

.test-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.test-btn[disabled] {
  opacity: 0.6;
  transform: none !important;
}

/* 日志区域 */
.log-section {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #fafafa;
  border-bottom: 1rpx solid #e8e8e8;
}

.log-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.log-actions {
  display: flex;
  gap: 16rpx;
}

.mini-btn {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  line-height: 1;
}

.mini-btn:active {
  opacity: 0.8;
}

.log-content {
  height: 600rpx;
  padding: 16rpx 32rpx;
}

.log-item {
  margin-bottom: 16rpx;
  padding: 12rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
  border-left: 4rpx solid #1890ff;
}

.log-text {
  font-size: 24rpx;
  color: #333;
  font-family: monospace;
  line-height: 1.4;
  word-break: break-all;
}

.log-empty {
  text-align: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
}
