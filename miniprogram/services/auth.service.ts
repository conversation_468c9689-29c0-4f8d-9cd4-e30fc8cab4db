/**
 * 用户认证服务
 * 处理微信登录、Token管理等认证相关功能
 */

import apiService, { ApiResponse } from '../utils/api';

// 微信登录请求参数
interface WechatLoginRequest {
  code: string;
  userInfo: {
    nickName: string;
    avatarUrl: string;
    gender: number;
  };
}

// 登录响应数据
interface LoginResponse {
  token: string;
  refreshToken: string;
  expiresIn: number;
  userId: string;
}

// 用户信息接口
interface UserInfo {
  id: string;
  openid: string;
  nickName: string;
  avatarUrl: string;
  gender: number;
  createTime: number;
  updateTime: number;
}

/**
 * 认证服务类
 */
class AuthService {
  /**
   * 静默登录（仅使用code，不获取用户信息）
   */
  async silentLogin(): Promise<LoginResponse> {
    try {
      // 1. 获取微信登录code
      const loginResult = await new Promise<WechatMiniprogram.LoginSuccessCallbackResult>((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject,
        });
      });

      if (!loginResult.code) {
        throw new Error('获取微信登录code失败');
      }

      // 2. 只传code，不传用户信息
      const loginRequest = {
        code: loginResult.code,
        // 不传userInfo字段
      };

      const response = await apiService.post<LoginResponse>('/auth/login', loginRequest, {
        needAuth: false,
        showLoading: false, // 静默登录不显示loading
      });

      if (response.code === 200 && response.data) {
        // 保存Token
        apiService.setToken(response.data.token, response.data.refreshToken);

        // 保存默认用户信息
        wx.setStorageSync('userInfo', {
          nickName: '微信用户',
          avatarUrl: '',
          gender: 0,
        });

        console.log('静默登录成功');
        return response.data;
      } else {
        throw new Error(response.message || '静默登录失败');
      }
    } catch (error) {
      console.error('静默登录失败:', error);
      throw error;
    }
  }

  /**
   * 微信授权登录（使用已获取的用户信息）
   * 由登录页面调用，传入已获取的用户信息
   */
  async wechatLoginWithUserInfo(userInfo: WechatMiniprogram.UserInfo): Promise<LoginResponse> {
    try {
      // 1. 获取微信登录code
      const loginResult = await new Promise<WechatMiniprogram.LoginSuccessCallbackResult>((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject,
        });
      });

      if (!loginResult.code) {
        throw new Error('获取微信登录code失败');
      }

      // 2. 调用后端登录接口
      const loginRequest: WechatLoginRequest = {
        code: loginResult.code,
        userInfo: {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          gender: userInfo.gender,
        },
      };

      const response = await apiService.post<LoginResponse>('/auth/login', loginRequest, {
        needAuth: false,
        showLoading: false, // 由调用方控制loading
      });

      if (response.code === 200 && response.data) {
        // 保存Token
        apiService.setToken(response.data.token, response.data.refreshToken);

        // 保存真实用户信息
        wx.setStorageSync('userInfo', userInfo);

        console.log('微信授权登录成功');
        return response.data;
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('微信授权登录失败:', error);
      throw error;
    }
  }



  /**
   * 刷新Token
   */
  async refreshToken(): Promise<boolean> {
    try {
      const refreshToken = wx.getStorageSync('refresh_token');
      if (!refreshToken) {
        return false;
      }

      const response = await apiService.post<{ token: string; refreshToken: string }>('/auth/refresh', {
        refreshToken,
      }, {
        needAuth: false,
        showLoading: false,
      });

      if (response.code === 200 && response.data) {
        apiService.setToken(response.data.token, response.data.refreshToken);
        return true;
      }
    } catch (error) {
      console.error('刷新Token失败:', error);
    }

    return false;
  }

  /**
   * 自动登录
   * 应用启动时调用，只检查已有Token，不进行网络请求
   */
  async autoLogin(): Promise<boolean> {
    try {
      // 1. 检查是否已有Token
      if (this.isLoggedIn()) {
        console.log('已有Token，自动登录成功');
        return true;
      }

      // 2. 尝试刷新Token（如果有refreshToken）
      const refreshToken = wx.getStorageSync('refresh_token');
      if (refreshToken) {
        console.log('尝试刷新Token');
        const refreshSuccess = await this.refreshToken();
        if (refreshSuccess) {
          console.log('Token刷新成功');
          return true;
        }
      }

      // 3. 没有有效Token，需要用户登录
      console.log('没有有效Token，需要用户登录');
      return false;
    } catch (error) {
      console.error('自动登录检查失败:', error);
      return false;
    }
  }

  /**
   * 检查登录状态
   */
  isLoggedIn(): boolean {
    return apiService.isLoggedIn();
  }

  /**
   * 获取本地存储的用户信息
   */
  getUserInfo(): WechatMiniprogram.UserInfo | null {
    try {
      return wx.getStorageSync('userInfo') || null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 登出
   */
  logout(): void {
    try {
      // 清除Token
      apiService.logout();
      
      // 清除用户信息
      wx.removeStorageSync('userInfo');
      
      // 显示提示
      wx.showToast({
        title: '已退出登录',
        icon: 'success',
        duration: 1500,
      });

      // 跳转到登录页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login',
        });
      }, 1500);
    } catch (error) {
      console.error('登出失败:', error);
    }
  }

  /**
   * 检查并处理登录状态
   * 如果未登录，跳转到登录页
   */
  async checkLoginStatus(): Promise<boolean> {
    if (!this.isLoggedIn()) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login',
          });
        },
      });
      return false;
    }

    return true;
  }



  /**
   * 获取Token
   */
  getToken(): string {
    return apiService.getToken();
  }
}

// 创建认证服务实例
const authService = new AuthService();

export default authService;
export type { LoginResponse, UserInfo, WechatLoginRequest };
