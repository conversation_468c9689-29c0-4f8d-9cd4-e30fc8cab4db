"use strict";
/**
 * 分类管理服务
 * 处理收支分类的增删改查等操作
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const api_1 = __importDefault(require("../utils/api"));
/**
 * 分类服务类
 */
class CategoryService {
    constructor() {
        this.cacheKey = 'cached_categories';
        this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    }
    /**
     * 获取分类列表
     */
    async getCategories(type, useCache = true) {
        try {
            console.log('分类服务 - 请求类型:', type, '使用缓存:', useCache);
            // 尝试从缓存获取
            if (useCache) {
                const cached = this.getCachedCategories(type);
                if (cached) {
                    console.log('分类服务 - 从缓存返回:', cached.length, '个分类');
                    return cached;
                }
            }
            // 如果指定了类型，直接请求该类型的分类
            const params = {};
            if (type) {
                params.type = type;
            }
            console.log('分类服务 - 发起API请求，参数:', params);
            const response = await api_1.default.get('/categories', params, {
                showLoading: false,
                needAuth: true
            });
            console.log('分类服务 - API响应:', response);
            if (response.code === 200 && response.data) {
                // 如果请求的是特定类型，只缓存该类型的数据
                // 如果请求的是所有类型，缓存所有数据
                if (type) {
                    // 特定类型请求，直接返回，不缓存（避免缓存不完整数据）
                    console.log('分类服务 - 返回特定类型分类:', response.data.length, '个');
                    return response.data;
                }
                else {
                    // 所有类型请求，缓存并返回
                    this.setCachedCategories(response.data);
                    console.log('分类服务 - 缓存所有分类:', response.data.length, '个');
                    return response.data;
                }
            }
            else {
                throw new Error(response.message || '获取分类列表失败');
            }
        }
        catch (error) {
            console.error('获取分类列表失败:', error);
            // 如果网络请求失败，尝试返回缓存数据
            const cached = this.getCachedCategories(type);
            if (cached) {
                console.log('分类服务 - 网络失败，从缓存返回:', cached.length, '个分类');
                return cached;
            }
            throw error;
        }
    }
    /**
     * 获取收入分类
     */
    async getIncomeCategories(useCache = true) {
        return this.getCategories('income', useCache);
    }
    /**
     * 获取支出分类
     */
    async getExpenseCategories(useCache = true) {
        return this.getCategories('expense', useCache);
    }
    /**
     * 创建分类
     */
    async createCategory(data) {
        try {
            const response = await api_1.default.post('/categories', data, {
                showLoading: true,
                loadingText: '创建中...',
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                // 清除缓存
                this.clearCache();
                wx.showToast({
                    title: '创建成功',
                    icon: 'success',
                    duration: 1500
                });
                return response.data;
            }
            else {
                throw new Error(response.message || '创建分类失败');
            }
        }
        catch (error) {
            console.error('创建分类失败:', error);
            throw error;
        }
    }
    /**
     * 更新分类
     */
    async updateCategory(id, data) {
        try {
            const response = await api_1.default.put(`/categories/${id}`, data, {
                showLoading: true,
                loadingText: '更新中...',
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                // 清除缓存
                this.clearCache();
                wx.showToast({
                    title: '更新成功',
                    icon: 'success',
                    duration: 1500
                });
                return response.data;
            }
            else {
                throw new Error(response.message || '更新分类失败');
            }
        }
        catch (error) {
            console.error('更新分类失败:', error);
            throw error;
        }
    }
    /**
     * 删除分类
     */
    async deleteCategory(id) {
        try {
            const response = await api_1.default.delete(`/categories/${id}`, {
                showLoading: true,
                loadingText: '删除中...',
                needAuth: true
            });
            if (response.code === 200) {
                // 清除缓存
                this.clearCache();
                wx.showToast({
                    title: '删除成功',
                    icon: 'success',
                    duration: 1500
                });
            }
            else {
                throw new Error(response.message || '删除分类失败');
            }
        }
        catch (error) {
            console.error('删除分类失败:', error);
            throw error;
        }
    }
    /**
     * 获取分类详情
     */
    async getCategoryById(id) {
        try {
            // 先从缓存中查找
            const cached = this.getCachedCategories();
            if (cached) {
                const category = cached.find(cat => cat.id === id);
                if (category) {
                    return category;
                }
            }
            // 如果缓存中没有，重新获取所有分类
            const categories = await this.getCategories(undefined, false);
            return categories.find(cat => cat.id === id) || null;
        }
        catch (error) {
            console.error('获取分类详情失败:', error);
            return null;
        }
    }
    /**
     * 获取默认分类
     */
    async getDefaultCategories(type) {
        try {
            const categories = await this.getCategories(type);
            return categories.filter(category => category.isDefault);
        }
        catch (error) {
            console.error('获取默认分类失败:', error);
            return [];
        }
    }
    /**
     * 搜索分类
     */
    async searchCategories(keyword, type) {
        try {
            const categories = await this.getCategories(type);
            if (!keyword.trim()) {
                return categories;
            }
            const lowerKeyword = keyword.toLowerCase();
            return categories.filter(category => category.name.toLowerCase().includes(lowerKeyword));
        }
        catch (error) {
            console.error('搜索分类失败:', error);
            return [];
        }
    }
    /**
     * 获取缓存的分类数据
     */
    getCachedCategories(type) {
        try {
            const cached = wx.getStorageSync(this.cacheKey);
            if (!cached || !cached.data || !cached.timestamp) {
                return null;
            }
            // 检查缓存是否过期
            if (Date.now() - cached.timestamp > this.cacheExpiry) {
                wx.removeStorageSync(this.cacheKey);
                return null;
            }
            const categories = cached.data;
            // 按类型过滤
            if (type) {
                return categories.filter(category => category.type === type);
            }
            return categories;
        }
        catch (error) {
            console.error('获取缓存分类失败:', error);
            return null;
        }
    }
    /**
     * 设置缓存的分类数据
     */
    setCachedCategories(categories) {
        try {
            wx.setStorageSync(this.cacheKey, {
                data: categories,
                timestamp: Date.now()
            });
        }
        catch (error) {
            console.error('设置缓存分类失败:', error);
        }
    }
    /**
     * 预加载所有分类数据到缓存
     */
    async preloadCategories() {
        try {
            console.log('预加载所有分类数据');
            // 请求所有分类（不指定type），这样会缓存完整数据
            await this.getCategories(undefined, false);
            console.log('预加载分类完成');
        }
        catch (error) {
            console.error('预加载分类失败:', error);
        }
    }
    /**
     * 清除分类缓存
     */
    clearCache() {
        try {
            wx.removeStorageSync(this.cacheKey);
            console.log('分类缓存已清除');
        }
        catch (error) {
            console.error('清除分类缓存失败:', error);
        }
    }
}
// 创建分类服务实例
const categoryService = new CategoryService();
exports.default = categoryService;
