"use strict";
/**
 * 统计分析服务
 * 处理收支统计、图表数据等分析功能
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const api_1 = __importDefault(require("../utils/api"));
const helpers_1 = require("../utils/helpers");
/**
 * 统计服务类
 */
class StatisticsService {
    constructor() {
        this.cachePrefix = 'stats_cache_';
        this.cacheExpiry = 2 * 60 * 1000; // 2分钟缓存
    }
    /**
     * 获取收支概览
     */
    async getOverview(startDate, endDate, period = 'month') {
        try {
            // 如果没有指定日期，使用当前月份
            if (!startDate || !endDate) {
                const currentMonth = (0, helpers_1.getCurrentMonth)();
                startDate = currentMonth.start;
                endDate = currentMonth.end;
            }
            const cacheKey = `${this.cachePrefix}overview_${startDate}_${endDate}_${period}`;
            // 尝试从缓存获取
            const cached = this.getCachedData(cacheKey);
            if (cached) {
                return cached;
            }
            const params = {
                startDate,
                endDate,
                period
            };
            const response = await api_1.default.get('/statistics/overview', params, {
                showLoading: false,
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                // 缓存数据
                this.setCachedData(cacheKey, response.data);
                return response.data;
            }
            else {
                throw new Error(response.message || '获取概览数据失败');
            }
        }
        catch (error) {
            console.error('获取概览数据失败:', error);
            throw error;
        }
    }
    /**
     * 获取月度统计
     */
    async getMonthlyStatistics(year, month) {
        try {
            // 如果没有指定年月，使用当前月份
            if (!year || !month) {
                const currentMonth = (0, helpers_1.getCurrentMonth)();
                year = currentMonth.year;
                month = currentMonth.month;
            }
            const cacheKey = `${this.cachePrefix}monthly_${year}_${month}`;
            // 尝试从缓存获取
            const cached = this.getCachedData(cacheKey);
            if (cached) {
                return cached;
            }
            const params = { year, month };
            const response = await api_1.default.get('/statistics/monthly', params, {
                showLoading: false,
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                // 缓存数据
                this.setCachedData(cacheKey, response.data);
                return response.data;
            }
            else {
                throw new Error(response.message || '获取月度统计失败');
            }
        }
        catch (error) {
            console.error('获取月度统计失败:', error);
            throw error;
        }
    }
    /**
     * 获取分类统计
     */
    async getCategoryStatistics(startDate, endDate, type) {
        try {
            // 如果没有指定日期，使用当前月份
            if (!startDate || !endDate) {
                const currentMonth = (0, helpers_1.getCurrentMonth)();
                startDate = currentMonth.start;
                endDate = currentMonth.end;
            }
            const cacheKey = `${this.cachePrefix}category_${startDate}_${endDate}_${type || 'all'}`;
            // 尝试从缓存获取
            const cached = this.getCachedData(cacheKey);
            if (cached) {
                return cached;
            }
            const params = {
                startDate,
                endDate,
                type: type || 'expense' // 确保总是传递type参数
            };
            const response = await api_1.default.get('/statistics/categories', params, {
                showLoading: false,
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                // 转换字段名并计算平均金额
                const convertedData = response.data.map((item) => ({
                    categoryId: item.category_id,
                    categoryName: item.category_name,
                    categoryIcon: item.category_icon,
                    categoryColor: item.category_color,
                    amount: item.amount,
                    transactionCount: item.transaction_count,
                    percentage: item.percentage,
                    avgAmount: item.transaction_count > 0 ? item.amount / item.transaction_count : 0
                }));
                // 缓存数据
                this.setCachedData(cacheKey, convertedData);
                return convertedData;
            }
            else {
                throw new Error(response.message || '获取分类统计失败');
            }
        }
        catch (error) {
            console.error('获取分类统计失败:', error);
            throw error;
        }
    }
    /**
     * 获取趋势分析
     */
    async getTrendAnalysis(period = 'month', count = 12, type = 'expense') {
        try {
            const cacheKey = `${this.cachePrefix}trend_${period}_${count}_${type}`;
            // 尝试从缓存获取
            const cached = this.getCachedData(cacheKey);
            if (cached) {
                return cached;
            }
            const params = {
                period,
                count,
                type
            };
            const response = await api_1.default.get('/statistics/trend', params, {
                showLoading: false,
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                // 缓存数据
                this.setCachedData(cacheKey, response.data);
                return response.data;
            }
            else {
                throw new Error(response.message || '获取趋势分析失败');
            }
        }
        catch (error) {
            console.error('获取趋势分析失败:', error);
            throw error;
        }
    }
    /**
     * 获取当前月份概览（用于首页）
     */
    async getCurrentMonthOverview() {
        const currentMonth = (0, helpers_1.getCurrentMonth)();
        return this.getOverview(currentMonth.start, currentMonth.end);
    }
    /**
     * 获取支出分类统计（用于饼图）
     */
    async getExpenseCategoryStats(startDate, endDate) {
        return this.getCategoryStatistics(startDate, endDate, 'expense');
    }
    /**
     * 获取收入分类统计
     */
    async getIncomeCategoryStats(startDate, endDate) {
        return this.getCategoryStatistics(startDate, endDate, 'income');
    }
    /**
     * 获取月度支出趋势（用于折线图）
     */
    async getMonthlyExpenseTrend(count = 6) {
        return this.getTrendAnalysis('month', count, 'expense');
    }
    /**
     * 获取年度对比数据
     */
    async getYearlyComparison(year) {
        try {
            const currentYearData = [];
            const previousYearData = [];
            // 获取当前年份和上一年份的月度数据
            for (let month = 1; month <= 12; month++) {
                try {
                    const [current, previous] = await Promise.all([
                        this.getMonthlyStatistics(year, month),
                        this.getMonthlyStatistics(year - 1, month)
                    ]);
                    currentYearData.push(current);
                    previousYearData.push(previous);
                }
                catch (error) {
                    console.error(`获取${year}年${month}月数据失败:`, error);
                }
            }
            return {
                currentYear: currentYearData,
                previousYear: previousYearData
            };
        }
        catch (error) {
            console.error('获取年度对比数据失败:', error);
            throw error;
        }
    }
    /**
     * 获取缓存数据
     */
    getCachedData(key) {
        try {
            const cached = wx.getStorageSync(key);
            if (!cached || !cached.data || !cached.timestamp) {
                return null;
            }
            // 检查缓存是否过期
            if (Date.now() - cached.timestamp > this.cacheExpiry) {
                wx.removeStorageSync(key);
                return null;
            }
            return cached.data;
        }
        catch (error) {
            console.error('获取缓存数据失败:', error);
            return null;
        }
    }
    /**
     * 设置缓存数据
     */
    setCachedData(key, data) {
        try {
            wx.setStorageSync(key, {
                data,
                timestamp: Date.now()
            });
        }
        catch (error) {
            console.error('设置缓存数据失败:', error);
        }
    }
    /**
     * 清除所有统计缓存
     */
    clearCache() {
        try {
            const info = wx.getStorageInfoSync();
            const keysToRemove = info.keys.filter(key => key.startsWith(this.cachePrefix));
            keysToRemove.forEach(key => {
                wx.removeStorageSync(key);
            });
        }
        catch (error) {
            console.error('清除统计缓存失败:', error);
        }
    }
    /**
     * 预加载统计数据
     */
    async preloadStatistics() {
        try {
            // 预加载当前月份的概览和统计数据
            await Promise.all([
                this.getCurrentMonthOverview(),
                this.getExpenseCategoryStats(),
                this.getMonthlyExpenseTrend()
            ]);
        }
        catch (error) {
            console.error('预加载统计数据失败:', error);
        }
    }
    /**
     * 获取用户统计信息
     */
    async getUserStatistics() {
        try {
            const response = await api_1.default.get('/statistics/user-stats', {}, {
                showLoading: false,
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                return response.data;
            }
            else {
                throw new Error(response.message || '获取用户统计失败');
            }
        }
        catch (error) {
            console.error('获取用户统计失败:', error);
            throw error;
        }
    }
}
// 创建统计服务实例
const statisticsService = new StatisticsService();
exports.default = statisticsService;
