"use strict";
/**
 * 交易记录服务
 * 处理收支记录的增删改查等操作
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const api_1 = __importDefault(require("../utils/api"));
/**
 * 交易记录服务类
 */
class TransactionService {
    /**
     * 获取交易记录列表
     */
    async getTransactions(query = {}) {
        try {
            const response = await api_1.default.get('/transactions', query, {
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                // 转换字段名从下划线到驼峰
                const convertedData = {
                    ...response.data,
                    list: response.data.list.map((item) => this.convertTransactionFields(item))
                };
                return convertedData;
            }
            else {
                throw new Error(response.message || '获取交易记录失败');
            }
        }
        catch (error) {
            console.error('获取交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 获取单个交易记录
     */
    async getTransaction(id) {
        try {
            const response = await api_1.default.get(`/transactions/${id}`, undefined, {
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                return this.convertTransactionFields(response.data);
            }
            else {
                throw new Error(response.message || '获取交易记录失败');
            }
        }
        catch (error) {
            console.error('获取交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 创建交易记录
     */
    async createTransaction(data) {
        try {
            const response = await api_1.default.post('/transactions', data, {
                showLoading: true,
                loadingText: '保存中...',
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                wx.showToast({
                    title: '记账成功',
                    icon: 'success',
                    duration: 1500,
                });
                return response.data;
            }
            else {
                throw new Error(response.message || '创建交易记录失败');
            }
        }
        catch (error) {
            console.error('创建交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 更新交易记录
     */
    async updateTransaction(id, data) {
        try {
            const response = await api_1.default.put(`/transactions/${id}`, data, {
                showLoading: true,
                loadingText: '更新中...',
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                wx.showToast({
                    title: '更新成功',
                    icon: 'success',
                    duration: 1500,
                });
                return response.data;
            }
            else {
                throw new Error(response.message || '更新交易记录失败');
            }
        }
        catch (error) {
            console.error('更新交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 删除交易记录
     */
    async deleteTransaction(id) {
        try {
            const response = await api_1.default.delete(`/transactions/${id}`, {
                showLoading: true,
                loadingText: '删除中...',
                needAuth: true
            });
            if (response.code === 200) {
                wx.showToast({
                    title: '删除成功',
                    icon: 'success',
                    duration: 1500,
                });
            }
            else {
                throw new Error(response.message || '删除交易记录失败');
            }
        }
        catch (error) {
            console.error('删除交易记录失败:', error);
            throw error;
        }
    }
    /**
     * 转换字段名从下划线到驼峰
     */
    convertTransactionFields(item) {
        // 处理iOS兼容的时间格式
        const convertDateString = (dateStr) => {
            if (!dateStr)
                return Date.now();
            // 将 "2025-07-29 08:12:27" 转换为 "2025/07/29 08:12:27" (iOS兼容)
            const iOSCompatibleDate = dateStr.replace(/-/g, '/');
            return new Date(iOSCompatibleDate).getTime();
        };
        return {
            id: item.id,
            type: item.type,
            amount: item.amount,
            categoryId: item.category_id,
            categoryName: item.category_name,
            categoryIcon: item.category_icon,
            categoryColor: item.category_color,
            description: item.description,
            date: item.date,
            createTime: convertDateString(item.created_at),
            updateTime: convertDateString(item.updated_at),
            imageUrl: item.image_url
        };
    }
    /**
     * 批量操作交易记录
     */
    async batchOperation(data) {
        try {
            const response = await api_1.default.post('/transactions/batch', data, {
                showLoading: true,
                loadingText: '处理中...',
                needAuth: true
            });
            if (response.code === 200 && response.data) {
                const { successCount, failCount } = response.data;
                wx.showToast({
                    title: `成功${successCount}条${failCount > 0 ? `，失败${failCount}条` : ''}`,
                    icon: failCount > 0 ? 'none' : 'success',
                    duration: 2000,
                });
                return response.data;
            }
            else {
                throw new Error(response.message || '批量操作失败');
            }
        }
        catch (error) {
            console.error('批量操作失败:', error);
            throw error;
        }
    }
    /**
     * 获取最近交易记录（用于首页展示）
     */
    async getRecentTransactions(limit = 5) {
        try {
            const response = await this.getTransactions({
                page: 1,
                pageSize: limit,
            });
            return response.list;
        }
        catch (error) {
            console.error('获取最近交易记录失败:', error);
            return [];
        }
    }
    /**
     * 按日期分组获取交易记录
     */
    async getTransactionsByDateGroup(query = {}) {
        try {
            const response = await this.getTransactions({
                ...query,
                pageSize: 1000, // 获取更多数据用于分组
            });
            // 按日期分组
            const groupedTransactions = {};
            response.list.forEach(transaction => {
                const date = transaction.date;
                if (!groupedTransactions[date]) {
                    groupedTransactions[date] = [];
                }
                groupedTransactions[date].push(transaction);
            });
            return groupedTransactions;
        }
        catch (error) {
            console.error('获取分组交易记录失败:', error);
            return {};
        }
    }
    /**
     * 搜索交易记录
     */
    async searchTransactions(keyword, filters = {}) {
        try {
            const response = await this.getTransactions({
                ...filters,
                keyword,
                pageSize: 100,
            });
            return response.list;
        }
        catch (error) {
            console.error('搜索交易记录失败:', error);
            return [];
        }
    }
    /**
     * 获取指定日期范围的交易记录
     */
    async getTransactionsByDateRange(startDate, endDate) {
        try {
            const response = await this.getTransactions({
                startDate,
                endDate,
                pageSize: 1000,
            });
            return response.list;
        }
        catch (error) {
            console.error('获取日期范围交易记录失败:', error);
            return [];
        }
    }
    /**
     * 获取指定分类的交易记录
     */
    async getTransactionsByCategory(categoryId, query = {}) {
        try {
            const response = await this.getTransactions({
                ...query,
                categoryId,
                pageSize: 1000,
            });
            return response.list;
        }
        catch (error) {
            console.error('获取分类交易记录失败:', error);
            return [];
        }
    }
}
// 创建交易记录服务实例
const transactionService = new TransactionService();
exports.default = transactionService;
