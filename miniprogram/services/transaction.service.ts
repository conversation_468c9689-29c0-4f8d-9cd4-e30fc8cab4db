/**
 * 交易记录服务
 * 处理收支记录的增删改查等操作
 */

import apiService, { ApiResponse, PaginatedResponse } from '../utils/api';

// 交易记录接口
interface Transaction {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  categoryId: string;
  categoryName?: string;
  categoryIcon?: string;
  categoryColor?: string;
  description: string;
  date: string;
  createTime: number;
  updateTime: number;
  imageUrl?: string;
}

// 创建交易记录请求参数
interface CreateTransactionRequest {
  type: 'income' | 'expense';
  amount: number;
  categoryId: string;
  description: string;
  date: string;
  imageUrl?: string;
}

// 更新交易记录请求参数
interface UpdateTransactionRequest {
  type?: 'income' | 'expense';
  amount?: number;
  categoryId?: string;
  description?: string;
  date?: string;
  imageUrl?: string;
}

// 交易记录查询参数
interface TransactionQuery {
  page?: number;
  pageSize?: number;
  startDate?: string;
  endDate?: string;
  type?: 'income' | 'expense';
  categoryId?: string;
  minAmount?: number;
  maxAmount?: number;
  keyword?: string;
}

// 批量操作请求参数
interface BatchOperationRequest {
  operation: 'delete' | 'updateCategory';
  ids: string[];
  data?: any;
}

/**
 * 交易记录服务类
 */
class TransactionService {
  /**
   * 获取交易记录列表
   */
  async getTransactions(query: TransactionQuery = {}): Promise<PaginatedResponse<Transaction>> {
    try {
      const response = await apiService.get<PaginatedResponse<any>>('/transactions', query, {
        needAuth: true
      });

      if (response.code === 200 && response.data) {
        // 转换字段名从下划线到驼峰
        const convertedData = {
          ...response.data,
          list: response.data.list.map((item: any) => this.convertTransactionFields(item))
        };
        return convertedData;
      } else {
        throw new Error(response.message || '获取交易记录失败');
      }
    } catch (error) {
      console.error('获取交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个交易记录
   */
  async getTransaction(id: string): Promise<Transaction> {
    try {
      const response = await apiService.get<any>(`/transactions/${id}`, undefined, {
        needAuth: true
      });

      if (response.code === 200 && response.data) {
        return this.convertTransactionFields(response.data);
      } else {
        throw new Error(response.message || '获取交易记录失败');
      }
    } catch (error) {
      console.error('获取交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 创建交易记录
   */
  async createTransaction(data: CreateTransactionRequest): Promise<Transaction> {
    try {
      const response = await apiService.post<Transaction>('/transactions', data, {
        showLoading: true,
        loadingText: '保存中...',
        needAuth: true
      });
      
      if (response.code === 200 && response.data) {
        wx.showToast({
          title: '记账成功',
          icon: 'success',
          duration: 1500,
        });
        return response.data;
      } else {
        throw new Error(response.message || '创建交易记录失败');
      }
    } catch (error) {
      console.error('创建交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新交易记录
   */
  async updateTransaction(id: string, data: UpdateTransactionRequest): Promise<Transaction> {
    try {
      const response = await apiService.put<Transaction>(`/transactions/${id}`, data, {
        showLoading: true,
        loadingText: '更新中...',
        needAuth: true
      });
      
      if (response.code === 200 && response.data) {
        wx.showToast({
          title: '更新成功',
          icon: 'success',
          duration: 1500,
        });
        return response.data;
      } else {
        throw new Error(response.message || '更新交易记录失败');
      }
    } catch (error) {
      console.error('更新交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 删除交易记录
   */
  async deleteTransaction(id: string): Promise<void> {
    try {
      const response = await apiService.delete(`/transactions/${id}`, {
        showLoading: true,
        loadingText: '删除中...',
        needAuth: true
      });
      
      if (response.code === 200) {
        wx.showToast({
          title: '删除成功',
          icon: 'success',
          duration: 1500,
        });
      } else {
        throw new Error(response.message || '删除交易记录失败');
      }
    } catch (error) {
      console.error('删除交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 转换字段名从下划线到驼峰
   */
  private convertTransactionFields(item: any): Transaction {
    // 处理iOS兼容的时间格式
    const convertDateString = (dateStr: string): number => {
      if (!dateStr) return Date.now();
      // 将 "2025-07-29 08:12:27" 转换为 "2025/07/29 08:12:27" (iOS兼容)
      const iOSCompatibleDate = dateStr.replace(/-/g, '/');
      return new Date(iOSCompatibleDate).getTime();
    };

    return {
      id: item.id,
      type: item.type,
      amount: item.amount,
      categoryId: item.category_id,
      categoryName: item.category_name,
      categoryIcon: item.category_icon,
      categoryColor: item.category_color,
      description: item.description,
      date: item.date,
      createTime: convertDateString(item.created_at),
      updateTime: convertDateString(item.updated_at),
      imageUrl: item.image_url
    } as Transaction;
  }

  /**
   * 批量操作交易记录
   */
  async batchOperation(data: BatchOperationRequest): Promise<{ successCount: number; failCount: number }> {
    try {
      const response = await apiService.post<{ successCount: number; failCount: number }>('/transactions/batch', data, {
        showLoading: true,
        loadingText: '处理中...',
        needAuth: true
      });
      
      if (response.code === 200 && response.data) {
        const { successCount, failCount } = response.data;
        wx.showToast({
          title: `成功${successCount}条${failCount > 0 ? `，失败${failCount}条` : ''}`,
          icon: failCount > 0 ? 'none' : 'success',
          duration: 2000,
        });
        return response.data;
      } else {
        throw new Error(response.message || '批量操作失败');
      }
    } catch (error) {
      console.error('批量操作失败:', error);
      throw error;
    }
  }

  /**
   * 获取最近交易记录（用于首页展示）
   */
  async getRecentTransactions(limit: number = 5): Promise<Transaction[]> {
    try {
      const response = await this.getTransactions({
        page: 1,
        pageSize: limit,
      });
      
      return response.list;
    } catch (error) {
      console.error('获取最近交易记录失败:', error);
      return [];
    }
  }

  /**
   * 按日期分组获取交易记录
   */
  async getTransactionsByDateGroup(query: TransactionQuery = {}): Promise<Record<string, Transaction[]>> {
    try {
      const response = await this.getTransactions({
        ...query,
        pageSize: 1000, // 获取更多数据用于分组
      });
      
      // 按日期分组
      const groupedTransactions: Record<string, Transaction[]> = {};
      response.list.forEach(transaction => {
        const date = transaction.date;
        if (!groupedTransactions[date]) {
          groupedTransactions[date] = [];
        }
        groupedTransactions[date].push(transaction);
      });
      
      return groupedTransactions;
    } catch (error) {
      console.error('获取分组交易记录失败:', error);
      return {};
    }
  }

  /**
   * 搜索交易记录
   */
  async searchTransactions(keyword: string, filters: Partial<TransactionQuery> = {}): Promise<Transaction[]> {
    try {
      const response = await this.getTransactions({
        ...filters,
        keyword,
        pageSize: 100,
      });
      
      return response.list;
    } catch (error) {
      console.error('搜索交易记录失败:', error);
      return [];
    }
  }

  /**
   * 获取指定日期范围的交易记录
   */
  async getTransactionsByDateRange(startDate: string, endDate: string): Promise<Transaction[]> {
    try {
      const response = await this.getTransactions({
        startDate,
        endDate,
        pageSize: 1000,
      });
      
      return response.list;
    } catch (error) {
      console.error('获取日期范围交易记录失败:', error);
      return [];
    }
  }

  /**
   * 获取指定分类的交易记录
   */
  async getTransactionsByCategory(categoryId: string, query: Partial<TransactionQuery> = {}): Promise<Transaction[]> {
    try {
      const response = await this.getTransactions({
        ...query,
        categoryId,
        pageSize: 1000,
      });
      
      return response.list;
    } catch (error) {
      console.error('获取分类交易记录失败:', error);
      return [];
    }
  }
}

// 创建交易记录服务实例
const transactionService = new TransactionService();

export default transactionService;
export type { 
  Transaction, 
  CreateTransactionRequest, 
  UpdateTransactionRequest, 
  TransactionQuery, 
  BatchOperationRequest 
};
