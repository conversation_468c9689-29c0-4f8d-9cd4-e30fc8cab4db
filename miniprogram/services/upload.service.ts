/**
 * 文件上传服务
 * 处理图片上传、文件管理等功能
 */

import apiService from '../utils/api';
import { formatFileSize, getFileExtension } from '../utils/helpers';

// 上传响应接口
interface UploadResponse {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}

// 上传配置接口
interface UploadConfig {
  maxSize?: number; // 最大文件大小（字节）
  allowedTypes?: string[]; // 允许的文件类型
  quality?: number; // 图片质量（0-1）
  compress?: boolean; // 是否压缩
}

/**
 * 上传服务类
 */
class UploadService {
  private defaultConfig: UploadConfig = {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
    quality: 0.8,
    compress: true
  };

  /**
   * 选择并上传图片
   */
  async chooseAndUploadImage(config?: Partial<UploadConfig>): Promise<UploadResponse> {
    try {
      // 选择图片
      const tempFilePath = await this.chooseImage(config);
      
      // 上传图片
      return await this.uploadImage(tempFilePath, config);
    } catch (error) {
      console.error('选择并上传图片失败:', error);
      throw error;
    }
  }

  /**
   * 选择图片
   */
  async chooseImage(config?: Partial<UploadConfig>): Promise<string> {
    const finalConfig = { ...this.defaultConfig, ...config };

    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count: 1,
        sizeType: finalConfig.compress ? ['compressed'] : ['original'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          
          // 验证文件
          this.validateFile(tempFilePath, finalConfig)
            .then(() => resolve(tempFilePath))
            .catch(reject);
        },
        fail: (error) => {
          console.error('选择图片失败:', error);
          reject(new Error('选择图片失败'));
        }
      });
    });
  }

  /**
   * 上传图片
   */
  async uploadImage(filePath: string, config?: Partial<UploadConfig>): Promise<UploadResponse> {
    const finalConfig = { ...this.defaultConfig, ...config };

    try {
      // 显示上传进度
      wx.showLoading({
        title: '上传中...',
        mask: true
      });

      // 如果需要压缩，先压缩图片
      let uploadPath = filePath;
      if (finalConfig.compress && finalConfig.quality) {
        uploadPath = await this.compressImage(filePath, finalConfig.quality);
      }

      // 上传文件
      const uploadResult = await this.uploadFile(uploadPath, 'image');
      
      wx.hideLoading();
      
      wx.showToast({
        title: '上传成功',
        icon: 'success',
        duration: 1500
      });

      return uploadResult;
    } catch (error) {
      wx.hideLoading();
      console.error('上传图片失败:', error);
      throw error;
    }
  }

  /**
   * 上传文件到服务器
   */
  private async uploadFile(filePath: string, type: string): Promise<UploadResponse> {
    return new Promise((resolve, reject) => {
      const token = apiService.getToken();
      
      wx.uploadFile({
        url: `http://127.0.0.1:3000/api/upload/image`,
        filePath,
        name: 'image', // 修复：使用后端期望的字段名
        formData: {
          type
        },
        header: {
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);

            if (data.code === 200 && data.data) {
              // 将相对URL转换为API代理URL
              const result = { ...data.data };
              if (result.url && !result.url.startsWith('http')) {
                // 将 /uploads/xxx 转换为 /api/image/xxx
                const imagePath = result.url.replace('/uploads/', '');
                result.url = `http://127.0.0.1:3000/api/image/${imagePath}`;
              }
              resolve(result);
            } else {
              reject(new Error(data.message || '上传失败'));
            }
          } catch (error) {
            reject(new Error('解析上传响应失败'));
          }
        },
        fail: (error) => {
          console.error('上传文件失败:', error);
          reject(new Error('上传文件失败'));
        }
      });
    });
  }

  /**
   * 压缩图片
   */
  private async compressImage(filePath: string, quality: number): Promise<string> {
    return new Promise((resolve, reject) => {
      wx.compressImage({
        src: filePath,
        quality: Math.floor(quality * 100),
        success: (res) => {
          resolve(res.tempFilePath);
        },
        fail: (error) => {
          console.error('压缩图片失败:', error);
          // 压缩失败时返回原图片
          resolve(filePath);
        }
      });
    });
  }

  /**
   * 验证文件
   */
  private async validateFile(filePath: string, config: UploadConfig): Promise<void> {
    try {
      // 获取文件信息
      const fileInfo = await this.getFileInfo(filePath);
      
      // 检查文件大小
      if (config.maxSize && fileInfo.size > config.maxSize) {
        throw new Error(`文件大小不能超过 ${formatFileSize(config.maxSize)}`);
      }
      
      // 检查文件类型
      if (config.allowedTypes && config.allowedTypes.length > 0) {
        const extension = getFileExtension(filePath).toLowerCase();
        const mimeType = this.getMimeTypeByExtension(extension);
        
        if (!config.allowedTypes.includes(mimeType)) {
          throw new Error('不支持的文件类型');
        }
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取文件信息
   */
  private async getFileInfo(filePath: string): Promise<{ size: number }> {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath,
        success: (res) => {
          resolve({ size: res.size });
        },
        fail: (error) => {
          console.error('获取文件信息失败:', error);
          reject(new Error('获取文件信息失败'));
        }
      });
    });
  }

  /**
   * 根据文件扩展名获取MIME类型
   */
  private getMimeTypeByExtension(extension: string): string {
    const mimeTypes: Record<string, string> = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'bmp': 'image/bmp'
    };
    
    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * 预览图片
   */
  previewImage(current: string, urls: string[] = []): void {
    wx.previewImage({
      current,
      urls: urls.length > 0 ? urls : [current]
    });
  }

  /**
   * 保存图片到相册
   */
  async saveImageToPhotosAlbum(filePath: string): Promise<void> {
    try {
      // 检查权限
      const authResult = await this.checkPhotosAlbumAuth();
      if (!authResult) {
        throw new Error('需要相册权限才能保存图片');
      }

      // 保存图片
      await new Promise<void>((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath,
          success: () => {
            wx.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 1500
            });
            resolve();
          },
          fail: (error) => {
            console.error('保存图片失败:', error);
            reject(new Error('保存图片失败'));
          }
        });
      });
    } catch (error) {
      console.error('保存图片到相册失败:', error);
      throw error;
    }
  }

  /**
   * 检查相册权限
   */
  private async checkPhotosAlbumAuth(): Promise<boolean> {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.writePhotosAlbum'] === false) {
            // 用户拒绝过权限，引导用户手动开启
            wx.showModal({
              title: '提示',
              content: '需要相册权限才能保存图片，请在设置中开启',
              showCancel: false,
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting();
                }
                resolve(false);
              }
            });
          } else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {
            // 用户未授权过，请求授权
            wx.authorize({
              scope: 'scope.writePhotosAlbum',
              success: () => resolve(true),
              fail: () => resolve(false)
            });
          } else {
            // 用户已授权
            resolve(true);
          }
        },
        fail: () => resolve(false)
      });
    });
  }

  /**
   * 删除临时文件
   */
  removeTempFile(filePath: string): void {
    wx.removeSavedFile({
      filePath,
      success: () => {
        console.log('删除临时文件成功:', filePath);
      },
      fail: (error) => {
        console.error('删除临时文件失败:', error);
      }
    });
  }

  /**
   * 获取本地文件列表
   */
  async getSavedFileList(): Promise<WechatMiniprogram.GetSavedFileListSuccessCallbackResult['fileList']> {
    return new Promise((resolve, reject) => {
      wx.getSavedFileList({
        success: (res) => {
          resolve(res.fileList);
        },
        fail: (error) => {
          console.error('获取本地文件列表失败:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * 清理过期的临时文件
   */
  async cleanupTempFiles(): Promise<void> {
    try {
      const fileList = await this.getSavedFileList();
      const now = Date.now();
      const expireTime = 24 * 60 * 60 * 1000; // 24小时

      fileList.forEach(file => {
        if (now - file.createTime > expireTime) {
          this.removeTempFile(file.filePath);
        }
      });
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }
  }
}

// 创建上传服务实例
const uploadService = new UploadService();

export default uploadService;
export type { UploadResponse, UploadConfig };
