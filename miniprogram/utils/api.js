"use strict";
/**
 * API 接口调用工具类
 * 统一处理网络请求、错误处理、Token管理等
 */
Object.defineProperty(exports, "__esModule", { value: true });
const simple_config_1 = require("./simple-config");
/**
 * API 请求工具类
 */
class ApiService {
    constructor() {
        this.token = '';
        this.refreshToken = '';
        this.loadTokenFromStorage();
    }
    /**
     * 从本地存储加载Token
     */
    loadTokenFromStorage() {
        try {
            this.token = wx.getStorageSync('access_token') || '';
            this.refreshToken = wx.getStorageSync('refresh_token') || '';
        }
        catch (error) {
            console.error('加载Token失败:', error);
        }
    }
    /**
     * 保存Token到本地存储
     */
    saveTokenToStorage(token, refreshToken) {
        try {
            wx.setStorageSync('access_token', token);
            wx.setStorageSync('refresh_token', refreshToken);
            this.token = token;
            this.refreshToken = refreshToken;
        }
        catch (error) {
            console.error('保存Token失败:', error);
        }
    }
    /**
     * 清除Token
     */
    clearToken() {
        try {
            wx.removeStorageSync('access_token');
            wx.removeStorageSync('refresh_token');
            this.token = '';
            this.refreshToken = '';
        }
        catch (error) {
            console.error('清除Token失败:', error);
        }
    }
    /**
     * 刷新Token
     */
    async refreshAccessToken() {
        if (!this.refreshToken) {
            return false;
        }
        try {
            const response = await this.request({
                url: '/api/auth/refresh',
                method: 'POST',
                data: { refreshToken: this.refreshToken },
                needAuth: false,
                showLoading: false,
            });
            if (response.code === 200 && response.data) {
                this.saveTokenToStorage(response.data.token, response.data.refreshToken);
                return true;
            }
        }
        catch (error) {
            console.error('刷新Token失败:', error);
        }
        return false;
    }
    /**
     * 统一请求方法
     */
    async request(config) {
        const { url, method = 'GET', data, header = {}, needAuth = true, showLoading = true, loadingText = '加载中...', } = config;
        // 显示加载提示
        if (showLoading) {
            wx.showLoading({
                title: loadingText,
                mask: true,
            });
        }
        // 构建请求头
        const requestHeader = {
            'Content-Type': 'application/json',
            ...header,
        };
        // 添加认证头
        if (needAuth && this.token) {
            requestHeader['Authorization'] = `Bearer ${this.token}`;
        }
        try {
            const response = await new Promise((resolve, reject) => {
                wx.request({
                    url: `${simple_config_1.config.api.baseUrl}${url}`,
                    method,
                    data,
                    header: requestHeader,
                    timeout: simple_config_1.config.api.timeout,
                    success: (res) => {
                        // 接受200-299范围内的HTTP状态码
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(res.data);
                        }
                        else {
                            reject(new Error(`HTTP ${res.statusCode}: ${res.errMsg}`));
                        }
                    },
                    fail: (err) => {
                        reject(new Error(err.errMsg || '网络请求失败'));
                    },
                });
            });
            // 隐藏加载提示
            if (showLoading) {
                wx.hideLoading();
            }
            // 处理业务错误
            if (response.code !== 200) {
                // Token过期，尝试刷新
                if (response.code === 401 && needAuth) {
                    const refreshSuccess = await this.refreshAccessToken();
                    if (refreshSuccess) {
                        // 重新发起请求
                        return this.request(config);
                    }
                    else {
                        // 刷新失败，跳转到登录页
                        this.clearToken();
                        wx.navigateTo({
                            url: '/pages/login/login',
                        });
                        throw new Error('登录已过期，请重新登录');
                    }
                }
                throw new Error(response.message || '请求失败');
            }
            return response;
        }
        catch (error) {
            // 隐藏加载提示
            if (showLoading) {
                wx.hideLoading();
            }
            // 显示错误提示
            wx.showToast({
                title: error instanceof Error ? error.message : '网络异常',
                icon: 'none',
                duration: 2000,
            });
            throw error;
        }
    }
    /**
     * GET 请求
     */
    async get(url, params, config) {
        const queryString = params ? this.buildQueryString(params) : '';
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request({
            url: fullUrl,
            method: 'GET',
            ...config,
        });
    }
    /**
     * POST 请求
     */
    async post(url, data, config) {
        return this.request({
            url,
            method: 'POST',
            data,
            ...config,
        });
    }
    /**
     * PUT 请求
     */
    async put(url, data, config) {
        return this.request({
            url,
            method: 'PUT',
            data,
            ...config,
        });
    }
    /**
     * DELETE 请求
     */
    async delete(url, config) {
        return this.request({
            url,
            method: 'DELETE',
            ...config,
        });
    }
    /**
     * 构建查询字符串
     */
    buildQueryString(params) {
        const queryParts = [];
        Object.keys(params).forEach(key => {
            const value = params[key];
            if (value !== undefined && value !== null && value !== '') {
                queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
            }
        });
        return queryParts.join('&');
    }
    /**
     * 设置Token
     */
    setToken(token, refreshToken) {
        this.saveTokenToStorage(token, refreshToken);
    }
    /**
     * 获取Token
     */
    getToken() {
        return this.token;
    }
    /**
     * 检查是否已登录
     */
    isLoggedIn() {
        return !!this.token;
    }
    /**
     * 登出
     */
    logout() {
        this.clearToken();
    }
}
// 创建API服务实例
const apiService = new ApiService();
exports.default = apiService;
