/**
 * API 接口调用工具类
 * 统一处理网络请求、错误处理、Token管理等
 */

import { config as appConfig } from './simple-config';

// 通用响应接口（与后端保持一致）
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T | null;
}

// 分页响应接口
interface PaginatedResponse<T> {
  total: number;
  page: number;
  pageSize: number;
  list: T[];
}

// 请求配置接口
interface RequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  header?: Record<string, string>;
  needAuth?: boolean;
  showLoading?: boolean;
  loadingText?: string;
}

/**
 * API 请求工具类
 */
class ApiService {
  private token: string = '';
  private refreshToken: string = '';

  constructor() {
    this.loadTokenFromStorage();
  }

  /**
   * 从本地存储加载Token
   */
  private loadTokenFromStorage(): void {
    try {
      this.token = wx.getStorageSync('access_token') || '';
      this.refreshToken = wx.getStorageSync('refresh_token') || '';
    } catch (error) {
      console.error('加载Token失败:', error);
    }
  }

  /**
   * 保存Token到本地存储
   */
  private saveTokenToStorage(token: string, refreshToken: string): void {
    try {
      wx.setStorageSync('access_token', token);
      wx.setStorageSync('refresh_token', refreshToken);
      this.token = token;
      this.refreshToken = refreshToken;
    } catch (error) {
      console.error('保存Token失败:', error);
    }
  }

  /**
   * 清除Token
   */
  private clearToken(): void {
    try {
      wx.removeStorageSync('access_token');
      wx.removeStorageSync('refresh_token');
      this.token = '';
      this.refreshToken = '';
    } catch (error) {
      console.error('清除Token失败:', error);
    }
  }

  /**
   * 刷新Token
   */
  private async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) {
      return false;
    }

    try {
      const response = await this.request<{
        token: string;
        refreshToken: string;
      }>({
        url: '/api/auth/refresh',
        method: 'POST',
        data: { refreshToken: this.refreshToken },
        needAuth: false,
        showLoading: false,
      });

      if (response.code === 200 && response.data) {
        this.saveTokenToStorage(response.data.token, response.data.refreshToken);
        return true;
      }
    } catch (error) {
      console.error('刷新Token失败:', error);
    }

    return false;
  }

  /**
   * 统一请求方法
   */
  async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    const {
      url,
      method = 'GET',
      data,
      header = {},
      needAuth = true,
      showLoading = true,
      loadingText = '加载中...',
    } = config;

    // 显示加载提示
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true,
      });
    }

    // 构建请求头
    const requestHeader: Record<string, string> = {
      'Content-Type': 'application/json',
      ...header,
    };

    // 添加认证头
    if (needAuth && this.token) {
      requestHeader['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await new Promise<ApiResponse<T>>((resolve, reject) => {
        wx.request({
          url: `${appConfig.api.baseUrl}${url}`,
          method,
          data,
          header: requestHeader,
          timeout: appConfig.api.timeout,
          success: (res) => {
            // 接受200-299范围内的HTTP状态码
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(res.data as ApiResponse<T>);
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${res.errMsg}`));
            }
          },
          fail: (err) => {
            reject(new Error(err.errMsg || '网络请求失败'));
          },
        });
      });

      // 隐藏加载提示
      if (showLoading) {
        wx.hideLoading();
      }

      // 处理业务错误
      if (response.code !== 200) {
        // Token过期，尝试刷新
        if (response.code === 401 && needAuth) {
          const refreshSuccess = await this.refreshAccessToken();
          if (refreshSuccess) {
            // 重新发起请求
            return this.request(config);
          } else {
            // 刷新失败，跳转到登录页
            this.clearToken();
            wx.navigateTo({
              url: '/pages/login/login',
            });
            throw new Error('登录已过期，请重新登录');
          }
        }

        throw new Error(response.message || '请求失败');
      }

      return response;
    } catch (error) {
      // 隐藏加载提示
      if (showLoading) {
        wx.hideLoading();
      }

      // 显示错误提示
      wx.showToast({
        title: error instanceof Error ? error.message : '网络异常',
        icon: 'none',
        duration: 2000,
      });

      throw error;
    }
  }

  /**
   * GET 请求
   */
  async get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    const queryString = params ? this.buildQueryString(params) : '';
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request<T>({
      url: fullUrl,
      method: 'GET',
      ...config,
    });
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config,
    });
  }

  /**
   * PUT 请求
   */
  async put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config,
    });
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      ...config,
    });
  }

  /**
   * 构建查询字符串
   */
  private buildQueryString(params: Record<string, any>): string {
    const queryParts: string[] = [];
    
    Object.keys(params).forEach(key => {
      const value = params[key];
      if (value !== undefined && value !== null && value !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    });
    
    return queryParts.join('&');
  }

  /**
   * 设置Token
   */
  setToken(token: string, refreshToken: string): void {
    this.saveTokenToStorage(token, refreshToken);
  }

  /**
   * 获取Token
   */
  getToken(): string {
    return this.token;
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    return !!this.token;
  }

  /**
   * 登出
   */
  logout(): void {
    this.clearToken();
  }
}

// 创建API服务实例
const apiService = new ApiService();

export default apiService;
export type { ApiResponse, PaginatedResponse };
