"use strict";
/**
 * 小程序环境配置管理
 * 根据不同环境提供不同的配置
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.utils = exports.environment = exports.config = void 0;
// 开发环境配置
const developmentConfig = {
    api: {
        baseUrl: 'http://192.168.134.61:3000/api',
        timeout: 10000,
        retryCount: 3,
    },
    wechat: {
        appId: 'wx4e10169147d3138b', // 开发环境使用测试AppID
    },
    features: {
        enableDebug: true,
        enableMock: false,
        enableErrorReport: false,
    },
    business: {
        maxFileSize: 5 * 1024 * 1024, // 5MB
        maxFiles: 5,
        pageSize: 20,
    },
};
// 生产环境配置
const productionConfig = {
    api: {
        baseUrl: 'http://192.168.134.61:3000/api', // 替换为实际的生产环境域名
        timeout: 15000,
        retryCount: 2,
    },
    wechat: {
        appId: 'wx4e10169147d3138b', // 替换为实际的微信小程序AppID
    },
    features: {
        enableDebug: false,
        enableMock: false,
        enableErrorReport: true,
    },
    business: {
        maxFileSize: 5 * 1024 * 1024, // 5MB
        maxFiles: 5,
        pageSize: 20,
    },
};
/**
 * 获取当前环境
 * 在小程序中，可以通过版本类型判断环境
 */
function getCurrentEnvironment() {
    try {
        // 获取小程序账号信息
        const accountInfo = wx.getAccountInfoSync();
        // 根据版本类型判断环境
        if (accountInfo && accountInfo.miniProgram) {
            switch (accountInfo.miniProgram.envVersion) {
                case 'develop': // 开发版
                case 'trial': // 体验版
                    return 'development';
                case 'release': // 正式版
                    return 'production';
                default:
                    return 'development';
            }
        }
    }
    catch (error) {
        console.warn('获取小程序环境信息失败，使用开发环境配置:', error);
    }
    // 默认返回开发环境
    return 'development';
}
/**
 * 获取当前环境配置
 */
function getConfig() {
    const env = getCurrentEnvironment();
    console.log('[CONFIG] 当前环境:', env);
    let selectedConfig;
    switch (env) {
        case 'production':
            selectedConfig = productionConfig;
            break;
        case 'development':
        default:
            selectedConfig = developmentConfig;
            break;
    }
    console.log('[CONFIG] API配置:', selectedConfig.api);
    return selectedConfig;
}
// 导出配置
exports.config = getConfig();
exports.environment = getCurrentEnvironment();
// 工具函数
exports.utils = {
    /**
     * 是否为开发环境
     */
    isDevelopment() {
        return exports.environment === 'development';
    },
    /**
     * 是否为生产环境
     */
    isProduction() {
        return exports.environment === 'production';
    },
    /**
     * 调试日志（仅在开发环境输出）
     */
    debugLog(...args) {
        if (exports.config.features.enableDebug) {
            console.log('[DEBUG]', ...args);
        }
    },
    /**
     * 错误日志
     */
    errorLog(...args) {
        console.error('[ERROR]', ...args);
        // 生产环境可以上报错误
        if (exports.config.features.enableErrorReport && this.isProduction()) {
            // TODO: 集成错误上报服务
            // this.reportError(args);
        }
    },
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    /**
     * 检查文件大小是否超限
     */
    isFileSizeValid(size) {
        return size <= exports.config.business.maxFileSize;
    },
    /**
     * 获取最大文件大小提示文本
     */
    getMaxFileSizeText() {
        return this.formatFileSize(exports.config.business.maxFileSize);
    },
};
// 默认导出配置
exports.default = exports.config;
