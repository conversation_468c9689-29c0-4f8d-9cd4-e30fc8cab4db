/**
 * 小程序环境配置管理
 * 根据不同环境提供不同的配置
 */

// 环境类型
type Environment = 'development' | 'production';

// 配置接口
interface AppConfig {
  // API配置
  api: {
    baseUrl: string;
    timeout: number;
    retryCount: number;
  };
  
  // 微信小程序配置
  wechat: {
    appId: string;
  };
  
  // 功能开关
  features: {
    enableDebug: boolean;
    enableMock: boolean;
    enableErrorReport: boolean;
  };
  
  // 业务配置
  business: {
    maxFileSize: number; // 文件上传最大大小（字节）
    maxFiles: number;    // 最大文件数量
    pageSize: number;    // 默认分页大小
  };
}

// 开发环境配置
const developmentConfig: AppConfig = {
  api: {
    baseUrl: 'http://192.168.134.61:3000/api',
    timeout: 10000,
    retryCount: 3,
  },
  wechat: {
    appId: 'wx4e10169147d3138b', // 开发环境使用测试AppID
  },
  features: {
    enableDebug: true,
    enableMock: false,
    enableErrorReport: false,
  },
  business: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 5,
    pageSize: 20,
  },
};

// 生产环境配置
const productionConfig: AppConfig = {
  api: {
    baseUrl: 'http://192.168.134.61:3000/api', // 替换为实际的生产环境域名
    timeout: 15000,
    retryCount: 2,
  },
  wechat: {
    appId: 'wx4e10169147d3138b', // 替换为实际的微信小程序AppID
  },
  features: {
    enableDebug: false,
    enableMock: false,
    enableErrorReport: true,
  },
  business: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 5,
    pageSize: 20,
  },
};

/**
 * 获取当前环境
 * 在小程序中，可以通过版本类型判断环境
 */
function getCurrentEnvironment(): Environment {
  try {
    // 获取小程序账号信息
    const accountInfo = wx.getAccountInfoSync();

    // 根据版本类型判断环境
    if (accountInfo && accountInfo.miniProgram) {
      switch (accountInfo.miniProgram.envVersion) {
        case 'develop':   // 开发版
        case 'trial':     // 体验版
          return 'development';
        case 'release':   // 正式版
          return 'production';
        default:
          return 'development';
      }
    }
  } catch (error) {
    console.warn('获取小程序环境信息失败，使用开发环境配置:', error);
  }

  // 默认返回开发环境
  return 'development';
}

/**
 * 获取当前环境配置
 */
function getConfig(): AppConfig {
  const env = getCurrentEnvironment();

  console.log('[CONFIG] 当前环境:', env);

  let selectedConfig: AppConfig;
  switch (env) {
    case 'production':
      selectedConfig = productionConfig;
      break;
    case 'development':
    default:
      selectedConfig = developmentConfig;
      break;
  }

  console.log('[CONFIG] API配置:', selectedConfig.api);
  return selectedConfig;
}

// 导出配置
export const config = getConfig();
export const environment = getCurrentEnvironment();

// 导出类型
export type { AppConfig, Environment };

// 工具函数
export const utils = {
  /**
   * 是否为开发环境
   */
  isDevelopment(): boolean {
    return environment === 'development';
  },

  /**
   * 是否为生产环境
   */
  isProduction(): boolean {
    return environment === 'production';
  },

  /**
   * 调试日志（仅在开发环境输出）
   */
  debugLog(...args: any[]): void {
    if (config.features.enableDebug) {
      console.log('[DEBUG]', ...args);
    }
  },

  /**
   * 错误日志
   */
  errorLog(...args: any[]): void {
    console.error('[ERROR]', ...args);
    
    // 生产环境可以上报错误
    if (config.features.enableErrorReport && this.isProduction()) {
      // TODO: 集成错误上报服务
      // this.reportError(args);
    }
  },

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 检查文件大小是否超限
   */
  isFileSizeValid(size: number): boolean {
    return size <= config.business.maxFileSize;
  },

  /**
   * 获取最大文件大小提示文本
   */
  getMaxFileSizeText(): string {
    return this.formatFileSize(config.business.maxFileSize);
  },
};

// 默认导出配置
export default config;
