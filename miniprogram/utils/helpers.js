"use strict";
/**
 * 通用工具函数
 * 提供日期处理、数字格式化、验证等常用功能
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniqueArray = exports.getRandomElement = exports.safeJsonParse = exports.getContrastColor = exports.isValidUrl = exports.formatFileSize = exports.getFileExtension = exports.deepClone = exports.generateId = exports.throttle = exports.debounce = exports.validateAmount = exports.getCurrentMonth = exports.getMonthRange = exports.getRelativeTime = exports.formatDate = exports.formatAmount = void 0;
/**
 * 格式化金额显示
 * @param amount 金额
 * @param showSymbol 是否显示货币符号
 * @param precision 小数位数
 */
function formatAmount(amount, showSymbol = true, precision = 2) {
    if (isNaN(amount)) {
        return showSymbol ? '¥0.00' : '0.00';
    }
    const formatted = amount.toFixed(precision);
    const parts = formatted.split('.');
    // 添加千分位分隔符
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    const result = parts.join('.');
    return showSymbol ? `¥${result}` : result;
}
exports.formatAmount = formatAmount;
/**
 * 格式化日期
 * @param date 日期对象、时间戳或日期字符串
 * @param format 格式化模式
 */
function formatDate(date, format = 'YYYY-MM-DD') {
    let dateObj;
    if (typeof date === 'number') {
        dateObj = new Date(date);
    }
    else if (typeof date === 'string') {
        dateObj = new Date(date);
    }
    else {
        dateObj = date;
    }
    if (isNaN(dateObj.getTime())) {
        return '';
    }
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');
    return format
        .replace('YYYY', String(year))
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}
exports.formatDate = formatDate;
/**
 * 获取相对时间描述
 * @param date 日期
 */
function getRelativeTime(date) {
    const now = new Date();
    const targetDate = new Date(date);
    const diffMs = now.getTime() - targetDate.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    if (diffDays === 0) {
        return '今天';
    }
    else if (diffDays === 1) {
        return '昨天';
    }
    else if (diffDays === 2) {
        return '前天';
    }
    else if (diffDays < 7) {
        return `${diffDays}天前`;
    }
    else if (diffDays < 30) {
        const weeks = Math.floor(diffDays / 7);
        return `${weeks}周前`;
    }
    else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        return `${months}个月前`;
    }
    else {
        const years = Math.floor(diffDays / 365);
        return `${years}年前`;
    }
}
exports.getRelativeTime = getRelativeTime;
/**
 * 获取月份的第一天和最后一天
 * @param year 年份
 * @param month 月份（1-12）
 */
function getMonthRange(year, month) {
    const start = new Date(year, month - 1, 1);
    const end = new Date(year, month, 0);
    return {
        start: formatDate(start, 'YYYY-MM-DD'),
        end: formatDate(end, 'YYYY-MM-DD')
    };
}
exports.getMonthRange = getMonthRange;
/**
 * 获取当前月份信息
 */
function getCurrentMonth() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const range = getMonthRange(year, month);
    return {
        year,
        month,
        ...range
    };
}
exports.getCurrentMonth = getCurrentMonth;
/**
 * 验证金额格式
 * @param amount 金额字符串
 */
function validateAmount(amount) {
    if (!amount || amount.trim() === '') {
        return { isValid: false, message: '请输入金额' };
    }
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) {
        return { isValid: false, message: '请输入有效的数字' };
    }
    if (numAmount <= 0) {
        return { isValid: false, message: '金额必须大于0' };
    }
    if (numAmount > 999999999) {
        return { isValid: false, message: '金额不能超过999,999,999' };
    }
    // 检查小数位数
    const decimalPart = amount.split('.')[1];
    if (decimalPart && decimalPart.length > 2) {
        return { isValid: false, message: '最多支持2位小数' };
    }
    return { isValid: true };
}
exports.validateAmount = validateAmount;
/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 */
function debounce(func, delay) {
    let timeoutId;
    return (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func(...args), delay);
    };
}
exports.debounce = debounce;
/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 */
function throttle(func, delay) {
    let lastCall = 0;
    return (...args) => {
        const now = Date.now();
        if (now - lastCall >= delay) {
            lastCall = now;
            func(...args);
        }
    };
}
exports.throttle = throttle;
/**
 * 生成唯一ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
exports.generateId = generateId;
/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    if (typeof obj === 'object') {
        const cloned = {};
        Object.keys(obj).forEach(key => {
            cloned[key] = deepClone(obj[key]);
        });
        return cloned;
    }
    return obj;
}
exports.deepClone = deepClone;
/**
 * 获取文件扩展名
 * @param filename 文件名
 */
function getFileExtension(filename) {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}
exports.getFileExtension = getFileExtension;
/**
 * 格式化文件大小
 * @param bytes 字节数
 */
function formatFileSize(bytes) {
    if (bytes === 0)
        return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
exports.formatFileSize = formatFileSize;
/**
 * 检查是否为有效的URL
 * @param url URL字符串
 */
function isValidUrl(url) {
    try {
        // 简单的URL验证
        return url.startsWith('http://') || url.startsWith('https://');
    }
    catch {
        return false;
    }
}
exports.isValidUrl = isValidUrl;
/**
 * 获取颜色的对比色（黑色或白色）
 * @param hexColor 十六进制颜色值
 */
function getContrastColor(hexColor) {
    // 移除 # 符号
    const color = hexColor.replace('#', '');
    // 转换为 RGB
    const r = parseInt(color.substr(0, 2), 16);
    const g = parseInt(color.substr(2, 2), 16);
    const b = parseInt(color.substr(4, 2), 16);
    // 计算亮度
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    // 返回对比色
    return brightness > 128 ? '#000000' : '#FFFFFF';
}
exports.getContrastColor = getContrastColor;
/**
 * 安全的JSON解析
 * @param jsonString JSON字符串
 * @param defaultValue 默认值
 */
function safeJsonParse(jsonString, defaultValue) {
    try {
        return JSON.parse(jsonString);
    }
    catch {
        return defaultValue;
    }
}
exports.safeJsonParse = safeJsonParse;
/**
 * 获取数组中的随机元素
 * @param array 数组
 */
function getRandomElement(array) {
    if (array.length === 0)
        return undefined;
    return array[Math.floor(Math.random() * array.length)];
}
exports.getRandomElement = getRandomElement;
/**
 * 数组去重
 * @param array 数组
 * @param key 对象数组的去重键
 */
function uniqueArray(array, key) {
    if (!key) {
        return [...new Set(array)];
    }
    const seen = new Set();
    return array.filter(item => {
        const value = item[key];
        if (seen.has(value)) {
            return false;
        }
        seen.add(value);
        return true;
    });
}
exports.uniqueArray = uniqueArray;
