"use strict";
/**
 * 简化的小程序配置管理
 * 避免复杂的环境检测，使用更可靠的配置方式
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.utils = exports.environment = exports.config = void 0;
// 环境检测函数
function getEnvironment() {
    try {
        // 尝试获取小程序账号信息
        const accountInfo = wx.getAccountInfoSync();
        if (accountInfo && accountInfo.miniProgram && accountInfo.miniProgram.envVersion) {
            // 正式版为生产环境
            return accountInfo.miniProgram.envVersion === 'release' ? 'production' : 'development';
        }
    }
    catch (error) {
        console.warn('无法获取小程序环境信息，默认使用开发环境');
    }
    // 默认开发环境
    return 'development';
}
// 基础配置
const baseConfig = {
    wechat: {
        appId: 'wx4e10169147d3138b', // 统一使用真实AppID
    },
    features: {
        enableDebug: true,
        enableMock: false,
        enableErrorReport: false,
    },
    business: {
        maxFileSize: 5 * 1024 * 1024, // 5MB
        maxFiles: 5,
        pageSize: 20,
    },
};
// 开发环境配置
const developmentConfig = {
    ...baseConfig,
    api: {
        baseUrl: 'http://***************:3000/api',
        timeout: 10000,
        retryCount: 3,
    },
    features: {
        ...baseConfig.features,
        enableDebug: true,
    },
};
// 生产环境配置
const productionConfig = {
    ...baseConfig,
    api: {
        baseUrl: 'https://your-domain.com/api', // 生产环境需要替换为真实域名
        timeout: 15000,
        retryCount: 2,
    },
    features: {
        ...baseConfig.features,
        enableDebug: false,
        enableErrorReport: true,
    },
};
// 获取当前配置
function getConfig() {
    const env = getEnvironment();
    console.log('[CONFIG] 当前环境:', env);
    const config = env === 'production' ? productionConfig : developmentConfig;
    console.log('[CONFIG] API配置:', config.api);
    return config;
}
// 导出配置
exports.config = getConfig();
exports.environment = getEnvironment();
// 工具函数
exports.utils = {
    isDevelopment() {
        return exports.environment === 'development';
    },
    isProduction() {
        return exports.environment === 'production';
    },
    debugLog(...args) {
        if (exports.config.features.enableDebug) {
            console.log('[DEBUG]', ...args);
        }
    },
    errorLog(...args) {
        console.error('[ERROR]', ...args);
    },
    formatFileSize(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    isFileSizeValid(size) {
        return size <= exports.config.business.maxFileSize;
    },
    getMaxFileSizeText() {
        return this.formatFileSize(exports.config.business.maxFileSize);
    },
};
exports.default = exports.config;
