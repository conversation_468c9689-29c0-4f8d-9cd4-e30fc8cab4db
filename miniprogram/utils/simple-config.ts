/**
 * 简化的小程序配置管理
 * 避免复杂的环境检测，使用更可靠的配置方式
 */

// 配置接口
interface AppConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retryCount: number;
  };
  wechat: {
    appId: string;
  };
  features: {
    enableDebug: boolean;
    enableMock: boolean;
    enableErrorReport: boolean;
  };
  business: {
    maxFileSize: number;
    maxFiles: number;
    pageSize: number;
  };
}

// 环境检测函数
function getEnvironment(): 'development' | 'production' {
  try {
    // 尝试获取小程序账号信息
    const accountInfo = wx.getAccountInfoSync();
    if (accountInfo && accountInfo.miniProgram && accountInfo.miniProgram.envVersion) {
      // 正式版为生产环境
      return accountInfo.miniProgram.envVersion === 'release' ? 'production' : 'development';
    }
  } catch (error) {
    console.warn('无法获取小程序环境信息，默认使用开发环境');
  }
  
  // 默认开发环境
  return 'development';
}

// 基础配置
const baseConfig = {
  wechat: {
    appId: 'wx4e10169147d3138b', // 统一使用真实AppID
  },
  features: {
    enableDebug: true,
    enableMock: false,
    enableErrorReport: false,
  },
  business: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 5,
    pageSize: 20,
  },
};

// 开发环境配置
const developmentConfig: AppConfig = {
  ...baseConfig,
  api: {
    baseUrl: 'http://***************:3000/api',
    timeout: 10000,
    retryCount: 3,
  },
  features: {
    ...baseConfig.features,
    enableDebug: true,
  },
};

// 生产环境配置
const productionConfig: AppConfig = {
  ...baseConfig,
  api: {
    baseUrl: 'https://your-domain.com/api', // 生产环境需要替换为真实域名
    timeout: 15000,
    retryCount: 2,
  },
  features: {
    ...baseConfig.features,
    enableDebug: false,
    enableErrorReport: true,
  },
};

// 获取当前配置
function getConfig(): AppConfig {
  const env = getEnvironment();
  console.log('[CONFIG] 当前环境:', env);
  
  const config = env === 'production' ? productionConfig : developmentConfig;
  console.log('[CONFIG] API配置:', config.api);
  
  return config;
}

// 导出配置
export const config = getConfig();
export const environment = getEnvironment();

// 工具函数
export const utils = {
  isDevelopment(): boolean {
    return environment === 'development';
  },

  isProduction(): boolean {
    return environment === 'production';
  },

  debugLog(...args: any[]): void {
    if (config.features.enableDebug) {
      console.log('[DEBUG]', ...args);
    }
  },

  errorLog(...args: any[]): void {
    console.error('[ERROR]', ...args);
  },

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  isFileSizeValid(size: number): boolean {
    return size <= config.business.maxFileSize;
  },

  getMaxFileSizeText(): string {
    return this.formatFileSize(config.business.maxFileSize);
  },
};

export default config;
