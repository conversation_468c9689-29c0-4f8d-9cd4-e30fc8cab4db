/**
 * 配置测试文件
 * 用于验证配置是否正确加载
 */

// 简化的配置对象，避免复杂的环境检测
const testConfig = {
  api: {
    baseUrl: 'http://127.0.0.1:3000/api',
    timeout: 10000,
    retryCount: 3,
  },
  wechat: {
    appId: 'wx4e10169147d3138b',
  },
  features: {
    enableDebug: true,
    enableMock: false,
    enableErrorReport: false,
  },
  business: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 5,
    pageSize: 20,
  },
};

console.log('[TEST-CONFIG] 配置对象:', testConfig);
console.log('[TEST-CONFIG] API基础URL:', testConfig.api.baseUrl);

module.exports = {
  config: testConfig
};
