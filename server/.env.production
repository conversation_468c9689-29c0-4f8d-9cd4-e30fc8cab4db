# 生产环境配置
NODE_ENV=production
PORT=3000

# 数据库配置
DATABASE_PATH=/var/data/accounting.db

# JWT配置（生产环境必须使用强密码）
JWT_SECRET=your-very-strong-production-jwt-secret-at-least-32-characters-long
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-very-strong-production-refresh-secret-at-least-32-characters-long
JWT_REFRESH_EXPIRES_IN=30d

# 微信小程序配置（替换为真实的AppID和AppSecret）
WECHAT_APP_ID=your-real-wechat-app-id
WECHAT_APP_SECRET=your-real-wechat-app-secret

# 文件上传配置
UPLOAD_DIR=/var/uploads
MAX_FILE_SIZE=5242880
MAX_FILES=5

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/logs/accounting-app.log

# CORS配置（限制为微信小程序域名）
CORS_ORIGIN=https://servicewechat.com

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 数据库配置（如果使用MySQL）
# DATABASE_TYPE=mysql
# DATABASE_HOST=localhost
# DATABASE_PORT=3306
# DATABASE_NAME=accounting_app
# DATABASE_USER=accounting_user
# DATABASE_PASSWORD=your-secure-database-password

# Redis配置（如果使用Redis缓存）
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your-redis-password

# 监控配置
# SENTRY_DSN=your-sentry-dsn-for-error-tracking

# SSL配置
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/private.key
