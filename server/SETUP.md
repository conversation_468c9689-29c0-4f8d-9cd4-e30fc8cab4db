# 记账小程序服务端本地运行指南

## 📋 环境要求

### 系统要求
- **操作系统**: macOS 10.15+, Windows 10+, Ubuntu 18.04+
- **内存**: 最少 4GB RAM
- **磁盘空间**: 最少 2GB 可用空间

### 软件依赖
- **Node.js**: >= 16.0.0 (推荐 18.x LTS)
- **npm**: >= 8.0.0 (或 yarn >= 1.22.0)
- **Git**: >= 2.20.0

## 🚀 快速开始

### 1. 环境准备

#### 1.1 安装 Node.js

**方法一：官方安装包（推荐）**
```bash
# 访问 https://nodejs.org 下载 LTS 版本
# 或使用包管理器安装

# macOS (使用 Homebrew)
brew install node@18

# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Windows (使用 Chocolatey)
choco install nodejs
```

**方法二：使用 NVM（推荐开发者）**
```bash
# 安装 NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 重新加载终端配置
source ~/.bashrc  # 或 source ~/.zshrc

# 安装并使用 Node.js 18
nvm install 18
nvm use 18
nvm alias default 18
```

#### 1.2 验证安装
```bash
node --version    # 应显示 v18.x.x
npm --version     # 应显示 8.x.x 或更高
```

### 2. 项目设置

#### 2.1 克隆项目（如果从Git获取）
```bash
git clone <repository-url>
cd jh-wx-app/server
```

#### 2.2 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

#### 2.3 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
nano .env  # 或使用其他编辑器
```

**环境变量配置说明：**
```env
# 服务器配置
PORT=3000                    # 服务端口
NODE_ENV=development         # 运行环境

# 数据库配置
DATABASE_PATH=./data/accounting.db  # SQLite数据库文件路径

# JWT配置（请修改为强密码）
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=30d

# 微信小程序配置（开发环境可使用测试值）
WECHAT_APP_ID=test-app-id
WECHAT_APP_SECRET=test-app-secret

# 文件上传配置
UPLOAD_DIR=./uploads         # 文件上传目录
MAX_FILE_SIZE=5242880        # 最大文件大小（5MB）
MAX_FILES=5                  # 最大文件数量

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS配置
CORS_ORIGIN=*

# 安全配置
RATE_LIMIT_WINDOW_MS=900000  # 15分钟
RATE_LIMIT_MAX_REQUESTS=100
```

### 3. 数据库设置

#### 3.1 SQLite 配置（默认，无需额外安装）
```bash
# 创建数据目录
mkdir -p data

# SQLite 数据库会在首次运行时自动创建
# 数据库文件位置：./data/accounting.db
```

#### 3.2 MySQL 配置（可选，生产环境推荐）

**安装 MySQL：**
```bash
# macOS
brew install mysql
brew services start mysql

# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql

# Windows
# 下载并安装 MySQL Community Server
# https://dev.mysql.com/downloads/mysql/
```

**创建数据库：**
```sql
-- 登录 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE accounting_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'accounting_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON accounting_app.* TO 'accounting_user'@'localhost';
FLUSH PRIVILEGES;
```

**更新环境配置：**
```env
# 如果使用 MySQL，更新 .env 文件
DATABASE_TYPE=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=accounting_app
DATABASE_USER=accounting_user
DATABASE_PASSWORD=your_password
```

### 4. 启动服务

#### 4.1 开发模式启动
```bash
# 方法一：使用 npm scripts
npm run dev

# 方法二：使用启动脚本（推荐）
# macOS/Linux
chmod +x scripts/start.sh
./scripts/start.sh dev

# Windows
scripts\start.bat dev

# 方法三：使用简化服务器（如果遇到 TypeScript 问题）
node start-simple.js
```

#### 4.2 生产模式启动
```bash
# 构建项目
npm run build

# 启动生产服务
npm start

# 或使用 PM2（推荐生产环境）
npm install -g pm2
pm2 start dist/app.js --name accounting-server
```

### 5. 验证安装

#### 5.1 健康检查
```bash
# 检查服务状态
curl http://localhost:3000/health

# 预期响应
{
  "status": "ok",
  "timestamp": "2023-12-01T12:00:00.000Z",
  "version": "1.0.0",
  "message": "记账小程序后端服务运行正常"
}
```

#### 5.2 API 测试
```bash
# 查看 API 文档
curl http://localhost:3000/api

# 测试登录接口
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"code":"test-code","userInfo":{"nickName":"测试用户"}}'
```

## 🛠️ 开发工具配置

### 1. VS Code 配置

**推荐扩展：**
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json"
  ]
}
```

**工作区设置：**
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

### 2. 调试配置

**VS Code 调试配置（.vscode/launch.json）：**
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Server",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/server/src/app.ts",
      "outFiles": ["${workspaceFolder}/server/dist/**/*.js"],
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector"
    }
  ]
}
```

## 🔧 常见问题解决

### 1. Node.js 版本问题

**问题**: `dyld: Library not loaded: libicui18n.73.dylib`

**解决方案**:
```bash
# 方法一：使用 NVM 切换版本
nvm install 18
nvm use 18

# 方法二：重新安装 Node.js
brew uninstall node
brew install node@18
brew link node@18 --force

# 方法三：创建符号链接（macOS）
sudo ln -sf /usr/local/opt/icu4c/lib/libicuuc.74.2.dylib /usr/local/opt/icu4c/lib/libicuuc.73.dylib
sudo ln -sf /usr/local/opt/icu4c/lib/libicui18n.74.2.dylib /usr/local/opt/icu4c/lib/libicui18n.73.dylib
```

### 2. 端口占用问题

**问题**: `Error: listen EADDRINUSE :::3000`

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :3000

# 杀死进程
kill -9 <PID>

# 或修改端口
echo "PORT=3001" >> .env
```

### 3. 权限问题

**问题**: `EACCES: permission denied`

**解决方案**:
```bash
# 创建必要目录并设置权限
mkdir -p data uploads logs
chmod 755 data uploads logs

# 修复 npm 权限（如果需要）
sudo chown -R $(whoami) ~/.npm
```

### 4. TypeScript 编译问题

**问题**: TypeScript 编译错误

**解决方案**:
```bash
# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 检查 TypeScript 配置
npx tsc --noEmit

# 使用简化服务器（临时方案）
node start-simple.js
```

## 📊 性能监控

### 1. 日志查看
```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log

# 使用 PM2 查看日志
pm2 logs accounting-server
```

### 2. 性能监控
```bash
# 安装监控工具
npm install -g clinic

# 性能分析
clinic doctor -- node dist/app.js
clinic bubbleprof -- node dist/app.js
```

## 🚀 部署准备

### 1. 生产环境配置
```bash
# 设置生产环境变量
export NODE_ENV=production

# 使用强密码
export JWT_SECRET="your-very-strong-production-secret"
export JWT_REFRESH_SECRET="your-very-strong-refresh-secret"
```

### 2. 进程管理
```bash
# 安装 PM2
npm install -g pm2

# 创建 PM2 配置文件
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'accounting-server',
    script: 'dist/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production'
    }
  }]
};
EOF

# 启动生产服务
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

---

**🎉 恭喜！服务端已成功运行！**

访问 http://localhost:3000/health 验证服务状态。

如有问题，请查看日志文件或参考常见问题解决方案。
