# 服务端接口单元测试指南

## 📋 测试概览

本项目为记账小程序后端的所有接口编写了完整的单元测试，确保接口功能正确性和稳定性。

### 测试覆盖的接口模块

1. **认证接口** (`auth.test.ts`)
   - 微信登录 (支持静默登录和授权登录)
   - Token刷新
   - 登出
   - Token验证

2. **分类接口** (`category.test.ts`)
   - 获取分类列表
   - 创建分类
   - 更新分类
   - 删除分类
   - 获取分类详情

3. **交易记录接口** (`transaction.test.ts`)
   - 获取交易记录列表 (支持分页、筛选)
   - 创建交易记录
   - 获取交易记录详情
   - 更新交易记录
   - 删除交易记录
   - 批量操作

4. **统计接口** (`statistics.test.ts`)
   - 统计概览
   - 月度统计
   - 分类统计
   - 趋势分析
   - 对比分析

5. **文件上传接口** (`upload.test.ts`)
   - 单文件上传
   - 多文件上传
   - 文件列表查询
   - 文件删除

6. **健康检查接口** (`health.test.ts`)
   - 基础健康检查
   - API健康检查
   - 性能测试

## 🚀 运行测试

### 安装依赖

```bash
cd server
npm install
```

### 运行所有测试

```bash
npm test
```

### 运行特定测试文件

```bash
# 运行认证接口测试
npm test auth.test.ts

# 运行分类接口测试
npm test category.test.ts

# 运行交易记录接口测试
npm test transaction.test.ts
```

### 监听模式运行测试

```bash
npm run test:watch
```

### 生成测试覆盖率报告

```bash
npm run test:coverage
```

### CI环境运行测试

```bash
npm run test:ci
```

## 📊 测试结果示例

运行测试后，你应该看到类似以下的输出：

```
 PASS  src/__tests__/auth.test.ts
 PASS  src/__tests__/category.test.ts
 PASS  src/__tests__/transaction.test.ts
 PASS  src/__tests__/statistics.test.ts
 PASS  src/__tests__/upload.test.ts
 PASS  src/__tests__/health.test.ts

Test Suites: 6 passed, 6 total
Tests:       85 passed, 85 total
Snapshots:   0 total
Time:        15.234 s
```

## 🔧 测试配置

### Jest 配置 (`jest.config.js`)

- 使用 TypeScript 预设
- 测试环境：Node.js
- 覆盖率收集：排除配置文件和迁移脚本
- 超时时间：10秒

### 测试设置 (`setup.ts`)

- 自动创建内存数据库
- 提供测试数据创建工具函数
- 环境变量配置
- 数据库清理工具

## 📝 测试用例详情

### 认证接口测试

#### 登录接口 (`POST /api/auth/login`)
- ✅ 新用户登录成功
- ✅ 已存在用户登录成功
- ✅ 静默登录（不传用户信息）
- ❌ 缺少授权码返回400错误
- ❌ 微信API调用失败返回错误
- ❌ 网络错误返回503错误

#### Token刷新 (`POST /api/auth/refresh`)
- ✅ 成功刷新Token
- ❌ 缺少refreshToken返回400错误
- ❌ 无效refreshToken返回401错误
- ❌ 用户不存在返回401错误

### 分类接口测试

#### 获取分类列表 (`GET /api/categories`)
- ✅ 返回用户的所有分类
- ✅ 支持按类型筛选
- ❌ 未认证返回401错误

#### 创建分类 (`POST /api/categories`)
- ✅ 成功创建新分类
- ❌ 缺少必填字段返回400错误
- ❌ 类型无效返回400错误
- ❌ 分类名称重复返回409错误

### 交易记录接口测试

#### 获取交易记录 (`GET /api/transactions`)
- ✅ 返回交易记录列表
- ✅ 支持分页查询
- ✅ 支持按类型筛选
- ✅ 支持按日期范围筛选

#### 创建交易记录 (`POST /api/transactions`)
- ✅ 成功创建新交易记录
- ❌ 缺少必填字段返回400错误
- ❌ 金额为负数返回400错误
- ❌ 分类不存在返回404错误

### 统计接口测试

#### 统计概览 (`GET /api/statistics/overview`)
- ✅ 返回统计概览数据
- ✅ 支持按月份筛选
- ❌ 未认证返回401错误

#### 月度统计 (`GET /api/statistics/monthly`)
- ✅ 返回月度统计数据
- ❌ 年份无效返回400错误

### 文件上传接口测试

#### 图片上传 (`POST /api/upload/image`)
- ✅ 成功上传图片文件
- ❌ 未提供文件返回400错误
- ❌ 文件类型不支持返回400错误
- ❌ 文件过大返回400错误

### 健康检查接口测试

#### 基础健康检查 (`GET /health`)
- ✅ 返回基本健康状态
- ✅ 返回正确的时间戳格式
- ✅ 返回正确的运行时间

## 🛠️ 测试工具函数

### 数据库操作
- `createTestDatabase()` - 创建测试数据库
- `cleanTestDatabase()` - 清理测试数据
- `closeTestDatabase()` - 关闭测试数据库

### 测试数据创建
- `createTestUser()` - 创建测试用户
- `createTestCategory()` - 创建测试分类
- `createTestTransaction()` - 创建测试交易记录

### 认证工具
- 自动生成JWT Token
- 模拟用户认证状态

## 🔍 调试测试

### 查看详细输出

```bash
npm test -- --verbose
```

### 运行单个测试用例

```bash
npm test -- --testNamePattern="应该成功登录新用户"
```

### 调试模式

```bash
npm test -- --detectOpenHandles --forceExit
```

## 📈 测试覆盖率

目标覆盖率：
- 语句覆盖率：> 90%
- 分支覆盖率：> 85%
- 函数覆盖率：> 90%
- 行覆盖率：> 90%

查看覆盖率报告：
```bash
npm run test:coverage
open coverage/lcov-report/index.html
```

## 🚨 常见问题

### 1. 测试数据库连接失败
确保SQLite3正确安装：
```bash
npm install sqlite3 --save
```

### 2. 测试超时
增加Jest超时时间：
```javascript
// jest.config.js
module.exports = {
  testTimeout: 30000 // 30秒
};
```

### 3. 模拟微信API失败
检查axios mock配置：
```javascript
jest.mock('axios');
const mockedAxios = jest.mocked(require('axios'));
```

## 📋 测试检查清单

运行测试前确保：
- [ ] 所有依赖已安装
- [ ] 环境变量正确配置
- [ ] 数据库连接正常
- [ ] 没有端口冲突

测试通过标准：
- [ ] 所有测试用例通过
- [ ] 覆盖率达到目标
- [ ] 没有内存泄漏
- [ ] 响应时间合理

## 🎯 下一步

1. **持续集成**: 将测试集成到CI/CD流程
2. **性能测试**: 添加接口性能测试
3. **集成测试**: 添加端到端测试
4. **压力测试**: 测试高并发场景

现在运行 `npm test` 开始测试所有接口！
