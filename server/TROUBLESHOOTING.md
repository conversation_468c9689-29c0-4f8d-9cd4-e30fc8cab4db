# 故障排除指南

## 🚨 常见问题及解决方案

### 1. Node.js 相关问题

#### 问题：ICU库版本不匹配
```
dyld: Library not loaded: libicui18n.73.dylib
```

**解决方案：**
```bash
# 方法一：使用NVM切换Node.js版本
nvm install 18
nvm use 18

# 方法二：重新安装Node.js
brew uninstall node
brew install node@18

# 方法三：创建符号链接（macOS）
sudo ln -sf /usr/local/opt/icu4c/lib/libicuuc.74.2.dylib /usr/local/opt/icu4c/lib/libicuuc.73.dylib
sudo ln -sf /usr/local/opt/icu4c/lib/libicui18n.74.2.dylib /usr/local/opt/icu4c/lib/libicui18n.73.dylib
```

#### 问题：Node.js版本过低
```
Error: Node.js version 14.x is not supported
```

**解决方案：**
```bash
# 升级到Node.js 18 LTS
nvm install 18
nvm use 18
nvm alias default 18
```

### 2. 依赖安装问题

#### 问题：npm安装失败
```
npm ERR! code EACCES
npm ERR! permission denied
```

**解决方案：**
```bash
# 修复npm权限
sudo chown -R $(whoami) ~/.npm

# 或使用nvm管理Node.js
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
```

#### 问题：SQLite3编译失败
```
Error: Failed to build sqlite3
```

**解决方案：**
```bash
# macOS
xcode-select --install
npm install sqlite3 --build-from-source

# Ubuntu/Debian
sudo apt-get install build-essential python3
npm install sqlite3 --build-from-source

# Windows
npm install --global windows-build-tools
npm install sqlite3 --build-from-source
```

### 3. 端口和网络问题

#### 问题：端口被占用
```
Error: listen EADDRINUSE :::3000
```

**解决方案：**
```bash
# 查找占用端口的进程
lsof -i :3000  # macOS/Linux
netstat -ano | findstr :3000  # Windows

# 杀死进程
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows

# 或修改端口
echo "PORT=3001" >> .env
```

#### 问题：CORS跨域错误
```
Access to fetch at 'http://localhost:3000' from origin 'https://servicewechat.com' has been blocked by CORS policy
```

**解决方案：**
```env
# 在.env文件中配置
CORS_ORIGIN=https://servicewechat.com
# 或允许所有来源（仅开发环境）
CORS_ORIGIN=*
```

### 4. 数据库问题

#### 问题：数据库文件权限错误
```
Error: SQLITE_CANTOPEN: unable to open database file
```

**解决方案：**
```bash
# 创建数据目录并设置权限
mkdir -p data
chmod 755 data

# 检查磁盘空间
df -h  # macOS/Linux
dir  # Windows
```

#### 问题：数据库表不存在
```
Error: SQLITE_ERROR: no such table: users
```

**解决方案：**
```bash
# 重新初始化数据库
rm -f data/accounting.db
node scripts/init-database.js
```

### 5. TypeScript编译问题

#### 问题：TypeScript编译错误
```
error TS2307: Cannot find module 'express'
```

**解决方案：**
```bash
# 重新安装类型定义
npm install @types/node @types/express --save-dev

# 清理并重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 问题：路径解析错误
```
TypeError: The "path" argument must be of type string. Received undefined
```

**解决方案：**
```bash
# 检查环境变量配置
cat .env

# 确保配置文件中的路径变量正确
DATABASE_PATH=./data/accounting.db
UPLOAD_DIR=./uploads
```

### 6. 微信小程序相关问题

#### 问题：微信登录失败
```
Error: invalid code
```

**解决方案：**
```javascript
// 检查微信小程序配置
// .env文件中配置正确的AppID和AppSecret
WECHAT_APP_ID=your-real-app-id
WECHAT_APP_SECRET=your-real-app-secret

// 开发环境可以使用测试值
WECHAT_APP_ID=test-app-id
WECHAT_APP_SECRET=test-app-secret
```

### 7. 文件上传问题

#### 问题：文件上传失败
```
Error: ENOENT: no such file or directory, open 'uploads/...'
```

**解决方案：**
```bash
# 创建上传目录
mkdir -p uploads
chmod 755 uploads

# 检查环境变量
echo $UPLOAD_DIR  # 应该是 ./uploads
```

### 8. 内存和性能问题

#### 问题：内存不足
```
JavaScript heap out of memory
```

**解决方案：**
```bash
# 增加Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

# 或在package.json中配置
"scripts": {
  "dev": "node --max-old-space-size=4096 ./node_modules/.bin/ts-node-dev src/app.ts"
}
```

### 9. 日志和调试

#### 启用详细日志
```env
# .env文件中配置
LOG_LEVEL=debug
NODE_ENV=development
```

#### 查看日志
```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log

# 使用PM2查看日志
pm2 logs accounting-server
```

### 10. 快速诊断命令

#### 系统信息检查
```bash
# 检查Node.js和npm版本
node --version
npm --version

# 检查项目依赖
npm list --depth=0

# 检查端口占用
lsof -i :3000  # macOS/Linux
netstat -ano | findstr :3000  # Windows

# 检查磁盘空间
df -h  # macOS/Linux
dir  # Windows

# 检查内存使用
free -m  # Linux
vm_stat  # macOS
```

#### 项目状态检查
```bash
# 检查环境配置
cat .env

# 检查数据库文件
ls -la data/

# 检查上传目录
ls -la uploads/

# 检查日志文件
ls -la logs/
```

## 🔧 应急解决方案

### 完全重置项目
```bash
# 1. 清理所有生成的文件
rm -rf node_modules
rm -rf dist
rm -rf data
rm -rf uploads
rm -rf logs
rm -f package-lock.json

# 2. 重新设置项目
npm install
mkdir -p data uploads logs
cp .env.example .env

# 3. 初始化数据库
node scripts/init-database.js

# 4. 启动服务
npm run dev
```

### 使用简化服务器
如果TypeScript版本无法运行，可以使用简化的JavaScript版本：
```bash
node start-simple.js
```

### Docker容器化运行
```bash
# 创建Dockerfile
cat > Dockerfile << 'EOF'
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
EOF

# 构建并运行
docker build -t accounting-server .
docker run -p 3000:3000 accounting-server
```

## 📞 获取帮助

如果以上解决方案都无法解决问题，请：

1. **检查日志文件**: `logs/app.log`
2. **查看控制台输出**: 完整的错误信息
3. **提供环境信息**: 
   - 操作系统版本
   - Node.js版本
   - npm版本
   - 错误的完整堆栈跟踪

## 🚀 预防措施

1. **定期更新依赖**: `npm update`
2. **使用固定版本**: 在`package.json`中使用确切版本号
3. **备份数据**: 定期备份`data/`目录
4. **监控日志**: 定期检查`logs/app.log`
5. **环境隔离**: 使用Docker或虚拟环境
