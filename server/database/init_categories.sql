-- 初始化默认分类数据
-- 使用方法：在SQLite数据库中执行此SQL文件

-- 清空现有分类数据（可选，如果需要重新初始化）
-- DELETE FROM categories;

-- 插入默认支出分类
INSERT OR IGNORE INTO categories (id, name, icon, color, type, sort_order, user_id, created_at, updated_at) VALUES
('cat_expense_1', '餐饮', '🍽️', '#FF6B6B', 'expense', 1, NULL, datetime('now'), datetime('now')),
('cat_expense_2', '交通', '🚗', '#4ECDC4', 'expense', 2, NULL, datetime('now'), datetime('now')),
('cat_expense_3', '购物', '🛒', '#45B7D1', 'expense', 3, NULL, datetime('now'), datetime('now')),
('cat_expense_4', '娱乐', '🎮', '#96CEB4', 'expense', 4, NULL, datetime('now'), datetime('now')),
('cat_expense_5', '医疗', '🏥', '#FFEAA7', 'expense', 5, NULL, datetime('now'), datetime('now')),
('cat_expense_6', '教育', '📚', '#DDA0DD', 'expense', 6, NULL, datetime('now'), datetime('now')),
('cat_expense_7', '住房', '🏠', '#98D8C8', 'expense', 7, NULL, datetime('now'), datetime('now')),
('cat_expense_8', '通讯', '📱', '#F7DC6F', 'expense', 8, NULL, datetime('now'), datetime('now')),
('cat_expense_9', '其他', '📦', '#BDC3C7', 'expense', 9, NULL, datetime('now'), datetime('now'));

-- 插入默认收入分类
INSERT OR IGNORE INTO categories (id, name, icon, color, type, sort_order, user_id, created_at, updated_at) VALUES
('cat_income_1', '工资', '💰', '#2ECC71', 'income', 1, NULL, datetime('now'), datetime('now')),
('cat_income_2', '奖金', '🎁', '#3498DB', 'income', 2, NULL, datetime('now'), datetime('now')),
('cat_income_3', '投资', '📈', '#9B59B6', 'income', 3, NULL, datetime('now'), datetime('now')),
('cat_income_4', '兼职', '💼', '#E67E22', 'income', 4, NULL, datetime('now'), datetime('now')),
('cat_income_5', '红包', '🧧', '#E74C3C', 'income', 5, NULL, datetime('now'), datetime('now')),
('cat_income_6', '其他', '💎', '#1ABC9C', 'income', 6, NULL, datetime('now'), datetime('now'));

-- 验证插入结果
-- SELECT * FROM categories ORDER BY type, sort_order;
