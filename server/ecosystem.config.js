/**
 * PM2 进程管理配置
 * 用于生产环境部署和进程管理
 */

module.exports = {
  apps: [
    {
      // 应用名称
      name: 'accounting-server',
      
      // 应用入口文件
      script: 'dist/app.js',
      
      // 实例数量（'max' 表示使用所有CPU核心）
      instances: 'max',
      
      // 执行模式（cluster 集群模式）
      exec_mode: 'cluster',
      
      // 开发环境配置
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      
      // 生产环境配置
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 自动重启配置
      autorestart: true,
      watch: false, // 生产环境不建议开启文件监听
      max_memory_restart: '1G',
      
      // 进程管理配置
      min_uptime: '10s',
      max_restarts: 10,
      
      // 环境变量文件
      env_file: '.env',
      
      // 其他配置
      node_args: '--max-old-space-size=1024',
      
      // 健康检查
      health_check_grace_period: 3000,
      
      // 优雅关闭
      kill_timeout: 5000,
      
      // 监听文件变化（仅开发环境）
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'data'
      ],
      
      // 合并日志
      merge_logs: true,
      
      // 时间戳
      time: true
    }
  ],

  // 部署配置
  deploy: {
    // 生产环境部署
    production: {
      user: 'deploy',
      host: 'your-server-ip',
      ref: 'origin/main',
      repo: '**************:your-username/your-repo.git',
      path: '/var/www/accounting-app',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    },
    
    // 测试环境部署
    staging: {
      user: 'deploy',
      host: 'your-staging-server-ip',
      ref: 'origin/develop',
      repo: '**************:your-username/your-repo.git',
      path: '/var/www/accounting-app-staging',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env staging'
    }
  }
};
