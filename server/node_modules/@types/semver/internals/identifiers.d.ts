/**
 * Compares two identifiers, must be numeric strings or truthy/falsy values.
 *
 * Sorts in ascending order when passed to `Array.sort()`.
 */
export function compareIdentifiers(a: string | null | undefined, b: string | null | undefined): 1 | 0 | -1;

/**
 * The reverse of compareIdentifiers.
 *
 * Sorts in descending order when passed to `Array.sort()`.
 */
export function rcompareIdentifiers(a: string | null | undefined, b: string | null | undefined): 1 | 0 | -1;
