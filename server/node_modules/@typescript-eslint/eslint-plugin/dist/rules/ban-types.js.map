{"version": 3, "file": "ban-types.js", "sourceRoot": "", "sources": ["../../src/rules/ban-types.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAsBhC,SAAS,YAAY,CAAC,GAAW;IAC/B,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,aAAa,CACpB,IAAmB,EACnB,UAA+B;IAE/B,OAAO,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,gBAAgB,CACvB,UAAkE;IAElE,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,EAAE,CAAC;KACX;IAED,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,OAAO,IAAI,UAAU,EAAE,CAAC;KACzB;IAED,IAAI,UAAU,CAAC,OAAO,EAAE;QACtB,OAAO,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;KACjC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,YAAY,GAAU;IAC1B,MAAM,EAAE;QACN,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,QAAQ;KAClB;IACD,OAAO,EAAE;QACP,OAAO,EAAE,qBAAqB;QAC9B,OAAO,EAAE,SAAS;KACnB;IACD,MAAM,EAAE;QACN,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,QAAQ;KAClB;IACD,MAAM,EAAE;QACN,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,QAAQ;KAClB;IACD,MAAM,EAAE;QACN,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,QAAQ;KAClB;IAED,QAAQ,EAAE;QACR,OAAO,EAAE;YACP,sDAAsD;YACtD,6FAA6F;YAC7F,oHAAoH;YACpH,iHAAiH;SAClH,CAAC,IAAI,CAAC,IAAI,CAAC;KACb;IAED,gBAAgB;IAChB,MAAM,EAAE;QACN,OAAO,EAAE;YACP,sGAAsG;YACtG,gFAAgF;YAChF,gFAAgF;YAChF,gHAAgH;SACjH,CAAC,IAAI,CAAC,IAAI,CAAC;QACZ,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,sBAAsB,CAAC;KACvD;IACD,IAAI,EAAE;QACJ,OAAO,EAAE;YACP,8CAA8C;YAC9C,gFAAgF;YAChF,gFAAgF;YAChF,iGAAiG;YACjG,gHAAgH;SACjH,CAAC,IAAI,CAAC,IAAI,CAAC;QACZ,OAAO,EAAE;YACP,QAAQ;YACR,SAAS;YACT,uBAAuB;YACvB,sBAAsB;SACvB;KACF;CACF,CAAC;AAEW,QAAA,aAAa,GAAG;IAC3B,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,OAAO,EAAE,sBAAc,CAAC,gBAAgB;IACxC,KAAK,EAAE,sBAAc,CAAC,cAAc;IACpC,IAAI,EAAE,sBAAc,CAAC,aAAa;IAClC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,SAAS,EAAE,sBAAc,CAAC,kBAAkB;IAC5C,OAAO,EAAE,sBAAc,CAAC,gBAAgB;IACxC,IAAI,EAAE,sBAAc,CAAC,aAAa;CACnC,CAAC;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,WAAW;IACjB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,wBAAwB;YACrC,WAAW,EAAE,OAAO;SACrB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,iBAAiB,EAAE,kDAAkD;YACrE,qBAAqB,EAAE,4CAA4C;SACpE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,oBAAoB,EAAE;4BACpB,KAAK,EAAE;gCACL,EAAE,IAAI,EAAE,MAAM,EAAE;gCAChB,EAAE,IAAI,EAAE,SAAS,EAAE;gCACnB,EAAE,IAAI,EAAE,QAAQ,EAAE;gCAClB;oCACE,IAAI,EAAE,QAAQ;oCACd,UAAU,EAAE;wCACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wCAC3B,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wCAC3B,OAAO,EAAE;4CACP,IAAI,EAAE,OAAO;4CACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yCAC1B;qCACF;oCACD,oBAAoB,EAAE,KAAK;iCAC5B;6BACF;yBACF;qBACF;oBACD,cAAc,EAAE;wBACd,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IACpB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;;QACvB,MAAM,cAAc,GAAG,MAAA,OAAO,CAAC,cAAc,mCAAI,IAAI,CAAC;QACtD,MAAM,WAAW,GAAG,MAAA,OAAO,CAAC,KAAK,mCAAI,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,EAAE,EACF,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,EAClC,WAAW,CACZ,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,GAAG,CACzB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CACxE,CAAC;QAEF,SAAS,gBAAgB,CACvB,QAAuB,EACvB,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC;YAEvD,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEzC,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,KAAK,EAAE;gBACpD,OAAO;aACR;YAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GACX,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC;YACrE,MAAM,OAAO,GACX,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ;gBAC1C,CAAC,CAAC,UAAU,CAAC,OAAO;gBACpB,CAAC,CAAC,SAAS,CAAC;YAEhB,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,mBAAmB;gBAC9B,IAAI,EAAE;oBACJ,IAAI;oBACJ,aAAa;iBACd;gBACD,GAAG,EAAE,OAAO;oBACV,CAAC,CAAC,CAAC,KAAK,EAAoB,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC;oBACnE,CAAC,CAAC,IAAI;gBACR,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBACpC,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,IAAI;wBACJ,WAAW;qBACZ;oBACD,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE,CAC/B,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC;iBAC3C,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAC3C,qBAAa,EACb,CAAC,GAA0B,EAAE,OAAO,EAAE,EAAE;YACtC,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC5B,GAAG,CAAC,qBAAa,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAmB,EAAQ,EAAE,CAC1D,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aACnC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EACD,EAAE,CACH,CAAC;QAEF,uCACK,gBAAgB,KAEnB,aAAa,CAAC,IAAI;gBAChB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvB,OAAO;iBACR;gBAED,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,WAAW,CAAC,IAAI;gBACd,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;oBAClC,gBAAgB,CAAC,IAAI,CAAC,CAAC;iBACxB;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEhC,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,gBAAgB,CAAC,IAAI,CAAC,CAAC;iBACxB;YACH,CAAC;YACD,mBAAmB,CAAC,IAAI;gBACtB,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,iBAAiB,CAAC,IAAI;gBACpB,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}