{"version": 3, "file": "space-before-blocks.js", "sourceRoot": "", "sources": ["../../src/rules/space-before-blocks.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,qBAAqB,CAAC,CAAC;AAK1D,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,0CAA0C;YACvD,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC9B,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ;YACN,mHAAmH;YACnH,eAAe,EAAE,wCAAwC;YACzD,mHAAmH;YACnH,YAAY,EAAE,qCAAqC,IAChD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAC1B;KACF;IACD,cAAc,EAAE,CAAC,QAAQ,CAAC;IAC1B,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,IAAI,YAAY,GAAG,IAAI,CAAC;QAExB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,YAAY,GAAG,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC;SAC5C;aAAM,IAAI,MAAM,KAAK,OAAO,EAAE;YAC7B,YAAY,GAAG,KAAK,CAAC;SACtB;QAED,SAAS,mBAAmB,CAC1B,IAA+C;YAE/C,MAAM,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,IAAI,CAAC,EAAE;gBAClE,yGAAyG;gBACzG,MAAM,QAAQ,GAAG,UAAU,CAAC,oBAAoB,CAC9C,cAAc,EACd,IAAsB,CACvB,CAAC;gBAEF,IAAI,YAAY,IAAI,CAAC,QAAQ,EAAE;oBAC7B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,cAAc;wBACzB,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;wBAC3C,CAAC;qBACF,CAAC,CAAC;iBACJ;qBAAM,IAAI,CAAC,YAAY,IAAI,QAAQ,EAAE;oBACpC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,iBAAiB;wBAC5B,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;gCACvB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;6BACd,CAAC,CAAC;wBACL,CAAC;qBACF,CAAC,CAAC;iBACJ;aACF;QACH,CAAC;QAED,SAAS,mBAAmB,CAAC,IAAgC;YAC3D,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrD,IAAI,UAAU,EAAE;gBACd,mBAAmB,CAAC,UAAU,CAAC,CAAC;aACjC;QACH,CAAC;QAED,uCACK,KAAK,KACR,iBAAiB,EAAE,mBAAmB,EACtC,eAAe,EAAE,mBAAmB,IACpC;IACJ,CAAC;CACF,CAAC,CAAC"}