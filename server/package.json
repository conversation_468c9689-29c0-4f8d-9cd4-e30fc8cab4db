{"name": "accounting-app-server", "version": "1.0.0", "description": "记账小程序后端服务", "main": "dist/app.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "ts-node src/database/migrate.ts", "db:seed": "ts-node src/database/seed.ts"}, "keywords": ["accounting", "miniprogram", "typescript", "express", "sqlite"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "sqlite3": "^5.1.6", "sqlite": "^5.0.1", "joi": "^17.9.2", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0", "dayjs": "^1.11.7", "axios": "^1.4.0", "dotenv": "^16.0.3"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/morgan": "^1.9.4", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/multer": "^1.4.7", "@types/uuid": "^9.0.1", "@types/node": "^20.2.5", "@types/jest": "^29.5.2", "@types/supertest": "^2.0.12", "supertest": "^6.3.3", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "eslint": "^8.41.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "nodemon": "^2.0.22", "ts-node-dev": "^2.0.0", "typescript": "^5.0.4"}, "engines": {"node": ">=16.0.0"}}