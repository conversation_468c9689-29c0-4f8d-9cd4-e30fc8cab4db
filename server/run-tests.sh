#!/bin/bash

# 服务端接口测试运行脚本
# 用于验证所有接口的单元测试

echo "🚀 开始运行服务端接口单元测试..."
echo "=================================="

# 检查Node.js版本
echo "📋 检查环境..."
node_version=$(node -v)
npm_version=$(npm -v)
echo "Node.js版本: $node_version"
echo "npm版本: $npm_version"

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在server目录下运行此脚本"
    exit 1
fi

# 检查是否安装了依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 检查TypeScript编译
echo "🔧 检查TypeScript编译..."
npx tsc --noEmit
if [ $? -ne 0 ]; then
    echo "❌ TypeScript编译检查失败"
    exit 1
fi

echo "✅ 环境检查完成"
echo ""

# 运行测试
echo "🧪 运行单元测试..."
echo "=================================="

# 设置测试环境变量
export NODE_ENV=test

# 运行所有测试
npm test

test_exit_code=$?

echo ""
echo "=================================="

if [ $test_exit_code -eq 0 ]; then
    echo "✅ 所有测试通过！"
    echo ""
    echo "📊 生成测试覆盖率报告..."
    npm run test:coverage
    
    if [ $? -eq 0 ]; then
        echo "✅ 测试覆盖率报告生成完成"
        echo "📁 报告位置: coverage/lcov-report/index.html"
    else
        echo "⚠️  测试覆盖率报告生成失败"
    fi
    
    echo ""
    echo "🎉 测试完成！所有接口测试通过。"
    echo ""
    echo "📋 测试总结:"
    echo "- 认证接口: ✅ 通过"
    echo "- 分类接口: ✅ 通过"
    echo "- 交易记录接口: ✅ 通过"
    echo "- 统计接口: ✅ 通过"
    echo "- 文件上传接口: ✅ 通过"
    echo "- 健康检查接口: ✅ 通过"
    echo ""
    echo "🚀 服务端接口已准备就绪，可以启动服务！"
    
else
    echo "❌ 测试失败！"
    echo ""
    echo "🔍 请检查以下内容:"
    echo "1. 数据库连接是否正常"
    echo "2. 环境变量是否正确配置"
    echo "3. 依赖是否完整安装"
    echo "4. 代码是否有语法错误"
    echo ""
    echo "💡 调试建议:"
    echo "- 运行 'npm test -- --verbose' 查看详细错误信息"
    echo "- 运行 'npm test -- --detectOpenHandles' 检查资源泄漏"
    echo "- 检查 TEST_GUIDE.md 获取更多帮助"
    
    exit 1
fi
