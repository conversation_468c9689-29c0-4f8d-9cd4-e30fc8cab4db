#!/bin/bash

# 记账小程序后端服务部署脚本
# 用于生产环境自动化部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
APP_NAME="accounting-server"
APP_DIR="/var/www/accounting-app"
BACKUP_DIR="/var/backups/accounting-app"
NGINX_CONF="/etc/nginx/sites-available/accounting-app"
SERVICE_USER="deploy"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 root 用户或 sudo 运行此脚本"
        exit 1
    fi
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_info "安装 PM2..."
        npm install -g pm2
    fi
    
    # 检查Nginx
    if ! command -v nginx &> /dev/null; then
        log_info "安装 Nginx..."
        apt update
        apt install -y nginx
    fi
    
    log_success "系统依赖检查完成"
}

# 创建用户和目录
setup_user_and_directories() {
    log_info "设置用户和目录..."
    
    # 创建部署用户
    if ! id "$SERVICE_USER" &>/dev/null; then
        useradd -m -s /bin/bash "$SERVICE_USER"
        log_success "创建用户: $SERVICE_USER"
    fi
    
    # 创建应用目录
    mkdir -p "$APP_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "/var/data"
    mkdir -p "/var/uploads"
    mkdir -p "/var/logs"
    
    # 设置目录权限
    chown -R "$SERVICE_USER:$SERVICE_USER" "$APP_DIR"
    chown -R "$SERVICE_USER:$SERVICE_USER" "/var/data"
    chown -R "$SERVICE_USER:$SERVICE_USER" "/var/uploads"
    chown -R "$SERVICE_USER:$SERVICE_USER" "/var/logs"
    
    chmod 755 "/var/uploads"
    chmod 755 "/var/data"
    
    log_success "用户和目录设置完成"
}

# 备份当前版本
backup_current_version() {
    if [ -d "$APP_DIR" ] && [ "$(ls -A $APP_DIR)" ]; then
        log_info "备份当前版本..."
        
        BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
        cp -r "$APP_DIR" "$BACKUP_DIR/$BACKUP_NAME"
        
        log_success "备份完成: $BACKUP_DIR/$BACKUP_NAME"
    fi
}

# 部署应用代码
deploy_application() {
    log_info "部署应用代码..."
    
    # 切换到应用目录
    cd "$APP_DIR"
    
    # 如果是Git仓库，拉取最新代码
    if [ -d ".git" ]; then
        sudo -u "$SERVICE_USER" git pull origin main
    else
        log_warning "不是Git仓库，请手动上传代码到 $APP_DIR"
        return 1
    fi
    
    # 安装依赖
    log_info "安装依赖..."
    sudo -u "$SERVICE_USER" npm ci --only=production
    
    # 构建应用
    log_info "构建应用..."
    sudo -u "$SERVICE_USER" npm run build
    
    log_success "应用部署完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [ ! -f "$APP_DIR/.env" ]; then
        if [ -f "$APP_DIR/.env.production" ]; then
            cp "$APP_DIR/.env.production" "$APP_DIR/.env"
            log_success "复制生产环境配置"
        else
            log_error "未找到环境配置文件"
            exit 1
        fi
    fi
    
    # 设置文件权限
    chown "$SERVICE_USER:$SERVICE_USER" "$APP_DIR/.env"
    chmod 600 "$APP_DIR/.env"
    
    log_success "环境变量配置完成"
}

# 配置Nginx
setup_nginx() {
    log_info "配置 Nginx..."
    
    # 复制Nginx配置
    if [ -f "$APP_DIR/nginx.conf" ]; then
        cp "$APP_DIR/nginx.conf" "$NGINX_CONF"
        
        # 创建软链接
        if [ ! -L "/etc/nginx/sites-enabled/accounting-app" ]; then
            ln -s "$NGINX_CONF" "/etc/nginx/sites-enabled/accounting-app"
        fi
        
        # 测试Nginx配置
        nginx -t
        
        log_success "Nginx 配置完成"
    else
        log_warning "未找到 Nginx 配置文件"
    fi
}

# 配置SSL证书
setup_ssl() {
    log_info "配置 SSL 证书..."
    
    # 检查是否安装了 certbot
    if ! command -v certbot &> /dev/null; then
        log_info "安装 Certbot..."
        apt install -y certbot python3-certbot-nginx
    fi
    
    # 这里可以添加自动申请SSL证书的逻辑
    log_warning "请手动配置SSL证书或运行: certbot --nginx -d your-domain.com"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 切换到应用目录
    cd "$APP_DIR"
    
    # 停止现有的PM2进程
    sudo -u "$SERVICE_USER" pm2 delete "$APP_NAME" 2>/dev/null || true
    
    # 启动PM2进程
    sudo -u "$SERVICE_USER" pm2 start ecosystem.config.js --env production
    
    # 保存PM2配置
    sudo -u "$SERVICE_USER" pm2 save
    
    # 设置PM2开机自启
    sudo -u "$SERVICE_USER" pm2 startup
    
    # 重启Nginx
    systemctl restart nginx
    systemctl enable nginx
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 10
    
    # 检查PM2进程
    if sudo -u "$SERVICE_USER" pm2 list | grep -q "$APP_NAME"; then
        log_success "PM2 进程运行正常"
    else
        log_error "PM2 进程启动失败"
        exit 1
    fi
    
    # 检查HTTP响应
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log_success "应用健康检查通过"
    else
        log_error "应用健康检查失败"
        exit 1
    fi
    
    # 检查Nginx
    if systemctl is-active --quiet nginx; then
        log_success "Nginx 运行正常"
    else
        log_error "Nginx 启动失败"
        exit 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份..."
    
    # 保留最近7天的备份
    find "$BACKUP_DIR" -type d -name "backup-*" -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
    
    log_success "旧备份清理完成"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "🎉 部署完成！"
    echo ""
    echo "📋 部署信息："
    echo "  - 应用目录: $APP_DIR"
    echo "  - 备份目录: $BACKUP_DIR"
    echo "  - 运行用户: $SERVICE_USER"
    echo "  - 进程管理: PM2"
    echo ""
    echo "🔗 访问地址："
    echo "  - 本地健康检查: http://localhost:3000/health"
    echo "  - API文档: http://localhost:3000/api"
    echo ""
    echo "🛠️ 管理命令："
    echo "  - 查看日志: sudo -u $SERVICE_USER pm2 logs $APP_NAME"
    echo "  - 重启服务: sudo -u $SERVICE_USER pm2 restart $APP_NAME"
    echo "  - 停止服务: sudo -u $SERVICE_USER pm2 stop $APP_NAME"
    echo "  - 查看状态: sudo -u $SERVICE_USER pm2 status"
    echo ""
}

# 主函数
main() {
    echo "🚀 开始部署记账小程序后端服务"
    echo "================================"
    echo ""
    
    check_root
    check_dependencies
    setup_user_and_directories
    backup_current_version
    deploy_application
    setup_environment
    setup_nginx
    setup_ssl
    start_services
    health_check
    cleanup_old_backups
    show_deployment_info
}

# 错误处理
trap 'log_error "部署失败，请检查错误信息"; exit 1' ERR

# 执行主函数
main "$@"
