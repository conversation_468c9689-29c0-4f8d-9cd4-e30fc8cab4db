#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 用于创建数据库表结构和初始数据
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// 加载环境变量
require('dotenv').config();

const DB_PATH = process.env.DATABASE_PATH || './data/accounting.db';
const DATA_DIR = path.dirname(DB_PATH);

console.log('🚀 开始初始化数据库...');

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
  console.log(`✅ 创建数据目录: ${DATA_DIR}`);
}

// 连接数据库
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log(`✅ 数据库连接成功: ${DB_PATH}`);
});

// 创建表结构
const createTables = () => {
  return new Promise((resolve, reject) => {
    const sql = `
      -- 用户表
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        openid TEXT UNIQUE NOT NULL,
        nickname TEXT,
        avatar_url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        deleted_at DATETIME NULL
      );

      -- 分类表
      CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
        icon TEXT,
        color TEXT,
        budget DECIMAL(10,2),
        sort_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        deleted_at DATETIME NULL,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );

      -- 交易记录表
      CREATE TABLE IF NOT EXISTS transactions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        category_id TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
        amount DECIMAL(10,2) NOT NULL,
        description TEXT,
        date DATE NOT NULL,
        image_url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        deleted_at DATETIME NULL,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (category_id) REFERENCES categories(id)
      );

      -- 创建索引
      CREATE INDEX IF NOT EXISTS idx_users_openid ON users(openid);
      CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories(user_id);
      CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
      CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
      CREATE INDEX IF NOT EXISTS idx_transactions_category_id ON transactions(category_id);
      CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date);
      CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
    `;

    db.exec(sql, (err) => {
      if (err) {
        console.error('❌ 创建表失败:', err.message);
        reject(err);
      } else {
        console.log('✅ 数据库表创建成功');
        resolve();
      }
    });
  });
};

// 插入默认分类数据
const insertDefaultCategories = () => {
  return new Promise((resolve, reject) => {
    const defaultCategories = [
      // 支出分类
      { id: 'cat_food', name: '餐饮', type: 'expense', icon: '🍔', color: '#FF6B6B', sort_order: 1 },
      { id: 'cat_transport', name: '交通', type: 'expense', icon: '🚗', color: '#4ECDC4', sort_order: 2 },
      { id: 'cat_shopping', name: '购物', type: 'expense', icon: '🛍️', color: '#45B7D1', sort_order: 3 },
      { id: 'cat_entertainment', name: '娱乐', type: 'expense', icon: '🎮', color: '#9B59B6', sort_order: 4 },
      { id: 'cat_medical', name: '医疗', type: 'expense', icon: '🏥', color: '#E74C3C', sort_order: 5 },
      { id: 'cat_education', name: '教育', type: 'expense', icon: '📚', color: '#F39C12', sort_order: 6 },
      { id: 'cat_housing', name: '住房', type: 'expense', icon: '🏠', color: '#8E44AD', sort_order: 7 },
      { id: 'cat_utilities', name: '水电费', type: 'expense', icon: '💡', color: '#16A085', sort_order: 8 },
      { id: 'cat_communication', name: '通讯', type: 'expense', icon: '📱', color: '#2980B9', sort_order: 9 },
      { id: 'cat_other_expense', name: '其他支出', type: 'expense', icon: '💰', color: '#95A5A6', sort_order: 10 },
      
      // 收入分类
      { id: 'cat_salary', name: '工资', type: 'income', icon: '💼', color: '#2ECC71', sort_order: 1 },
      { id: 'cat_bonus', name: '奖金', type: 'income', icon: '🏆', color: '#F39C12', sort_order: 2 },
      { id: 'cat_investment', name: '投资收益', type: 'income', icon: '📈', color: '#27AE60', sort_order: 3 },
      { id: 'cat_freelance', name: '兼职收入', type: 'income', icon: '💻', color: '#3498DB', sort_order: 4 },
      { id: 'cat_gift', name: '礼金', type: 'income', icon: '🎁', color: '#E67E22', sort_order: 5 },
      { id: 'cat_other_income', name: '其他收入', type: 'income', icon: '💎', color: '#9B59B6', sort_order: 6 }
    ];

    const stmt = db.prepare(`
      INSERT OR IGNORE INTO categories (id, user_id, name, type, icon, color, sort_order, created_at, updated_at)
      VALUES (?, NULL, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `);

    let completed = 0;
    const total = defaultCategories.length;

    defaultCategories.forEach(category => {
      stmt.run([
        category.id,
        category.name,
        category.type,
        category.icon,
        category.color,
        category.sort_order
      ], (err) => {
        if (err) {
          console.error(`❌ 插入分类失败 (${category.name}):`, err.message);
        }
        
        completed++;
        if (completed === total) {
          stmt.finalize();
          console.log(`✅ 默认分类数据插入完成 (${total} 条)`);
          resolve();
        }
      });
    });
  });
};

// 创建示例用户和数据（可选）
const createSampleData = () => {
  return new Promise((resolve, reject) => {
    const sampleUser = {
      id: 'user_sample',
      openid: 'sample_openid_123',
      nickname: '示例用户',
      avatar_url: ''
    };

    const sampleTransactions = [
      {
        id: 'txn_1',
        user_id: 'user_sample',
        category_id: 'cat_food',
        type: 'expense',
        amount: 25.50,
        description: '午餐',
        date: new Date().toISOString().split('T')[0]
      },
      {
        id: 'txn_2',
        user_id: 'user_sample',
        category_id: 'cat_salary',
        type: 'income',
        amount: 5000.00,
        description: '月薪',
        date: new Date().toISOString().split('T')[0]
      }
    ];

    // 插入示例用户
    db.run(`
      INSERT OR IGNORE INTO users (id, openid, nickname, avatar_url, created_at, updated_at)
      VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))
    `, [sampleUser.id, sampleUser.openid, sampleUser.nickname, sampleUser.avatar_url], (err) => {
      if (err) {
        console.error('❌ 插入示例用户失败:', err.message);
        reject(err);
        return;
      }

      // 插入示例交易记录
      const stmt = db.prepare(`
        INSERT OR IGNORE INTO transactions (id, user_id, category_id, type, amount, description, date, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `);

      let completed = 0;
      const total = sampleTransactions.length;

      sampleTransactions.forEach(transaction => {
        stmt.run([
          transaction.id,
          transaction.user_id,
          transaction.category_id,
          transaction.type,
          transaction.amount,
          transaction.description,
          transaction.date
        ], (err) => {
          if (err) {
            console.error('❌ 插入示例交易失败:', err.message);
          }
          
          completed++;
          if (completed === total) {
            stmt.finalize();
            console.log(`✅ 示例数据插入完成 (用户: 1, 交易: ${total})`);
            resolve();
          }
        });
      });
    });
  });
};

// 验证数据库
const verifyDatabase = () => {
  return new Promise((resolve, reject) => {
    db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
      if (err) {
        reject(err);
        return;
      }

      console.log('📊 数据库表列表:');
      tables.forEach(table => {
        console.log(`  - ${table.name}`);
      });

      // 检查分类数量
      db.get("SELECT COUNT(*) as count FROM categories", (err, result) => {
        if (err) {
          reject(err);
          return;
        }

        console.log(`📈 默认分类数量: ${result.count}`);
        resolve();
      });
    });
  });
};

// 主执行函数
async function main() {
  try {
    await createTables();
    await insertDefaultCategories();
    
    // 如果需要创建示例数据，取消下面的注释
    // await createSampleData();
    
    await verifyDatabase();
    
    console.log('🎉 数据库初始化完成！');
    console.log('');
    console.log('📝 下一步操作:');
    console.log('  1. 启动服务: npm run dev');
    console.log('  2. 访问健康检查: http://localhost:3000/health');
    console.log('  3. 查看API文档: http://localhost:3000/api');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 执行初始化
main();
