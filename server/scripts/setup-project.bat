@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 记账小程序服务端项目完整设置脚本 (Windows版本)
:: 自动完成环境检查、依赖安装、数据库初始化等工作

echo 🚀 记账小程序服务端项目设置
echo ================================
echo.

:: 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ 错误: 未找到 package.json 文件，请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

:: 检查Node.js是否安装
echo 📋 检查环境依赖...
node -v >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未安装 Node.js，请先安装 Node.js ^>= 16.0.0
    echo.
    echo 安装方法：
    echo   1. 访问 https://nodejs.org 下载 LTS 版本
    echo   2. 或使用 Chocolatey: choco install nodejs
    echo   3. 或使用 Scoop: scoop install nodejs
    pause
    exit /b 1
)

:: 获取Node.js版本
for /f "tokens=1" %%i in ('node -v') do set NODE_VERSION=%%i
echo ✅ Node.js 版本: %NODE_VERSION%

:: 检查npm
npm -v >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: npm 未安装
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('npm -v') do set NPM_VERSION=%%i
echo ✅ npm 版本: %NPM_VERSION%

:: 创建必要的目录
echo.
echo 📁 创建项目目录...
if not exist "data" (
    mkdir data
    echo ✅ 创建目录: data
) else (
    echo ℹ️  目录已存在: data
)

if not exist "uploads" (
    mkdir uploads
    echo ✅ 创建目录: uploads
) else (
    echo ℹ️  目录已存在: uploads
)

if not exist "logs" (
    mkdir logs
    echo ✅ 创建目录: logs
) else (
    echo ℹ️  目录已存在: logs
)

if not exist "scripts" (
    mkdir scripts
    echo ✅ 创建目录: scripts
) else (
    echo ℹ️  目录已存在: scripts
)

:: 设置环境配置
echo.
echo ⚙️  设置环境配置...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ 环境配置文件已创建: .env
        echo ⚠️  请编辑 .env 文件配置必要的环境变量
    ) else (
        echo ⚠️  .env.example 文件不存在，创建默认配置...
        (
            echo # 服务器配置
            echo PORT=3000
            echo NODE_ENV=development
            echo.
            echo # 数据库配置
            echo DATABASE_PATH=./data/accounting.db
            echo.
            echo # JWT配置
            echo JWT_SECRET=your-super-secret-jwt-key-for-development-only
            echo JWT_EXPIRES_IN=7d
            echo JWT_REFRESH_SECRET=your-super-secret-refresh-key-for-development-only
            echo JWT_REFRESH_EXPIRES_IN=30d
            echo.
            echo # 微信小程序配置
            echo WECHAT_APP_ID=test-app-id
            echo WECHAT_APP_SECRET=test-app-secret
            echo.
            echo # 文件上传配置
            echo UPLOAD_DIR=./uploads
            echo MAX_FILE_SIZE=5242880
            echo MAX_FILES=5
            echo.
            echo # 日志配置
            echo LOG_LEVEL=info
            echo LOG_FILE=./logs/app.log
            echo.
            echo # CORS配置
            echo CORS_ORIGIN=*
            echo.
            echo # 安全配置
            echo RATE_LIMIT_WINDOW_MS=900000
            echo RATE_LIMIT_MAX_REQUESTS=100
        ) > .env
        echo ✅ 默认环境配置文件已创建
    )
) else (
    echo ℹ️  环境配置文件已存在: .env
)

:: 安装依赖
echo.
echo 📦 安装项目依赖...
if exist "package-lock.json" (
    call npm ci
) else (
    call npm install
)

if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

:: 初始化数据库
echo.
echo 🗄️  初始化数据库...
if exist "scripts\init-database.js" (
    node scripts\init-database.js
    if errorlevel 1 (
        echo ⚠️  数据库初始化失败，但可以继续
    ) else (
        echo ✅ 数据库初始化完成
    )
) else (
    echo ⚠️  数据库初始化脚本不存在，跳过数据库初始化
)

:: 显示完成信息
echo.
echo 🎉 项目设置完成！
echo.
echo 📋 项目信息：
echo   - 项目目录: %CD%
for /f "tokens=1" %%i in ('node -v') do echo   - Node.js 版本: %%i
for /f "tokens=1" %%i in ('npm -v') do echo   - npm 版本: %%i
echo   - 数据库: SQLite (./data/accounting.db)
echo.
echo 🚀 启动命令：
echo   开发模式: npm run dev
echo   生产构建: npm run build
echo   生产启动: npm start
echo   简化服务器: node start-simple.js
echo.
echo 🔗 访问地址：
echo   健康检查: http://localhost:3000/health
echo   API文档: http://localhost:3000/api
echo.

:: 询问是否启动开发服务器
set /p "START_SERVER=是否现在启动开发服务器？(y/N): "
if /i "%START_SERVER%"=="y" (
    echo.
    echo 🚀 启动开发服务器...
    call npm run dev
) else (
    echo.
    echo ℹ️  稍后可以使用 'npm run dev' 启动开发服务器
    pause
)

endlocal
