#!/bin/bash

# 记账小程序服务端项目完整设置脚本
# 自动完成环境检查、依赖安装、数据库初始化等工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查Node.js版本
check_node_version() {
    if ! command_exists node; then
        log_error "Node.js 未安装，请先安装 Node.js >= 16.0.0"
        echo "安装方法："
        echo "  macOS: brew install node"
        echo "  Ubuntu: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs"
        echo "  或访问: https://nodejs.org"
        exit 1
    fi

    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="16.0.0"

    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        log_error "Node.js 版本过低，需要 >= $REQUIRED_VERSION，当前版本: $NODE_VERSION"
        log_info "建议使用 NVM 安装最新版本："
        echo "  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
        echo "  nvm install 18"
        echo "  nvm use 18"
        exit 1
    fi

    log_success "Node.js 版本检查通过: $NODE_VERSION"
}

# 检查npm版本
check_npm_version() {
    if ! command_exists npm; then
        log_error "npm 未安装"
        exit 1
    fi

    NPM_VERSION=$(npm -v)
    log_success "npm 版本: $NPM_VERSION"
}

# 创建必要的目录
create_directories() {
    log_info "创建项目目录..."
    
    directories=("data" "uploads" "logs" "scripts")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "创建目录: $dir"
        else
            log_info "目录已存在: $dir"
        fi
    done
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 设置环境配置
setup_environment() {
    log_info "设置环境配置..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_success "环境配置文件已创建: .env"
            log_warning "请编辑 .env 文件配置必要的环境变量"
        else
            log_warning ".env.example 文件不存在，创建默认配置..."
            cat > .env << 'EOF'
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DATABASE_PATH=./data/accounting.db

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-for-development-only
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-for-development-only
JWT_REFRESH_EXPIRES_IN=30d

# 微信小程序配置
WECHAT_APP_ID=test-app-id
WECHAT_APP_SECRET=test-app-secret

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=5242880
MAX_FILES=5

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS配置
CORS_ORIGIN=*

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
EOF
            log_success "默认环境配置文件已创建"
        fi
    else
        log_info "环境配置文件已存在: .env"
    fi
}

# 初始化数据库
initialize_database() {
    log_info "初始化数据库..."
    
    if [ -f "scripts/init-database.js" ]; then
        node scripts/init-database.js
        log_success "数据库初始化完成"
    else
        log_warning "数据库初始化脚本不存在，跳过数据库初始化"
    fi
}

# 运行测试
run_tests() {
    log_info "运行项目测试..."
    
    if npm run test --silent >/dev/null 2>&1; then
        log_success "所有测试通过"
    else
        log_warning "测试失败或未配置测试"
    fi
}

# 启动开发服务器
start_dev_server() {
    log_info "准备启动开发服务器..."
    
    echo ""
    echo "🎉 项目设置完成！"
    echo ""
    echo "📋 项目信息："
    echo "  - 项目目录: $(pwd)"
    echo "  - Node.js 版本: $(node -v)"
    echo "  - npm 版本: $(npm -v)"
    echo "  - 数据库: SQLite (./data/accounting.db)"
    echo ""
    echo "🚀 启动命令："
    echo "  开发模式: npm run dev"
    echo "  生产构建: npm run build"
    echo "  生产启动: npm start"
    echo "  简化服务器: node start-simple.js"
    echo ""
    echo "🔗 访问地址："
    echo "  健康检查: http://localhost:3000/health"
    echo "  API文档: http://localhost:3000/api"
    echo ""
    
    read -p "是否现在启动开发服务器？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "启动开发服务器..."
        npm run dev
    else
        log_info "稍后可以使用 'npm run dev' 启动开发服务器"
    fi
}

# 主函数
main() {
    echo "🚀 记账小程序服务端项目设置"
    echo "================================"
    echo ""
    
    # 检查当前目录
    if [ ! -f "package.json" ]; then
        log_error "未找到 package.json 文件，请确保在正确的项目目录中运行此脚本"
        exit 1
    fi
    
    # 执行设置步骤
    check_node_version
    check_npm_version
    create_directories
    setup_environment
    install_dependencies
    initialize_database
    # run_tests  # 可选：运行测试
    start_dev_server
}

# 错误处理
trap 'log_error "脚本执行失败，请检查错误信息"; exit 1' ERR

# 执行主函数
main "$@"
