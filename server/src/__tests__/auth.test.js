"use strict";
/**
 * 认证接口测试
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const setup_1 = require("./setup");
const auth_routes_1 = __importDefault(require("../routes/auth.routes"));
const response_1 = require("../utils/response");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// Mock axios for WeChat API calls
jest.mock('axios');
const mockedAxios = jest.mocked(require('axios'));
describe('Auth Routes', () => {
    let app;
    let db;
    beforeAll(async () => {
        db = await (0, setup_1.createTestDatabase)();
        // 创建测试应用
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use(response_1.responseMiddleware);
        app.use('/api/auth', auth_routes_1.default);
    });
    afterAll(async () => {
        await (0, setup_1.closeTestDatabase)(db);
    });
    beforeEach(async () => {
        await (0, setup_1.cleanTestDatabase)(db);
        jest.clearAllMocks();
    });
    describe('POST /api/auth/login', () => {
        it('应该成功登录新用户', async () => {
            // Mock 微信API响应
            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    openid: 'test-openid-123',
                    session_key: 'test-session-key-123'
                }
            });
            const loginData = {
                code: 'test-wx-code',
                userInfo: {
                    nickName: '测试用户',
                    avatarUrl: 'https://example.com/avatar.jpg',
                    gender: 1,
                    city: '深圳',
                    province: '广东',
                    country: '中国'
                }
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '登录成功',
                data: {
                    token: expect.any(String),
                    refreshToken: expect.any(String),
                    user: {
                        id: expect.any(Number),
                        openid: 'test-openid-123',
                        nickname: '测试用户'
                    }
                }
            });
            // 验证JWT token
            const decoded = jsonwebtoken_1.default.verify(response.body.data.token, process.env.JWT_SECRET);
            expect(decoded.userId).toBeDefined();
            expect(decoded.openid).toBe('test-openid-123');
        });
        it('应该成功登录已存在用户', async () => {
            // 先创建用户
            const existingUser = await (0, setup_1.createTestUser)(db, {
                openid: 'existing-openid-123',
                nickname: '已存在用户'
            });
            // Mock 微信API响应
            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    openid: 'existing-openid-123',
                    session_key: 'new-session-key'
                }
            });
            const loginData = {
                code: 'test-wx-code',
                userInfo: {
                    nickName: '更新后的用户名',
                    avatarUrl: 'https://example.com/new-avatar.jpg',
                    gender: 2
                }
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(200);
            expect(response.body.data.user.nickname).toBe('更新后的用户名');
            expect(response.body.data.user.id).toBe(existingUser.id);
        });
        it('应该支持不传用户信息的静默登录', async () => {
            // Mock 微信API响应
            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    openid: 'silent-login-openid',
                    session_key: 'silent-session-key'
                }
            });
            const loginData = {
                code: 'test-wx-code'
                // 不传 userInfo
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '登录成功',
                data: {
                    token: expect.any(String),
                    refreshToken: expect.any(String),
                    user: {
                        openid: 'silent-login-openid',
                        nickname: null // 静默登录时昵称为空
                    }
                }
            });
        });
        it('应该在缺少code时返回400错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send({})
                .expect(400);
            expect(response.body).toMatchObject({
                code: 400,
                message: '缺少微信授权码'
            });
        });
        it('应该在微信API调用失败时返回错误', async () => {
            // Mock 微信API错误响应
            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    errcode: 40013,
                    errmsg: 'invalid appid'
                }
            });
            const loginData = {
                code: 'invalid-code'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(400);
            expect(response.body.message).toContain('微信API错误');
        });
        it('应该在网络错误时返回503错误', async () => {
            // Mock 网络错误
            mockedAxios.get.mockRejectedValueOnce(new Error('Network Error'));
            const loginData = {
                code: 'test-code'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(503);
            expect(response.body.message).toBe('微信登录服务暂时不可用');
        });
    });
    describe('POST /api/auth/refresh', () => {
        it('应该成功刷新token', async () => {
            // 先创建用户并获取refresh token
            const user = await (0, setup_1.createTestUser)(db);
            const refreshToken = jsonwebtoken_1.default.sign({ userId: user.id, openid: user.openid }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/refresh')
                .send({ refreshToken })
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '令牌刷新成功',
                data: {
                    token: expect.any(String),
                    refreshToken: expect.any(String)
                }
            });
            // 验证新的token
            const decoded = jsonwebtoken_1.default.verify(response.body.data.token, process.env.JWT_SECRET);
            expect(decoded.userId).toBe(user.id);
        });
        it('应该在缺少refreshToken时返回400错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/refresh')
                .send({})
                .expect(400);
            expect(response.body.message).toBe('缺少刷新令牌');
        });
        it('应该在无效refreshToken时返回401错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/refresh')
                .send({ refreshToken: 'invalid-token' })
                .expect(401);
            expect(response.body.message).toBe('刷新令牌无效');
        });
        it('应该在用户不存在时返回401错误', async () => {
            const invalidRefreshToken = jsonwebtoken_1.default.sign({ userId: 99999, openid: 'non-existent' }, process.env.JWT_REFRESH_SECRET, { expiresIn: '30d' });
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/refresh')
                .send({ refreshToken: invalidRefreshToken })
                .expect(401);
            expect(response.body.message).toBe('用户不存在');
        });
    });
    describe('POST /api/auth/logout', () => {
        it('应该成功登出', async () => {
            const user = await (0, setup_1.createTestUser)(db);
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/logout')
                .send({ userId: user.id })
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '登出成功'
            });
        });
        it('应该在不传userId时也能成功', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/auth/logout')
                .send({})
                .expect(200);
            expect(response.body.message).toBe('登出成功');
        });
    });
    describe('GET /api/auth/verify', () => {
        it('应该在有效token时验证成功', async () => {
            const user = await (0, setup_1.createTestUser)(db);
            const token = jsonwebtoken_1.default.sign({ userId: user.id, openid: user.openid }, process.env.JWT_SECRET, { expiresIn: '7d' });
            const response = await (0, supertest_1.default)(app)
                .get('/api/auth/verify')
                .set('Authorization', `Bearer ${token}`)
                .expect(200);
            expect(response.body.message).toBe('令牌有效');
        });
        it('应该在缺少token时返回401错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/auth/verify')
                .expect(401);
            expect(response.body.message).toBe('缺少访问令牌');
        });
        it('应该在无效token时返回401错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/auth/verify')
                .set('Authorization', 'Bearer invalid-token')
                .expect(401);
            expect(response.body.message).toBe('缺少访问令牌');
        });
    });
});
