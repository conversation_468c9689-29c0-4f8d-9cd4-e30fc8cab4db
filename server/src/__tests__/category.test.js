"use strict";
/**
 * 分类接口测试
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const setup_1 = require("./setup");
const category_routes_1 = __importDefault(require("../routes/category.routes"));
const response_1 = require("../utils/response");
const auth_middleware_1 = require("../middleware/auth.middleware");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
describe('Category Routes', () => {
    let app;
    let db;
    let testUser;
    let authToken;
    beforeAll(async () => {
        db = await (0, setup_1.createTestDatabase)();
        // 创建测试应用
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use(response_1.responseMiddleware);
        app.use('/api/categories', auth_middleware_1.authMiddleware, category_routes_1.default);
    });
    afterAll(async () => {
        await (0, setup_1.closeTestDatabase)(db);
    });
    beforeEach(async () => {
        await (0, setup_1.cleanTestDatabase)(db);
        // 创建测试用户和认证token
        testUser = await (0, setup_1.createTestUser)(db);
        authToken = jsonwebtoken_1.default.sign({ userId: testUser.id, openid: testUser.openid }, process.env.JWT_SECRET, { expiresIn: '7d' });
    });
    describe('GET /api/categories', () => {
        it('应该返回用户的所有分类', async () => {
            // 创建测试分类
            await (0, setup_1.createTestCategory)(db, testUser.id, { name: '餐饮', type: 'expense' });
            await (0, setup_1.createTestCategory)(db, testUser.id, { name: '工资', type: 'income' });
            const response = await (0, supertest_1.default)(app)
                .get('/api/categories')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'success',
                data: expect.arrayContaining([
                    expect.objectContaining({
                        name: '餐饮',
                        type: 'expense'
                    }),
                    expect.objectContaining({
                        name: '工资',
                        type: 'income'
                    })
                ])
            });
        });
        it('应该支持按类型筛选分类', async () => {
            await (0, setup_1.createTestCategory)(db, testUser.id, { name: '餐饮', type: 'expense' });
            await (0, setup_1.createTestCategory)(db, testUser.id, { name: '工资', type: 'income' });
            const response = await (0, supertest_1.default)(app)
                .get('/api/categories?type=expense')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data).toHaveLength(1);
            expect(response.body.data[0].type).toBe('expense');
        });
        it('应该在未认证时返回401错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/categories')
                .expect(401);
            expect(response.body.message).toContain('未提供认证令牌');
        });
    });
    describe('POST /api/categories', () => {
        it('应该成功创建新分类', async () => {
            const categoryData = {
                name: '交通',
                type: 'expense',
                icon: '🚗',
                color: '#4CAF50'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/categories')
                .set('Authorization', `Bearer ${authToken}`)
                .send(categoryData)
                .expect(201);
            expect(response.body).toMatchObject({
                code: 201,
                message: '分类创建成功',
                data: expect.objectContaining({
                    id: expect.any(Number),
                    name: '交通',
                    type: 'expense',
                    icon: '🚗',
                    color: '#4CAF50',
                    user_id: testUser.id
                })
            });
        });
        it('应该在缺少必填字段时返回400错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/categories')
                .set('Authorization', `Bearer ${authToken}`)
                .send({ name: '测试分类' }) // 缺少type字段
                .expect(400);
            expect(response.body.message).toContain('验证失败');
        });
        it('应该在类型无效时返回400错误', async () => {
            const categoryData = {
                name: '测试分类',
                type: 'invalid_type'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/categories')
                .set('Authorization', `Bearer ${authToken}`)
                .send(categoryData)
                .expect(400);
            expect(response.body.message).toContain('验证失败');
        });
        it('应该在分类名称重复时返回409错误', async () => {
            // 先创建一个分类
            await (0, setup_1.createTestCategory)(db, testUser.id, { name: '餐饮', type: 'expense' });
            const categoryData = {
                name: '餐饮',
                type: 'expense'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/categories')
                .set('Authorization', `Bearer ${authToken}`)
                .send(categoryData)
                .expect(409);
            expect(response.body.message).toBe('分类名称已存在');
        });
    });
    describe('PUT /api/categories/:id', () => {
        it('应该成功更新分类', async () => {
            const category = await (0, setup_1.createTestCategory)(db, testUser.id, { name: '餐饮', type: 'expense' });
            const updateData = {
                name: '美食',
                icon: '🍔',
                color: '#FF5722'
            };
            const response = await (0, supertest_1.default)(app)
                .put(`/api/categories/${category.id}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(updateData)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '分类更新成功',
                data: expect.objectContaining({
                    id: category.id,
                    name: '美食',
                    icon: '🍔',
                    color: '#FF5722'
                })
            });
        });
        it('应该在分类不存在时返回404错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .put('/api/categories/99999')
                .set('Authorization', `Bearer ${authToken}`)
                .send({ name: '不存在的分类' })
                .expect(404);
            expect(response.body.message).toBe('分类不存在');
        });
        it('应该在更新其他用户的分类时返回403错误', async () => {
            // 创建另一个用户的分类
            const otherUser = await (0, setup_1.createTestUser)(db, { openid: 'other-user-openid' });
            const otherCategory = await (0, setup_1.createTestCategory)(db, otherUser.id, { name: '其他用户分类' });
            const response = await (0, supertest_1.default)(app)
                .put(`/api/categories/${otherCategory.id}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({ name: '尝试更新' })
                .expect(403);
            expect(response.body.message).toBe('无权限操作此分类');
        });
    });
    describe('DELETE /api/categories/:id', () => {
        it('应该成功删除分类', async () => {
            const category = await (0, setup_1.createTestCategory)(db, testUser.id, { name: '测试分类' });
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/categories/${category.id}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '分类删除成功'
            });
            // 验证分类已被删除
            const checkResponse = await (0, supertest_1.default)(app)
                .get('/api/categories')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(checkResponse.body.data).not.toContainEqual(expect.objectContaining({ id: category.id }));
        });
        it('应该在分类不存在时返回404错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .delete('/api/categories/99999')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(404);
            expect(response.body.message).toBe('分类不存在');
        });
        it('应该在删除其他用户的分类时返回403错误', async () => {
            const otherUser = await (0, setup_1.createTestUser)(db, { openid: 'other-user-openid' });
            const otherCategory = await (0, setup_1.createTestCategory)(db, otherUser.id, { name: '其他用户分类' });
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/categories/${otherCategory.id}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(403);
            expect(response.body.message).toBe('无权限操作此分类');
        });
        it('应该在分类被交易记录使用时返回409错误', async () => {
            const category = await (0, setup_1.createTestCategory)(db, testUser.id, { name: '有交易的分类' });
            // 创建使用该分类的交易记录
            await db.run(`
        INSERT INTO transactions (user_id, category_id, type, amount, description, date)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [testUser.id, category.id, 'expense', 100, '测试交易', '2024-01-01']);
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/categories/${category.id}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(409);
            expect(response.body.message).toBe('该分类下还有交易记录，无法删除');
        });
    });
    describe('GET /api/categories/:id', () => {
        it('应该返回指定分类的详情', async () => {
            const category = await (0, setup_1.createTestCategory)(db, testUser.id, {
                name: '详情测试分类',
                type: 'income',
                icon: '💰',
                color: '#4CAF50'
            });
            const response = await (0, supertest_1.default)(app)
                .get(`/api/categories/${category.id}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'success',
                data: expect.objectContaining({
                    id: category.id,
                    name: '详情测试分类',
                    type: 'income',
                    icon: '💰',
                    color: '#4CAF50'
                })
            });
        });
        it('应该在分类不存在时返回404错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/categories/99999')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(404);
            expect(response.body.message).toBe('分类不存在');
        });
        it('应该在访问其他用户分类时返回403错误', async () => {
            const otherUser = await (0, setup_1.createTestUser)(db, { openid: 'other-user-openid' });
            const otherCategory = await (0, setup_1.createTestCategory)(db, otherUser.id, { name: '其他用户分类' });
            const response = await (0, supertest_1.default)(app)
                .get(`/api/categories/${otherCategory.id}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(403);
            expect(response.body.message).toBe('无权限访问此分类');
        });
    });
});
