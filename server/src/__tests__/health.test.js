"use strict";
/**
 * 健康检查接口测试
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const setup_1 = require("./setup");
const response_1 = require("../utils/response");
describe('Health Check Routes', () => {
    let app;
    let db;
    beforeAll(async () => {
        db = await (0, setup_1.createTestDatabase)();
        // 创建测试应用
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use(response_1.responseMiddleware);
        // 添加健康检查路由
        app.get('/health', (req, res) => {
            res.success({
                status: 'ok',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || 'development',
                version: process.env.npm_package_version || '1.0.0'
            }, '服务运行正常');
        });
        app.get('/api/health', (req, res) => {
            res.success({
                status: 'ok',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || 'development',
                version: process.env.npm_package_version || '1.0.0',
                database: 'connected'
            }, 'API服务运行正常');
        });
    });
    afterAll(async () => {
        await (0, setup_1.closeTestDatabase)(db);
    });
    describe('GET /health', () => {
        it('应该返回基本健康状态', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '服务运行正常',
                data: expect.objectContaining({
                    status: 'ok',
                    timestamp: expect.any(String),
                    uptime: expect.any(Number),
                    environment: 'test',
                    version: expect.any(String)
                })
            });
        });
        it('应该返回正确的时间戳格式', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            const timestamp = response.body.data.timestamp;
            expect(new Date(timestamp).toISOString()).toBe(timestamp);
        });
        it('应该返回正确的运行时间', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            expect(response.body.data.uptime).toBeGreaterThan(0);
        });
    });
    describe('GET /api/health', () => {
        it('应该返回API健康状态', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health')
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'API服务运行正常',
                data: expect.objectContaining({
                    status: 'ok',
                    timestamp: expect.any(String),
                    uptime: expect.any(Number),
                    environment: 'test',
                    version: expect.any(String),
                    database: 'connected'
                })
            });
        });
        it('应该包含数据库连接状态', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health')
                .expect(200);
            expect(response.body.data.database).toBe('connected');
        });
    });
    describe('健康检查性能测试', () => {
        it('应该在100ms内响应', async () => {
            const startTime = Date.now();
            await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            const responseTime = Date.now() - startTime;
            expect(responseTime).toBeLessThan(100);
        });
        it('应该支持并发请求', async () => {
            const requests = Array.from({ length: 10 }, () => (0, supertest_1.default)(app).get('/health').expect(200));
            const responses = await Promise.all(requests);
            responses.forEach(response => {
                expect(response.body.data.status).toBe('ok');
            });
        });
    });
    describe('错误处理测试', () => {
        it('应该处理不存在的路由', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/non-existent-route')
                .expect(404);
            // 注意：这里的响应格式取决于你的错误处理中间件
            // 如果没有特殊的404处理，可能返回默认的Express 404响应
        });
        it('应该处理无效的HTTP方法', async () => {
            const response = await (0, supertest_1.default)(app)
                .patch('/health') // 使用不支持的方法
                .expect(404);
        });
    });
    describe('响应格式验证', () => {
        it('应该返回正确的Content-Type', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            expect(response.headers['content-type']).toMatch(/application\/json/);
        });
        it('应该包含正确的响应结构', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            // 验证响应结构符合统一格式
            expect(response.body).toHaveProperty('code');
            expect(response.body).toHaveProperty('message');
            expect(response.body).toHaveProperty('data');
            expect(typeof response.body.code).toBe('number');
            expect(typeof response.body.message).toBe('string');
            expect(typeof response.body.data).toBe('object');
        });
        it('应该返回有效的JSON', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            expect(() => JSON.parse(JSON.stringify(response.body))).not.toThrow();
        });
    });
    describe('环境变量测试', () => {
        it('应该正确识别测试环境', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            expect(response.body.data.environment).toBe('test');
        });
        it('应该返回版本信息', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/health')
                .expect(200);
            expect(response.body.data.version).toBeDefined();
            expect(typeof response.body.data.version).toBe('string');
        });
    });
});
