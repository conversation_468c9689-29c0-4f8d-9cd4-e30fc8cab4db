"use strict";
/**
 * Jest 测试设置文件
 * 配置测试环境和全局设置
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testDb = exports.createTestTransaction = exports.createTestCategory = exports.createTestUser = exports.closeTestDatabase = exports.cleanTestDatabase = exports.createTestDatabase = void 0;
const sqlite3_1 = __importDefault(require("sqlite3"));
const sqlite_1 = require("sqlite");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.DATABASE_PATH = ':memory:'; // 使用内存数据库
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret';
process.env.WECHAT_APP_ID = 'test-app-id';
process.env.WECHAT_APP_SECRET = 'test-app-secret';
// 全局测试数据库实例
let testDb;
/**
 * 创建测试数据库
 */
async function createTestDatabase() {
    const db = await (0, sqlite_1.open)({
        filename: ':memory:',
        driver: sqlite3_1.default.Database
    });
    // 读取并执行数据库初始化脚本
    const schemaPath = path_1.default.join(__dirname, '../database/schema.sql');
    if (fs_1.default.existsSync(schemaPath)) {
        const schema = fs_1.default.readFileSync(schemaPath, 'utf8');
        await db.exec(schema);
    }
    else {
        // 如果没有schema文件，手动创建表结构
        await createTestTables(db);
    }
    return db;
}
exports.createTestDatabase = createTestDatabase;
/**
 * 创建测试表结构
 */
async function createTestTables(db) {
    // 用户表
    await db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      openid TEXT UNIQUE NOT NULL,
      session_key TEXT,
      nickname TEXT,
      avatar_url TEXT,
      gender INTEGER DEFAULT 0,
      city TEXT,
      province TEXT,
      country TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login_at DATETIME
    )
  `);
    // 分类表
    await db.exec(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      icon TEXT,
      color TEXT,
      sort_order INTEGER DEFAULT 0,
      is_system INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);
    // 交易记录表
    await db.exec(`
    CREATE TABLE IF NOT EXISTS transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      category_id INTEGER NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      amount DECIMAL(10,2) NOT NULL,
      description TEXT,
      date DATE NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE RESTRICT
    )
  `);
    // 文件表
    await db.exec(`
    CREATE TABLE IF NOT EXISTS files (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      filename TEXT NOT NULL,
      original_name TEXT NOT NULL,
      mime_type TEXT NOT NULL,
      size INTEGER NOT NULL,
      path TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);
}
/**
 * 清理测试数据库
 */
async function cleanTestDatabase(db) {
    await db.exec('DELETE FROM transactions');
    await db.exec('DELETE FROM categories');
    await db.exec('DELETE FROM files');
    await db.exec('DELETE FROM users');
}
exports.cleanTestDatabase = cleanTestDatabase;
/**
 * 关闭测试数据库
 */
async function closeTestDatabase(db) {
    await db.close();
}
exports.closeTestDatabase = closeTestDatabase;
/**
 * 创建测试用户
 */
async function createTestUser(db, userData = {}) {
    const defaultUser = {
        openid: 'test-openid-' + Date.now(),
        session_key: 'test-session-key',
        nickname: '测试用户',
        avatar_url: 'https://example.com/avatar.jpg',
        gender: 1,
        city: '深圳',
        province: '广东',
        country: '中国'
    };
    const user = { ...defaultUser, ...userData };
    const result = await db.run(`
    INSERT INTO users (openid, session_key, nickname, avatar_url, gender, city, province, country)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `, [user.openid, user.session_key, user.nickname, user.avatar_url, user.gender, user.city, user.province, user.country]);
    return {
        id: result.lastID,
        ...user,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    };
}
exports.createTestUser = createTestUser;
/**
 * 创建测试分类
 */
async function createTestCategory(db, userId, categoryData = {}) {
    const defaultCategory = {
        name: '测试分类',
        type: 'expense',
        icon: '🛒',
        color: '#FF6B6B',
        sort_order: 0,
        is_system: 0
    };
    const category = { ...defaultCategory, ...categoryData };
    const result = await db.run(`
    INSERT INTO categories (user_id, name, type, icon, color, sort_order, is_system)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `, [userId, category.name, category.type, category.icon, category.color, category.sort_order, category.is_system]);
    return {
        id: result.lastID,
        user_id: userId,
        ...category,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    };
}
exports.createTestCategory = createTestCategory;
/**
 * 创建测试交易记录
 */
async function createTestTransaction(db, userId, categoryId, transactionData = {}) {
    const defaultTransaction = {
        type: 'expense',
        amount: 100.00,
        description: '测试交易',
        date: new Date().toISOString().split('T')[0]
    };
    const transaction = { ...defaultTransaction, ...transactionData };
    const result = await db.run(`
    INSERT INTO transactions (user_id, category_id, type, amount, description, date)
    VALUES (?, ?, ?, ?, ?, ?)
  `, [userId, categoryId, transaction.type, transaction.amount, transaction.description, transaction.date]);
    return {
        id: result.lastID,
        user_id: userId,
        category_id: categoryId,
        ...transaction,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    };
}
exports.createTestTransaction = createTestTransaction;
// 全局设置
beforeAll(async () => {
    try {
        exports.testDb = testDb = await createTestDatabase();
    }
    catch (error) {
        console.error('创建测试数据库失败:', error);
        throw error;
    }
});
afterAll(async () => {
    try {
        if (testDb) {
            await closeTestDatabase(testDb);
        }
    }
    catch (error) {
        console.error('关闭测试数据库失败:', error);
    }
});
