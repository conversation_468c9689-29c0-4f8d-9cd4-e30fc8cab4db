/**
 * Jest 测试设置文件
 * 配置测试环境和全局设置
 */

import { Database } from 'sqlite';
import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';
import fs from 'fs';

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.DATABASE_PATH = ':memory:'; // 使用内存数据库
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret';
process.env.WECHAT_APP_ID = 'test-app-id';
process.env.WECHAT_APP_SECRET = 'test-app-secret';

// 全局测试数据库实例
let testDb: Database;

/**
 * 创建测试数据库
 */
export async function createTestDatabase(): Promise<Database> {
  const db = await open({
    filename: ':memory:',
    driver: sqlite3.Database
  });

  // 读取并执行数据库初始化脚本
  const schemaPath = path.join(__dirname, '../database/schema.sql');
  if (fs.existsSync(schemaPath)) {
    const schema = fs.readFileSync(schemaPath, 'utf8');
    await db.exec(schema);
  } else {
    // 如果没有schema文件，手动创建表结构
    await createTestTables(db);
  }

  return db;
}

/**
 * 创建测试表结构
 */
async function createTestTables(db: Database): Promise<void> {
  // 用户表
  await db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      openid TEXT UNIQUE NOT NULL,
      session_key TEXT,
      nickname TEXT,
      avatar_url TEXT,
      gender INTEGER DEFAULT 0,
      city TEXT,
      province TEXT,
      country TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login_at DATETIME
    )
  `);

  // 分类表
  await db.exec(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      icon TEXT,
      color TEXT,
      sort_order INTEGER DEFAULT 0,
      is_system INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);

  // 交易记录表
  await db.exec(`
    CREATE TABLE IF NOT EXISTS transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      category_id INTEGER NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      amount DECIMAL(10,2) NOT NULL,
      description TEXT,
      date DATE NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE RESTRICT
    )
  `);

  // 文件表
  await db.exec(`
    CREATE TABLE IF NOT EXISTS files (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      filename TEXT NOT NULL,
      original_name TEXT NOT NULL,
      mime_type TEXT NOT NULL,
      size INTEGER NOT NULL,
      path TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);
}

/**
 * 清理测试数据库
 */
export async function cleanTestDatabase(db: Database): Promise<void> {
  await db.exec('DELETE FROM transactions');
  await db.exec('DELETE FROM categories');
  await db.exec('DELETE FROM files');
  await db.exec('DELETE FROM users');
}

/**
 * 关闭测试数据库
 */
export async function closeTestDatabase(db: Database): Promise<void> {
  await db.close();
}

/**
 * 创建测试用户
 */
export async function createTestUser(db: Database, userData: any = {}): Promise<any> {
  const defaultUser = {
    openid: 'test-openid-' + Date.now(),
    session_key: 'test-session-key',
    nickname: '测试用户',
    avatar_url: 'https://example.com/avatar.jpg',
    gender: 1,
    city: '深圳',
    province: '广东',
    country: '中国'
  };

  const user = { ...defaultUser, ...userData };
  
  const result = await db.run(`
    INSERT INTO users (openid, session_key, nickname, avatar_url, gender, city, province, country)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `, [user.openid, user.session_key, user.nickname, user.avatar_url, user.gender, user.city, user.province, user.country]);

  return {
    id: result.lastID,
    ...user,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

/**
 * 创建测试分类
 */
export async function createTestCategory(db: Database, userId: number, categoryData: any = {}): Promise<any> {
  const defaultCategory = {
    name: '测试分类',
    type: 'expense',
    icon: '🛒',
    color: '#FF6B6B',
    sort_order: 0,
    is_system: 0
  };

  const category = { ...defaultCategory, ...categoryData };
  
  const result = await db.run(`
    INSERT INTO categories (user_id, name, type, icon, color, sort_order, is_system)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `, [userId, category.name, category.type, category.icon, category.color, category.sort_order, category.is_system]);

  return {
    id: result.lastID,
    user_id: userId,
    ...category,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

/**
 * 创建测试交易记录
 */
export async function createTestTransaction(db: Database, userId: number, categoryId: number, transactionData: any = {}): Promise<any> {
  const defaultTransaction = {
    type: 'expense',
    amount: 100.00,
    description: '测试交易',
    date: new Date().toISOString().split('T')[0]
  };

  const transaction = { ...defaultTransaction, ...transactionData };
  
  const result = await db.run(`
    INSERT INTO transactions (user_id, category_id, type, amount, description, date)
    VALUES (?, ?, ?, ?, ?, ?)
  `, [userId, categoryId, transaction.type, transaction.amount, transaction.description, transaction.date]);

  return {
    id: result.lastID,
    user_id: userId,
    category_id: categoryId,
    ...transaction,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

// 全局设置
beforeAll(async () => {
  try {
    testDb = await createTestDatabase();
  } catch (error) {
    console.error('创建测试数据库失败:', error);
    throw error;
  }
});

afterAll(async () => {
  try {
    if (testDb) {
      await closeTestDatabase(testDb);
    }
  } catch (error) {
    console.error('关闭测试数据库失败:', error);
  }
});

// 导出测试数据库实例
export { testDb };
