"use strict";
/**
 * 统计接口测试
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const setup_1 = require("./setup");
const statistics_routes_1 = __importDefault(require("../routes/statistics.routes"));
const response_1 = require("../utils/response");
const auth_middleware_1 = require("../middleware/auth.middleware");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
describe('Statistics Routes', () => {
    let app;
    let db;
    let testUser;
    let expenseCategory;
    let incomeCategory;
    let authToken;
    beforeAll(async () => {
        db = await (0, setup_1.createTestDatabase)();
        // 创建测试应用
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use(response_1.responseMiddleware);
        app.use('/api/statistics', auth_middleware_1.authMiddleware, statistics_routes_1.default);
    });
    afterAll(async () => {
        await (0, setup_1.closeTestDatabase)(db);
    });
    beforeEach(async () => {
        await (0, setup_1.cleanTestDatabase)(db);
        // 创建测试用户、分类和认证token
        testUser = await (0, setup_1.createTestUser)(db);
        expenseCategory = await (0, setup_1.createTestCategory)(db, testUser.id, { name: '支出分类', type: 'expense' });
        incomeCategory = await (0, setup_1.createTestCategory)(db, testUser.id, { name: '收入分类', type: 'income' });
        authToken = jsonwebtoken_1.default.sign({ userId: testUser.id, openid: testUser.openid }, process.env.JWT_SECRET, { expiresIn: '7d' });
        // 创建测试数据
        await (0, setup_1.createTestTransaction)(db, testUser.id, expenseCategory.id, {
            type: 'expense',
            amount: 100,
            date: '2024-01-15'
        });
        await (0, setup_1.createTestTransaction)(db, testUser.id, expenseCategory.id, {
            type: 'expense',
            amount: 200,
            date: '2024-01-20'
        });
        await (0, setup_1.createTestTransaction)(db, testUser.id, incomeCategory.id, {
            type: 'income',
            amount: 500,
            date: '2024-01-10'
        });
    });
    describe('GET /api/statistics/overview', () => {
        it('应该返回统计概览数据', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/overview')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'success',
                data: expect.objectContaining({
                    totalIncome: 500,
                    totalExpense: 300,
                    balance: 200,
                    transactionCount: 3,
                    categoryCount: 2
                })
            });
        });
        it('应该支持按月份筛选', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/overview?year=2024&month=1')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data).toMatchObject({
                totalIncome: 500,
                totalExpense: 300,
                balance: 200
            });
        });
        it('应该在未认证时返回401错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/overview')
                .expect(401);
            expect(response.body.message).toContain('未提供认证令牌');
        });
    });
    describe('GET /api/statistics/monthly', () => {
        it('应该返回月度统计数据', async () => {
            // 创建不同月份的数据
            await (0, setup_1.createTestTransaction)(db, testUser.id, expenseCategory.id, {
                type: 'expense',
                amount: 150,
                date: '2024-02-15'
            });
            await (0, setup_1.createTestTransaction)(db, testUser.id, incomeCategory.id, {
                type: 'income',
                amount: 300,
                date: '2024-02-10'
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/monthly?year=2024')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'success',
                data: expect.objectContaining({
                    year: 2024,
                    months: expect.arrayContaining([
                        expect.objectContaining({
                            month: 1,
                            income: 500,
                            expense: 300,
                            balance: 200,
                            transactionCount: 3
                        }),
                        expect.objectContaining({
                            month: 2,
                            income: 300,
                            expense: 150,
                            balance: 150,
                            transactionCount: 2
                        })
                    ])
                })
            });
        });
        it('应该在年份无效时返回400错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/monthly?year=invalid')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(400);
            expect(response.body.message).toContain('验证失败');
        });
    });
    describe('GET /api/statistics/category', () => {
        it('应该返回分类统计数据', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/category')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'success',
                data: expect.arrayContaining([
                    expect.objectContaining({
                        category_id: expenseCategory.id,
                        category_name: '支出分类',
                        type: 'expense',
                        total_amount: 300,
                        transaction_count: 2,
                        percentage: expect.any(Number)
                    }),
                    expect.objectContaining({
                        category_id: incomeCategory.id,
                        category_name: '收入分类',
                        type: 'income',
                        total_amount: 500,
                        transaction_count: 1,
                        percentage: expect.any(Number)
                    })
                ])
            });
        });
        it('应该支持按类型筛选', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/category?type=expense')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data).toHaveLength(1);
            expect(response.body.data[0].type).toBe('expense');
        });
        it('应该支持按日期范围筛选', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/category?startDate=2024-01-01&endDate=2024-01-31')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data).toHaveLength(2);
        });
    });
    describe('GET /api/statistics/trend', () => {
        it('应该返回趋势分析数据', async () => {
            // 创建更多测试数据
            for (let day = 1; day <= 7; day++) {
                await (0, setup_1.createTestTransaction)(db, testUser.id, expenseCategory.id, {
                    type: 'expense',
                    amount: day * 10,
                    date: `2024-01-${day.toString().padStart(2, '0')}`
                });
            }
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/trend?period=week&startDate=2024-01-01&endDate=2024-01-07')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'success',
                data: expect.objectContaining({
                    period: 'week',
                    data: expect.arrayContaining([
                        expect.objectContaining({
                            date: expect.any(String),
                            income: expect.any(Number),
                            expense: expect.any(Number),
                            balance: expect.any(Number)
                        })
                    ]),
                    summary: expect.objectContaining({
                        totalIncome: expect.any(Number),
                        totalExpense: expect.any(Number),
                        averageDaily: expect.any(Number),
                        trend: expect.any(String)
                    })
                })
            });
        });
        it('应该支持不同的时间周期', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/trend?period=month&year=2024&month=1')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data.period).toBe('month');
        });
        it('应该在无效周期时返回400错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/trend?period=invalid')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(400);
            expect(response.body.message).toContain('验证失败');
        });
        it('应该在缺少必要参数时返回400错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/trend?period=week')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(400);
            expect(response.body.message).toContain('验证失败');
        });
    });
    describe('GET /api/statistics/comparison', () => {
        it('应该返回对比分析数据', async () => {
            // 创建上个月的数据
            await (0, setup_1.createTestTransaction)(db, testUser.id, expenseCategory.id, {
                type: 'expense',
                amount: 250,
                date: '2023-12-15'
            });
            await (0, setup_1.createTestTransaction)(db, testUser.id, incomeCategory.id, {
                type: 'income',
                amount: 400,
                date: '2023-12-10'
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/comparison?currentYear=2024&currentMonth=1&previousYear=2023&previousMonth=12')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'success',
                data: expect.objectContaining({
                    current: expect.objectContaining({
                        income: 500,
                        expense: 300,
                        balance: 200
                    }),
                    previous: expect.objectContaining({
                        income: 400,
                        expense: 250,
                        balance: 150
                    }),
                    comparison: expect.objectContaining({
                        incomeChange: expect.any(Number),
                        expenseChange: expect.any(Number),
                        balanceChange: expect.any(Number),
                        incomeChangePercent: expect.any(Number),
                        expenseChangePercent: expect.any(Number)
                    })
                })
            });
        });
        it('应该在参数不完整时返回400错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/comparison?currentYear=2024')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(400);
            expect(response.body.message).toContain('验证失败');
        });
    });
    describe('数据权限测试', () => {
        it('应该只返回当前用户的统计数据', async () => {
            // 创建另一个用户和数据
            const otherUser = await (0, setup_1.createTestUser)(db, { openid: 'other-user' });
            const otherCategory = await (0, setup_1.createTestCategory)(db, otherUser.id, { name: '其他用户分类' });
            await (0, setup_1.createTestTransaction)(db, otherUser.id, otherCategory.id, {
                type: 'expense',
                amount: 1000,
                date: '2024-01-15'
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/statistics/overview')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            // 应该只包含当前用户的数据，不包含其他用户的1000元支出
            expect(response.body.data.totalExpense).toBe(300);
            expect(response.body.data.transactionCount).toBe(3);
        });
    });
});
