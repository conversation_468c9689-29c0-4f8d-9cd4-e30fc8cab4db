/**
 * 交易记录接口测试
 */

import request from 'supertest';
import express from 'express';
import { Database } from 'sqlite';
import { createTestDatabase, cleanTestDatabase, closeTestDatabase, createTestUser, createTestCategory, createTestTransaction } from './setup';
import transactionRoutes from '../routes/transaction.routes';
import { responseMiddleware } from '../utils/response';
import { authMiddleware } from '../middleware/auth.middleware';
import jwt from 'jsonwebtoken';

describe('Transaction Routes', () => {
  let app: express.Application;
  let db: Database;
  let testUser: any;
  let testCategory: any;
  let authToken: string;

  beforeAll(async () => {
    db = await createTestDatabase();
    
    // 创建测试应用
    app = express();
    app.use(express.json());
    app.use(responseMiddleware);
    app.use('/api/transactions', authMiddleware, transactionRoutes);
  });

  afterAll(async () => {
    await closeTestDatabase(db);
  });

  beforeEach(async () => {
    await cleanTestDatabase(db);
    
    // 创建测试用户、分类和认证token
    testUser = await createTestUser(db);
    testCategory = await createTestCategory(db, testUser.id, { name: '测试分类', type: 'expense' });
    authToken = jwt.sign(
      { userId: testUser.id, openid: testUser.openid },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );
  });

  describe('GET /api/transactions', () => {
    it('应该返回用户的交易记录列表', async () => {
      // 创建测试交易记录
      await createTestTransaction(db, testUser.id, testCategory.id, {
        amount: 100.50,
        description: '午餐',
        date: '2024-01-15'
      });
      await createTestTransaction(db, testUser.id, testCategory.id, {
        amount: 50.00,
        description: '咖啡',
        date: '2024-01-16'
      });

      const response = await request(app)
        .get('/api/transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        code: 200,
        message: 'success',
        data: {
          transactions: expect.arrayContaining([
            expect.objectContaining({
              amount: 100.50,
              description: '午餐'
            }),
            expect.objectContaining({
              amount: 50.00,
              description: '咖啡'
            })
          ]),
          pagination: expect.objectContaining({
            total: 2,
            page: 1,
            limit: 20
          })
        }
      });
    });

    it('应该支持分页查询', async () => {
      // 创建多条记录
      for (let i = 1; i <= 25; i++) {
        await createTestTransaction(db, testUser.id, testCategory.id, {
          amount: i * 10,
          description: `交易${i}`,
          date: '2024-01-01'
        });
      }

      const response = await request(app)
        .get('/api/transactions?page=2&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.transactions).toHaveLength(10);
      expect(response.body.data.pagination).toMatchObject({
        total: 25,
        page: 2,
        limit: 10,
        totalPages: 3
      });
    });

    it('应该支持按类型筛选', async () => {
      const incomeCategory = await createTestCategory(db, testUser.id, { name: '收入分类', type: 'income' });
      
      await createTestTransaction(db, testUser.id, testCategory.id, { type: 'expense', amount: 100 });
      await createTestTransaction(db, testUser.id, incomeCategory.id, { type: 'income', amount: 200 });

      const response = await request(app)
        .get('/api/transactions?type=income')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.transactions).toHaveLength(1);
      expect(response.body.data.transactions[0].type).toBe('income');
    });

    it('应该支持按日期范围筛选', async () => {
      await createTestTransaction(db, testUser.id, testCategory.id, { date: '2024-01-01' });
      await createTestTransaction(db, testUser.id, testCategory.id, { date: '2024-01-15' });
      await createTestTransaction(db, testUser.id, testCategory.id, { date: '2024-02-01' });

      const response = await request(app)
        .get('/api/transactions?startDate=2024-01-01&endDate=2024-01-31')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.transactions).toHaveLength(2);
    });

    it('应该在未认证时返回401错误', async () => {
      const response = await request(app)
        .get('/api/transactions')
        .expect(401);

      expect(response.body.message).toContain('未提供认证令牌');
    });
  });

  describe('POST /api/transactions', () => {
    it('应该成功创建新交易记录', async () => {
      const transactionData = {
        category_id: testCategory.id,
        type: 'expense',
        amount: 150.75,
        description: '超市购物',
        date: '2024-01-20'
      };

      const response = await request(app)
        .post('/api/transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(transactionData)
        .expect(201);

      expect(response.body).toMatchObject({
        code: 201,
        message: '交易记录创建成功',
        data: expect.objectContaining({
          id: expect.any(Number),
          category_id: testCategory.id,
          type: 'expense',
          amount: 150.75,
          description: '超市购物',
          date: '2024-01-20',
          user_id: testUser.id
        })
      });
    });

    it('应该在缺少必填字段时返回400错误', async () => {
      const response = await request(app)
        .post('/api/transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ amount: 100 }) // 缺少必填字段
        .expect(400);

      expect(response.body.message).toContain('验证失败');
    });

    it('应该在金额为负数时返回400错误', async () => {
      const transactionData = {
        category_id: testCategory.id,
        type: 'expense',
        amount: -100,
        date: '2024-01-20'
      };

      const response = await request(app)
        .post('/api/transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(transactionData)
        .expect(400);

      expect(response.body.message).toContain('验证失败');
    });

    it('应该在分类不存在时返回404错误', async () => {
      const transactionData = {
        category_id: 99999,
        type: 'expense',
        amount: 100,
        date: '2024-01-20'
      };

      const response = await request(app)
        .post('/api/transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(transactionData)
        .expect(404);

      expect(response.body.message).toBe('分类不存在');
    });

    it('应该在使用其他用户分类时返回403错误', async () => {
      const otherUser = await createTestUser(db, { openid: 'other-user' });
      const otherCategory = await createTestCategory(db, otherUser.id, { name: '其他用户分类' });

      const transactionData = {
        category_id: otherCategory.id,
        type: 'expense',
        amount: 100,
        date: '2024-01-20'
      };

      const response = await request(app)
        .post('/api/transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(transactionData)
        .expect(403);

      expect(response.body.message).toBe('无权限使用此分类');
    });
  });

  describe('GET /api/transactions/:id', () => {
    it('应该返回指定交易记录的详情', async () => {
      const transaction = await createTestTransaction(db, testUser.id, testCategory.id, {
        amount: 88.88,
        description: '详情测试',
        date: '2024-01-25'
      });

      const response = await request(app)
        .get(`/api/transactions/${transaction.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        code: 200,
        message: 'success',
        data: expect.objectContaining({
          id: transaction.id,
          amount: 88.88,
          description: '详情测试',
          date: '2024-01-25'
        })
      });
    });

    it('应该在交易记录不存在时返回404错误', async () => {
      const response = await request(app)
        .get('/api/transactions/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.message).toBe('交易记录不存在');
    });

    it('应该在访问其他用户交易记录时返回403错误', async () => {
      const otherUser = await createTestUser(db, { openid: 'other-user' });
      const otherCategory = await createTestCategory(db, otherUser.id, { name: '其他分类' });
      const otherTransaction = await createTestTransaction(db, otherUser.id, otherCategory.id);

      const response = await request(app)
        .get(`/api/transactions/${otherTransaction.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.message).toBe('无权限访问此交易记录');
    });
  });

  describe('PUT /api/transactions/:id', () => {
    it('应该成功更新交易记录', async () => {
      const transaction = await createTestTransaction(db, testUser.id, testCategory.id, {
        amount: 100,
        description: '原始描述'
      });

      const updateData = {
        amount: 200,
        description: '更新后的描述'
      };

      const response = await request(app)
        .put(`/api/transactions/${transaction.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        code: 200,
        message: '交易记录更新成功',
        data: expect.objectContaining({
          id: transaction.id,
          amount: 200,
          description: '更新后的描述'
        })
      });
    });

    it('应该在交易记录不存在时返回404错误', async () => {
      const response = await request(app)
        .put('/api/transactions/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ amount: 100 })
        .expect(404);

      expect(response.body.message).toBe('交易记录不存在');
    });

    it('应该在更新其他用户交易记录时返回403错误', async () => {
      const otherUser = await createTestUser(db, { openid: 'other-user' });
      const otherCategory = await createTestCategory(db, otherUser.id, { name: '其他分类' });
      const otherTransaction = await createTestTransaction(db, otherUser.id, otherCategory.id);

      const response = await request(app)
        .put(`/api/transactions/${otherTransaction.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ amount: 100 })
        .expect(403);

      expect(response.body.message).toBe('无权限操作此交易记录');
    });
  });

  describe('DELETE /api/transactions/:id', () => {
    it('应该成功删除交易记录', async () => {
      const transaction = await createTestTransaction(db, testUser.id, testCategory.id);

      const response = await request(app)
        .delete(`/api/transactions/${transaction.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        code: 200,
        message: '交易记录删除成功'
      });

      // 验证记录已被删除
      const checkResponse = await request(app)
        .get(`/api/transactions/${transaction.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('应该在交易记录不存在时返回404错误', async () => {
      const response = await request(app)
        .delete('/api/transactions/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.message).toBe('交易记录不存在');
    });

    it('应该在删除其他用户交易记录时返回403错误', async () => {
      const otherUser = await createTestUser(db, { openid: 'other-user' });
      const otherCategory = await createTestCategory(db, otherUser.id, { name: '其他分类' });
      const otherTransaction = await createTestTransaction(db, otherUser.id, otherCategory.id);

      const response = await request(app)
        .delete(`/api/transactions/${otherTransaction.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.message).toBe('无权限操作此交易记录');
    });
  });

  describe('POST /api/transactions/batch', () => {
    it('应该成功批量操作交易记录', async () => {
      const transaction1 = await createTestTransaction(db, testUser.id, testCategory.id);
      const transaction2 = await createTestTransaction(db, testUser.id, testCategory.id);

      const batchData = {
        action: 'delete',
        transactionIds: [transaction1.id, transaction2.id]
      };

      const response = await request(app)
        .post('/api/transactions/batch')
        .set('Authorization', `Bearer ${authToken}`)
        .send(batchData)
        .expect(200);

      expect(response.body).toMatchObject({
        code: 200,
        message: '批量操作完成',
        data: expect.objectContaining({
          successCount: 2,
          failCount: 0
        })
      });
    });

    it('应该在无效操作类型时返回400错误', async () => {
      const batchData = {
        action: 'invalid_action',
        transactionIds: [1, 2]
      };

      const response = await request(app)
        .post('/api/transactions/batch')
        .set('Authorization', `Bearer ${authToken}`)
        .send(batchData)
        .expect(400);

      expect(response.body.message).toContain('验证失败');
    });

    it('应该在交易记录ID为空时返回400错误', async () => {
      const batchData = {
        action: 'delete',
        transactionIds: []
      };

      const response = await request(app)
        .post('/api/transactions/batch')
        .set('Authorization', `Bearer ${authToken}`)
        .send(batchData)
        .expect(400);

      expect(response.body.message).toContain('验证失败');
    });
  });
});
