"use strict";
/**
 * 文件上传接口测试
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const setup_1 = require("./setup");
const upload_routes_1 = __importDefault(require("../routes/upload.routes"));
const response_1 = require("../utils/response");
const auth_middleware_1 = require("../middleware/auth.middleware");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Mock multer for file upload testing
jest.mock('multer', () => {
    const multer = jest.requireActual('multer');
    return {
        ...multer,
        diskStorage: jest.fn(() => ({
            _handleFile: jest.fn(),
            _removeFile: jest.fn()
        })),
        memoryStorage: jest.fn(() => ({
            _handleFile: jest.fn(),
            _removeFile: jest.fn()
        }))
    };
});
describe('Upload Routes', () => {
    let app;
    let db;
    let testUser;
    let authToken;
    beforeAll(async () => {
        db = await (0, setup_1.createTestDatabase)();
        // 创建测试应用
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use(response_1.responseMiddleware);
        app.use('/api/upload', auth_middleware_1.authMiddleware, upload_routes_1.default);
    });
    afterAll(async () => {
        await (0, setup_1.closeTestDatabase)(db);
    });
    beforeEach(async () => {
        await (0, setup_1.cleanTestDatabase)(db);
        // 创建测试用户和认证token
        testUser = await (0, setup_1.createTestUser)(db);
        authToken = jsonwebtoken_1.default.sign({ userId: testUser.id, openid: testUser.openid }, process.env.JWT_SECRET, { expiresIn: '7d' });
    });
    describe('POST /api/upload/image', () => {
        it('应该成功上传图片文件', async () => {
            // 创建测试图片文件
            const testImagePath = path_1.default.join(__dirname, 'fixtures', 'test-image.jpg');
            // 确保测试文件存在
            if (!fs_1.default.existsSync(path_1.default.dirname(testImagePath))) {
                fs_1.default.mkdirSync(path_1.default.dirname(testImagePath), { recursive: true });
            }
            // 创建一个简单的测试图片文件
            const testImageBuffer = Buffer.from('fake-image-data');
            fs_1.default.writeFileSync(testImagePath, testImageBuffer);
            const response = await (0, supertest_1.default)(app)
                .post('/api/upload/image')
                .set('Authorization', `Bearer ${authToken}`)
                .attach('image', testImagePath)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '图片上传成功',
                data: expect.objectContaining({
                    id: expect.any(Number),
                    filename: expect.any(String),
                    original_name: 'test-image.jpg',
                    mime_type: expect.any(String),
                    size: expect.any(Number),
                    url: expect.any(String)
                })
            });
            // 清理测试文件
            if (fs_1.default.existsSync(testImagePath)) {
                fs_1.default.unlinkSync(testImagePath);
            }
        });
        it('应该在未提供文件时返回400错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/upload/image')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(400);
            expect(response.body.message).toBe('请选择要上传的图片');
        });
        it('应该在文件类型不支持时返回400错误', async () => {
            // 创建非图片文件
            const testFilePath = path_1.default.join(__dirname, 'fixtures', 'test-file.txt');
            if (!fs_1.default.existsSync(path_1.default.dirname(testFilePath))) {
                fs_1.default.mkdirSync(path_1.default.dirname(testFilePath), { recursive: true });
            }
            fs_1.default.writeFileSync(testFilePath, 'test content');
            const response = await (0, supertest_1.default)(app)
                .post('/api/upload/image')
                .set('Authorization', `Bearer ${authToken}`)
                .attach('image', testFilePath)
                .expect(400);
            expect(response.body.message).toContain('不支持的文件类型');
            // 清理测试文件
            if (fs_1.default.existsSync(testFilePath)) {
                fs_1.default.unlinkSync(testFilePath);
            }
        });
        it('应该在文件过大时返回400错误', async () => {
            // 创建大文件
            const testFilePath = path_1.default.join(__dirname, 'fixtures', 'large-image.jpg');
            if (!fs_1.default.existsSync(path_1.default.dirname(testFilePath))) {
                fs_1.default.mkdirSync(path_1.default.dirname(testFilePath), { recursive: true });
            }
            // 创建6MB的文件（超过5MB限制）
            const largeBuffer = Buffer.alloc(6 * 1024 * 1024, 'a');
            fs_1.default.writeFileSync(testFilePath, largeBuffer);
            const response = await (0, supertest_1.default)(app)
                .post('/api/upload/image')
                .set('Authorization', `Bearer ${authToken}`)
                .attach('image', testFilePath)
                .expect(400);
            expect(response.body.message).toContain('文件大小超出限制');
            // 清理测试文件
            if (fs_1.default.existsSync(testFilePath)) {
                fs_1.default.unlinkSync(testFilePath);
            }
        });
        it('应该在未认证时返回401错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/upload/image')
                .expect(401);
            expect(response.body.message).toContain('未提供认证令牌');
        });
    });
    describe('POST /api/upload/multiple', () => {
        it('应该成功上传多个文件', async () => {
            // 创建多个测试文件
            const testFiles = ['test1.jpg', 'test2.png'];
            const testFilePaths = [];
            for (const filename of testFiles) {
                const filePath = path_1.default.join(__dirname, 'fixtures', filename);
                if (!fs_1.default.existsSync(path_1.default.dirname(filePath))) {
                    fs_1.default.mkdirSync(path_1.default.dirname(filePath), { recursive: true });
                }
                fs_1.default.writeFileSync(filePath, Buffer.from('fake-image-data'));
                testFilePaths.push(filePath);
            }
            const request_builder = (0, supertest_1.default)(app)
                .post('/api/upload/multiple')
                .set('Authorization', `Bearer ${authToken}`);
            // 添加多个文件
            for (const filePath of testFilePaths) {
                request_builder.attach('images', filePath);
            }
            const response = await request_builder.expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '文件上传成功',
                data: expect.objectContaining({
                    uploaded: expect.arrayContaining([
                        expect.objectContaining({
                            filename: expect.any(String),
                            original_name: expect.any(String),
                            url: expect.any(String)
                        })
                    ]),
                    failed: expect.any(Array),
                    summary: expect.objectContaining({
                        total: testFiles.length,
                        success: expect.any(Number),
                        failed: expect.any(Number)
                    })
                })
            });
            // 清理测试文件
            for (const filePath of testFilePaths) {
                if (fs_1.default.existsSync(filePath)) {
                    fs_1.default.unlinkSync(filePath);
                }
            }
        });
        it('应该在文件数量超限时返回400错误', async () => {
            // 创建超过限制数量的文件
            const testFiles = Array.from({ length: 6 }, (_, i) => `test${i + 1}.jpg`);
            const testFilePaths = [];
            for (const filename of testFiles) {
                const filePath = path_1.default.join(__dirname, 'fixtures', filename);
                if (!fs_1.default.existsSync(path_1.default.dirname(filePath))) {
                    fs_1.default.mkdirSync(path_1.default.dirname(filePath), { recursive: true });
                }
                fs_1.default.writeFileSync(filePath, Buffer.from('fake-image-data'));
                testFilePaths.push(filePath);
            }
            const request_builder = (0, supertest_1.default)(app)
                .post('/api/upload/multiple')
                .set('Authorization', `Bearer ${authToken}`);
            for (const filePath of testFilePaths) {
                request_builder.attach('images', filePath);
            }
            const response = await request_builder.expect(400);
            expect(response.body.message).toContain('文件数量超出限制');
            // 清理测试文件
            for (const filePath of testFilePaths) {
                if (fs_1.default.existsSync(filePath)) {
                    fs_1.default.unlinkSync(filePath);
                }
            }
        });
    });
    describe('GET /api/upload/files', () => {
        it('应该返回用户上传的文件列表', async () => {
            // 先在数据库中创建一些文件记录
            await db.run(`
        INSERT INTO files (user_id, filename, original_name, mime_type, size, path)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [testUser.id, 'test-file-1.jpg', 'original-1.jpg', 'image/jpeg', 1024, '/uploads/test-file-1.jpg']);
            await db.run(`
        INSERT INTO files (user_id, filename, original_name, mime_type, size, path)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [testUser.id, 'test-file-2.png', 'original-2.png', 'image/png', 2048, '/uploads/test-file-2.png']);
            const response = await (0, supertest_1.default)(app)
                .get('/api/upload/files')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: 'success',
                data: expect.objectContaining({
                    files: expect.arrayContaining([
                        expect.objectContaining({
                            filename: 'test-file-1.jpg',
                            original_name: 'original-1.jpg',
                            mime_type: 'image/jpeg'
                        }),
                        expect.objectContaining({
                            filename: 'test-file-2.png',
                            original_name: 'original-2.png',
                            mime_type: 'image/png'
                        })
                    ]),
                    pagination: expect.objectContaining({
                        total: 2,
                        page: 1,
                        limit: 20
                    })
                })
            });
        });
        it('应该支持分页查询', async () => {
            // 创建多个文件记录
            for (let i = 1; i <= 25; i++) {
                await db.run(`
          INSERT INTO files (user_id, filename, original_name, mime_type, size, path)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [testUser.id, `file-${i}.jpg`, `original-${i}.jpg`, 'image/jpeg', 1024, `/uploads/file-${i}.jpg`]);
            }
            const response = await (0, supertest_1.default)(app)
                .get('/api/upload/files?page=2&limit=10')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data.files).toHaveLength(10);
            expect(response.body.data.pagination).toMatchObject({
                total: 25,
                page: 2,
                limit: 10,
                totalPages: 3
            });
        });
        it('应该只返回当前用户的文件', async () => {
            // 创建其他用户的文件
            const otherUser = await (0, setup_1.createTestUser)(db, { openid: 'other-user' });
            await db.run(`
        INSERT INTO files (user_id, filename, original_name, mime_type, size, path)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [otherUser.id, 'other-user-file.jpg', 'other-file.jpg', 'image/jpeg', 1024, '/uploads/other-user-file.jpg']);
            // 创建当前用户的文件
            await db.run(`
        INSERT INTO files (user_id, filename, original_name, mime_type, size, path)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [testUser.id, 'my-file.jpg', 'my-file.jpg', 'image/jpeg', 1024, '/uploads/my-file.jpg']);
            const response = await (0, supertest_1.default)(app)
                .get('/api/upload/files')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body.data.files).toHaveLength(1);
            expect(response.body.data.files[0].filename).toBe('my-file.jpg');
        });
    });
    describe('DELETE /api/upload/files/:id', () => {
        it('应该成功删除文件', async () => {
            // 创建文件记录
            const result = await db.run(`
        INSERT INTO files (user_id, filename, original_name, mime_type, size, path)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [testUser.id, 'delete-test.jpg', 'delete-test.jpg', 'image/jpeg', 1024, '/uploads/delete-test.jpg']);
            const fileId = result.lastID;
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/upload/files/${fileId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            expect(response.body).toMatchObject({
                code: 200,
                message: '文件删除成功'
            });
            // 验证文件记录已被删除
            const file = await db.get('SELECT * FROM files WHERE id = ?', [fileId]);
            expect(file).toBeUndefined();
        });
        it('应该在文件不存在时返回404错误', async () => {
            const response = await (0, supertest_1.default)(app)
                .delete('/api/upload/files/99999')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(404);
            expect(response.body.message).toBe('文件不存在');
        });
        it('应该在删除其他用户文件时返回403错误', async () => {
            const otherUser = await (0, setup_1.createTestUser)(db, { openid: 'other-user' });
            const result = await db.run(`
        INSERT INTO files (user_id, filename, original_name, mime_type, size, path)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [otherUser.id, 'other-file.jpg', 'other-file.jpg', 'image/jpeg', 1024, '/uploads/other-file.jpg']);
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/upload/files/${result.lastID}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(403);
            expect(response.body.message).toBe('无权限删除此文件');
        });
    });
});
