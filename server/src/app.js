"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApp = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const config_1 = require("./config");
const database_1 = require("./database");
const error_middleware_1 = require("./middleware/error.middleware");
const auth_middleware_1 = require("./middleware/auth.middleware");
const response_1 = require("./utils/response");
// 路由导入
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
const category_routes_1 = __importDefault(require("./routes/category.routes"));
const transaction_routes_1 = __importDefault(require("./routes/transaction.routes"));
const statistics_routes_1 = __importDefault(require("./routes/statistics.routes"));
const upload_routes_1 = __importDefault(require("./routes/upload.routes"));
/**
 * 创建Express应用
 */
function createApp() {
    const app = (0, express_1.default)();
    // 基础中间件
    app.use((0, helmet_1.default)()); // 安全头
    app.use((0, cors_1.default)()); // 跨域支持
    app.use((0, morgan_1.default)('combined')); // 日志记录
    app.use(express_1.default.json({ limit: '10mb' })); // JSON解析
    app.use(express_1.default.urlencoded({ extended: true })); // URL编码解析
    app.use(response_1.responseMiddleware); // 统一响应格式
    // 静态文件服务
    app.use('/uploads', express_1.default.static(config_1.config.upload.uploadDir));
    // 图片代理接口（用于小程序访问）
    app.get('/api/image/*', (req, res) => {
        const imagePath = req.params[0];
        const fullPath = path_1.default.join(config_1.config.upload.uploadDir, imagePath);
        // 检查文件是否存在
        if (!fs_1.default.existsSync(fullPath)) {
            return res.status(404).json({ error: '图片不存在' });
        }
        // 设置正确的Content-Type
        const ext = path_1.default.extname(fullPath).toLowerCase();
        const mimeTypes = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp'
        };
        const contentType = mimeTypes[ext] || 'application/octet-stream';
        res.setHeader('Content-Type', contentType);
        res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1年缓存
        // 发送文件
        res.sendFile(fullPath);
    });
    // 健康检查
    app.get('/health', (req, res) => {
        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        });
    });
    app.get('/api/health', (req, res) => {
        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            database: 'connected'
        });
    });
    // API路由
    app.use('/api/auth', auth_routes_1.default);
    app.use('/api/users', auth_middleware_1.authMiddleware, user_routes_1.default);
    app.use('/api/categories', auth_middleware_1.authMiddleware, category_routes_1.default);
    app.use('/api/transactions', auth_middleware_1.authMiddleware, transaction_routes_1.default);
    app.use('/api/statistics', auth_middleware_1.authMiddleware, statistics_routes_1.default);
    app.use('/api/upload', auth_middleware_1.authMiddleware, upload_routes_1.default);
    // 404处理
    app.use('*', (req, res) => {
        res.status(404).json({
            error: 'Not Found',
            message: `Route ${req.originalUrl} not found`
        });
    });
    // 错误处理中间件
    app.use(error_middleware_1.errorHandler);
    return app;
}
exports.createApp = createApp;
/**
 * 启动服务器
 */
async function startServer() {
    try {
        // 验证配置
        (0, config_1.validateConfig)();
        console.log('✅ 配置验证通过');
        // 初始化数据库
        await (0, database_1.initDatabase)();
        console.log('✅ 数据库初始化完成');
        // 创建应用
        const app = createApp();
        // 启动服务器
        const server = app.listen(config_1.config.port, () => {
            console.log(`🚀 服务器启动成功`);
            console.log(`📍 地址: http://localhost:${config_1.config.port}`);
            console.log(`🌍 环境: ${config_1.config.nodeEnv}`);
            console.log(`📊 健康检查: http://localhost:${config_1.config.port}/health`);
        });
        // 优雅关闭
        process.on('SIGTERM', () => {
            console.log('收到SIGTERM信号，开始优雅关闭...');
            server.close(() => {
                console.log('服务器已关闭');
                process.exit(0);
            });
        });
        process.on('SIGINT', () => {
            console.log('收到SIGINT信号，开始优雅关闭...');
            server.close(() => {
                console.log('服务器已关闭');
                process.exit(0);
            });
        });
    }
    catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}
// 如果直接运行此文件，则启动服务器
if (require.main === module) {
    startServer();
}
exports.default = createApp;
