import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import fs from 'fs';
import path from 'path';
import { config, validateConfig } from './config';
import { initDatabase } from './database';
import { errorHandler } from './middleware/error.middleware';
import { authMiddleware } from './middleware/auth.middleware';
import { responseMiddleware } from './utils/response';

// 路由导入
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import categoryRoutes from './routes/category.routes';
import transactionRoutes from './routes/transaction.routes';
import statisticsRoutes from './routes/statistics.routes';
import uploadRoutes from './routes/upload.routes';

/**
 * 创建Express应用
 */
export function createApp(): express.Application {
  const app = express();

  // 基础中间件
  app.use(helmet()); // 安全头
  app.use(cors()); // 跨域支持
  app.use(morgan('combined')); // 日志记录
  app.use(express.json({ limit: '10mb' })); // JSON解析
  app.use(express.urlencoded({ extended: true })); // URL编码解析
  app.use(responseMiddleware); // 统一响应格式

  // 静态文件服务
  app.use('/uploads', express.static(config.upload.uploadDir));

  // 图片代理接口（用于小程序访问）
  app.get('/api/image/*', (req, res) => {
    const imagePath = req.params[0];
    const fullPath = path.join(config.upload.uploadDir, imagePath);

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ error: '图片不存在' });
    }

    // 设置正确的Content-Type
    const ext = path.extname(fullPath).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp'
    };

    const contentType = mimeTypes[ext] || 'application/octet-stream';
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1年缓存

    // 发送文件
    res.sendFile(fullPath);
  });

  // 健康检查
  app.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  });

  app.get('/api/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      database: 'connected'
    });
  });

  // API路由
  app.use('/api/auth', authRoutes);
  app.use('/api/users', authMiddleware, userRoutes);
  app.use('/api/categories', authMiddleware, categoryRoutes);
  app.use('/api/transactions', authMiddleware, transactionRoutes);
  app.use('/api/statistics', authMiddleware, statisticsRoutes);
  app.use('/api/upload', authMiddleware, uploadRoutes);

  // 404处理
  app.use('*', (req, res) => {
    res.status(404).json({
      error: 'Not Found',
      message: `Route ${req.originalUrl} not found`
    });
  });

  // 错误处理中间件
  app.use(errorHandler);

  return app;
}

/**
 * 启动服务器
 */
async function startServer() {
  try {
    // 验证配置
    validateConfig();
    console.log('✅ 配置验证通过');

    // 初始化数据库
    await initDatabase();
    console.log('✅ 数据库初始化完成');

    // 创建应用
    const app = createApp();

    // 启动服务器
    const server = app.listen(config.port, () => {
      console.log(`🚀 服务器启动成功`);
      console.log(`📍 地址: http://localhost:${config.port}`);
      console.log(`🌍 环境: ${config.nodeEnv}`);
      console.log(`📊 健康检查: http://localhost:${config.port}/health`);
    });

    // 优雅关闭
    process.on('SIGTERM', () => {
      console.log('收到SIGTERM信号，开始优雅关闭...');
      server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('收到SIGINT信号，开始优雅关闭...');
      server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

export default createApp;
