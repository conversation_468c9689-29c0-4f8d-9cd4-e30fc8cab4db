"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = __importDefault(require("path"));
// 数据库配置
const config = {
    development: {
        client: 'sqlite3',
        connection: {
            filename: process.env.DB_PATH || path_1.default.join(__dirname, '../../data/accounting.db')
        },
        useNullAsDefault: true,
        migrations: {
            directory: path_1.default.join(__dirname, '../database/migrations'),
            extension: 'ts'
        },
        seeds: {
            directory: path_1.default.join(__dirname, '../database/seeds'),
            extension: 'ts'
        },
        pool: {
            afterCreate: (conn, done) => {
                // 启用外键约束
                conn.run('PRAGMA foreign_keys = ON', done);
            }
        }
    },
    production: {
        client: 'sqlite3',
        connection: {
            filename: process.env.DB_PATH || path_1.default.join(__dirname, '../../data/accounting.db')
        },
        useNullAsDefault: true,
        migrations: {
            directory: path_1.default.join(__dirname, '../database/migrations'),
            extension: 'js'
        },
        seeds: {
            directory: path_1.default.join(__dirname, '../database/seeds'),
            extension: 'js'
        },
        pool: {
            min: 1,
            max: 10,
            afterCreate: (conn, done) => {
                // 启用外键约束
                conn.run('PRAGMA foreign_keys = ON', done);
            }
        }
    },
    test: {
        client: 'sqlite3',
        connection: ':memory:',
        useNullAsDefault: true,
        migrations: {
            directory: path_1.default.join(__dirname, '../database/migrations'),
            extension: 'ts'
        },
        seeds: {
            directory: path_1.default.join(__dirname, '../database/seeds'),
            extension: 'ts'
        },
        pool: {
            afterCreate: (conn, done) => {
                // 启用外键约束
                conn.run('PRAGMA foreign_keys = ON', done);
            }
        }
    }
};
exports.default = config;
