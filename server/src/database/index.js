"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheck = exports.runMigrations = exports.closeDatabase = exports.initDatabase = exports.getDatabase = void 0;
const sqlite3_1 = __importDefault(require("sqlite3"));
const sqlite_1 = require("sqlite");
const config_1 = require("../config");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
// 数据库连接实例
let db = null;
/**
 * 获取数据库连接
 */
async function getDatabase() {
    if (!db) {
        throw new Error('数据库未初始化，请先调用 initDatabase()');
    }
    return db;
}
exports.getDatabase = getDatabase;
/**
 * 初始化数据库
 */
async function initDatabase() {
    try {
        // 确保数据库目录存在
        const dbDir = path_1.default.dirname(config_1.config.database.path);
        if (!fs_1.default.existsSync(dbDir)) {
            fs_1.default.mkdirSync(dbDir, { recursive: true });
        }
        // 打开数据库连接
        db = await (0, sqlite_1.open)({
            filename: config_1.config.database.path,
            driver: sqlite3_1.default.Database
        });
        // 启用外键约束
        await db.exec('PRAGMA foreign_keys = ON');
        // 设置WAL模式以提高并发性能
        await db.exec('PRAGMA journal_mode = WAL');
        // 创建表结构
        await createTables();
        // 插入默认数据
        await insertDefaultData();
        console.log('数据库初始化完成');
    }
    catch (error) {
        console.error('数据库初始化失败:', error);
        throw error;
    }
}
exports.initDatabase = initDatabase;
/**
 * 创建表结构
 */
async function createTables() {
    if (!db)
        throw new Error('数据库未连接');
    // 读取SQL文件
    const schemaPath = path_1.default.join(__dirname, 'schema.sql');
    const schema = fs_1.default.readFileSync(schemaPath, 'utf-8');
    // 执行SQL语句
    await db.exec(schema);
}
/**
 * 插入默认数据
 */
async function insertDefaultData() {
    if (!db)
        throw new Error('数据库未连接');
    // 检查是否已有默认分类数据
    const categoryCount = await db.get('SELECT COUNT(*) as count FROM categories');
    if (categoryCount.count > 0) {
        return; // 已有数据，跳过初始化
    }
    // 默认支出分类
    const expenseCategories = [
        { name: '餐饮', icon: '🍔', color: '#FF6B6B', type: 'expense' },
        { name: '交通', icon: '🚗', color: '#4ECDC4', type: 'expense' },
        { name: '购物', icon: '🛍️', color: '#45B7D1', type: 'expense' },
        { name: '娱乐', icon: '🎮', color: '#96CEB4', type: 'expense' },
        { name: '医疗', icon: '💊', color: '#FFEAA7', type: 'expense' },
        { name: '教育', icon: '📚', color: '#DDA0DD', type: 'expense' },
        { name: '住房', icon: '🏠', color: '#98D8C8', type: 'expense' },
        { name: '通讯', icon: '📱', color: '#F7DC6F', type: 'expense' },
        { name: '服饰', icon: '👕', color: '#BB8FCE', type: 'expense' },
        { name: '其他', icon: '💰', color: '#85C1E9', type: 'expense' }
    ];
    // 默认收入分类
    const incomeCategories = [
        { name: '工资', icon: '💼', color: '#2ECC71', type: 'income' },
        { name: '奖金', icon: '🏆', color: '#F39C12', type: 'income' },
        { name: '投资', icon: '📈', color: '#9B59B6', type: 'income' },
        { name: '兼职', icon: '💻', color: '#1ABC9C', type: 'income' },
        { name: '礼金', icon: '🎁', color: '#E74C3C', type: 'income' },
        { name: '其他', icon: '💰', color: '#34495E', type: 'income' }
    ];
    // 插入分类数据
    const allCategories = [...expenseCategories, ...incomeCategories];
    for (const category of allCategories) {
        await db.run('INSERT INTO categories (name, icon, color, type, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, datetime("now"), datetime("now"))', [category.name, category.icon, category.color, category.type, 0]);
    }
    console.log('默认分类数据插入完成');
}
/**
 * 关闭数据库连接
 */
async function closeDatabase() {
    if (db) {
        await db.close();
        db = null;
        console.log('数据库连接已关闭');
    }
}
exports.closeDatabase = closeDatabase;
/**
 * 执行数据库迁移
 */
async function runMigrations() {
    // 这里可以添加数据库迁移逻辑
    console.log('数据库迁移完成');
}
exports.runMigrations = runMigrations;
/**
 * 数据库健康检查
 */
async function healthCheck() {
    try {
        if (!db)
            return false;
        await db.get('SELECT 1');
        return true;
    }
    catch (error) {
        console.error('数据库健康检查失败:', error);
        return false;
    }
}
exports.healthCheck = healthCheck;
