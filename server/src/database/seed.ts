/**
 * 数据库种子数据
 * 用于初始化系统默认数据
 */

import { getDatabase } from './index';

/**
 * 创建默认分类
 */
export async function createDefaultCategories() {
  const db = await getDatabase();
  
  // 默认支出分类
  const expenseCategories = [
    { name: '餐饮', icon: '🍽️', color: '#FF6B6B', sort_order: 1 },
    { name: '交通', icon: '🚗', color: '#4ECDC4', sort_order: 2 },
    { name: '购物', icon: '🛒', color: '#45B7D1', sort_order: 3 },
    { name: '娱乐', icon: '🎮', color: '#96CEB4', sort_order: 4 },
    { name: '医疗', icon: '🏥', color: '#FFEAA7', sort_order: 5 },
    { name: '教育', icon: '📚', color: '#DDA0DD', sort_order: 6 },
    { name: '住房', icon: '🏠', color: '#98D8C8', sort_order: 7 },
    { name: '通讯', icon: '📱', color: '#F7DC6F', sort_order: 8 },
    { name: '其他', icon: '📦', color: '#BDC3C7', sort_order: 9 }
  ];
  
  // 默认收入分类
  const incomeCategories = [
    { name: '工资', icon: '💰', color: '#2ECC71', sort_order: 1 },
    { name: '奖金', icon: '🎁', color: '#3498DB', sort_order: 2 },
    { name: '投资', icon: '📈', color: '#9B59B6', sort_order: 3 },
    { name: '兼职', icon: '💼', color: '#E67E22', sort_order: 4 },
    { name: '红包', icon: '🧧', color: '#E74C3C', sort_order: 5 },
    { name: '其他', icon: '💎', color: '#1ABC9C', sort_order: 6 }
  ];
  
  // 插入支出分类
  for (let i = 0; i < expenseCategories.length; i++) {
    const category = expenseCategories[i];
    await db.run(`
      INSERT OR IGNORE INTO categories (id, user_id, name, type, icon, color, sort_order, created_at, updated_at)
      VALUES (?, NULL, ?, 'expense', ?, ?, ?, datetime('now'), datetime('now'))
    `, [`cat_expense_${i + 1}`, category.name, category.icon, category.color, category.sort_order]);
  }

  // 插入收入分类
  for (let i = 0; i < incomeCategories.length; i++) {
    const category = incomeCategories[i];
    await db.run(`
      INSERT OR IGNORE INTO categories (id, user_id, name, type, icon, color, sort_order, created_at, updated_at)
      VALUES (?, NULL, ?, 'income', ?, ?, ?, datetime('now'), datetime('now'))
    `, [`cat_income_${i + 1}`, category.name, category.icon, category.color, category.sort_order]);
  }
  
  console.log('默认分类创建完成');
}

/**
 * 为用户复制系统默认分类
 */
export async function copyDefaultCategoriesToUser(userId: number) {
  const db = await getDatabase();
  
  // 检查用户是否已有分类
  const existingCategories = await db.get(
    'SELECT COUNT(*) as count FROM categories WHERE user_id = ?',
    [userId]
  );
  
  if (existingCategories.count > 0) {
    console.log(`用户 ${userId} 已有分类，跳过复制`);
    return;
  }
  
  // 复制系统默认分类给用户
  await db.run(`
    INSERT INTO categories (id, user_id, name, type, icon, color, sort_order, created_at, updated_at)
    SELECT
      'user_' || ? || '_' || substr(id, 5),
      ?,
      name,
      type,
      icon,
      color,
      sort_order,
      datetime('now'),
      datetime('now')
    FROM categories
    WHERE user_id IS NULL
  `, [userId, userId]);
  
  console.log(`为用户 ${userId} 复制默认分类完成`);
}

/**
 * 创建示例交易记录
 */
async function createSampleTransactions(userId: number) {
  const db = await getDatabase();
  
  // 获取用户的分类
  const categories = await db.all(
    'SELECT id, name, type FROM categories WHERE user_id = ? ORDER BY type, sort_order',
    [userId]
  );
  
  if (categories.length === 0) {
    console.log('用户没有分类，无法创建示例交易记录');
    return;
  }
  
  const expenseCategories = categories.filter(c => c.type === 'expense');
  const incomeCategories = categories.filter(c => c.type === 'income');
  
  // 示例交易记录
  const sampleTransactions = [
    // 本月收入
    { categoryId: incomeCategories[0]?.id, type: 'income', amount: 8000, description: '工资收入', date: '2024-01-01' },
    { categoryId: incomeCategories[1]?.id, type: 'income', amount: 1000, description: '年终奖金', date: '2024-01-02' },
    
    // 本月支出
    { categoryId: expenseCategories[0]?.id, type: 'expense', amount: 45.5, description: '午餐', date: '2024-01-03' },
    { categoryId: expenseCategories[1]?.id, type: 'expense', amount: 12, description: '地铁费', date: '2024-01-03' },
    { categoryId: expenseCategories[2]?.id, type: 'expense', amount: 299, description: '购买衣服', date: '2024-01-04' },
    { categoryId: expenseCategories[0]?.id, type: 'expense', amount: 68, description: '晚餐聚会', date: '2024-01-04' },
    { categoryId: expenseCategories[3]?.id, type: 'expense', amount: 35, description: '电影票', date: '2024-01-05' },
    { categoryId: expenseCategories[0]?.id, type: 'expense', amount: 25, description: '早餐', date: '2024-01-06' },
    { categoryId: expenseCategories[1]?.id, type: 'expense', amount: 50, description: '打车费', date: '2024-01-06' },
    { categoryId: expenseCategories[2]?.id, type: 'expense', amount: 128, description: '日用品', date: '2024-01-07' }
  ];
  
  // 插入示例交易记录
  for (const transaction of sampleTransactions) {
    if (transaction.categoryId) {
      await db.run(`
        INSERT INTO transactions (user_id, category_id, type, amount, description, date)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [userId, transaction.categoryId, transaction.type, transaction.amount, transaction.description, transaction.date]);
    }
  }
  
  console.log(`为用户 ${userId} 创建示例交易记录完成`);
}

/**
 * 初始化数据库种子数据
 */
export async function seedDatabase() {
  try {
    console.log('开始初始化数据库种子数据...');
    
    // 创建默认分类
    await createDefaultCategories();
    
    console.log('数据库种子数据初始化完成');
  } catch (error) {
    console.error('数据库种子数据初始化失败:', error);
    throw error;
  }
}

/**
 * 为新用户初始化数据
 */
export async function initUserData(userId: string) {
  try {
    console.log(`开始为用户 ${userId} 初始化数据...`);
    
    // 复制默认分类
    await copyDefaultCategoriesToUser(userId);
    
    // 创建示例交易记录（可选）
    // await createSampleTransactions(userId);
    
    console.log(`用户 ${userId} 数据初始化完成`);
  } catch (error) {
    console.error(`用户 ${userId} 数据初始化失败:`, error);
    throw error;
  }
}

// 如果直接运行此文件，执行种子数据初始化
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('种子数据初始化成功');
      process.exit(0);
    })
    .catch((error) => {
      console.error('种子数据初始化失败:', error);
      process.exit(1);
    });
}
