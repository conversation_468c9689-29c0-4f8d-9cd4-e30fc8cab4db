"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyRefreshToken = exports.generateRefreshToken = exports.generateToken = exports.adminMiddleware = exports.optionalAuthMiddleware = exports.authMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config");
const database_1 = require("../database");
const error_middleware_1 = require("./error.middleware");
/**
 * JWT令牌验证中间件
 */
const authMiddleware = async (req, res, next) => {
    try {
        // 获取Authorization头
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            throw (0, error_middleware_1.unauthorizedError)('缺少访问令牌');
        }
        // 检查Bearer格式
        if (!authHeader.startsWith('Bearer ')) {
            throw (0, error_middleware_1.unauthorizedError)('访问令牌格式错误');
        }
        // 提取令牌
        const token = authHeader.substring(7);
        if (!token) {
            throw (0, error_middleware_1.unauthorizedError)('访问令牌不能为空');
        }
        // 验证JWT令牌
        let decoded;
        try {
            decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
        }
        catch (error) {
            if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                throw (0, error_middleware_1.unauthorizedError)('访问令牌已过期');
            }
            else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                throw (0, error_middleware_1.unauthorizedError)('无效的访问令牌');
            }
            else {
                throw (0, error_middleware_1.unauthorizedError)('令牌验证失败');
            }
        }
        // 检查令牌payload
        if (!decoded.userId || !decoded.openid) {
            throw (0, error_middleware_1.unauthorizedError)('令牌数据不完整');
        }
        // 从数据库获取用户信息
        const db = await (0, database_1.getDatabase)();
        const user = await db.get('SELECT id, openid, nickname, avatar_url, status, created_at FROM users WHERE id = ? AND status = "active"', [decoded.userId]);
        if (!user) {
            throw (0, error_middleware_1.unauthorizedError)('用户不存在或已被禁用');
        }
        // 将用户信息添加到请求对象
        req.user = {
            id: user.id,
            openid: user.openid,
            nickname: user.nickname,
            avatar_url: user.avatar_url
        };
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.authMiddleware = authMiddleware;
/**
 * 可选的身份验证中间件（不强制要求登录）
 */
const optionalAuthMiddleware = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next(); // 没有令牌，继续执行
        }
        const token = authHeader.substring(7);
        if (!token) {
            return next(); // 空令牌，继续执行
        }
        try {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
            if (decoded.userId && decoded.openid) {
                const db = await (0, database_1.getDatabase)();
                const user = await db.get('SELECT id, openid, nickname, avatar_url FROM users WHERE id = ? AND status = "active"', [decoded.userId]);
                if (user) {
                    req.user = {
                        id: user.id,
                        openid: user.openid,
                        nickname: user.nickname,
                        avatar_url: user.avatar_url
                    };
                }
            }
        }
        catch (error) {
            // 令牌无效，但不阻止请求继续
            console.warn('可选身份验证失败:', error.message);
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.optionalAuthMiddleware = optionalAuthMiddleware;
/**
 * 管理员权限验证中间件
 */
const adminMiddleware = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, error_middleware_1.unauthorizedError)('需要登录');
        }
        const db = await (0, database_1.getDatabase)();
        const user = await db.get('SELECT role FROM users WHERE id = ?', [req.user.id]);
        if (!user || user.role !== 'admin') {
            throw new error_middleware_1.AppError('需要管理员权限', 403);
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.adminMiddleware = adminMiddleware;
/**
 * 生成JWT令牌
 */
const generateToken = (payload) => {
    return jsonwebtoken_1.default.sign(payload, config_1.config.jwt.secret, {
        expiresIn: config_1.config.jwt.expiresIn,
        issuer: 'accounting-app',
        audience: 'accounting-app-users'
    });
};
exports.generateToken = generateToken;
/**
 * 生成刷新令牌
 */
const generateRefreshToken = (payload) => {
    return jsonwebtoken_1.default.sign(payload, config_1.config.jwt.refreshSecret, {
        expiresIn: config_1.config.jwt.refreshExpiresIn,
        issuer: 'accounting-app',
        audience: 'accounting-app-users'
    });
};
exports.generateRefreshToken = generateRefreshToken;
/**
 * 验证刷新令牌
 */
const verifyRefreshToken = (token) => {
    try {
        return jsonwebtoken_1.default.verify(token, config_1.config.jwt.refreshSecret);
    }
    catch (error) {
        throw (0, error_middleware_1.unauthorizedError)('无效的刷新令牌');
    }
};
exports.verifyRefreshToken = verifyRefreshToken;
