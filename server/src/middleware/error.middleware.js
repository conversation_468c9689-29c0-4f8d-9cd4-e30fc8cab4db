"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.conflictError = exports.notFoundError = exports.forbiddenError = exports.unauthorizedError = exports.validationError = exports.notFoundHandler = exports.errorHandler = exports.asyncHandler = exports.AppError = void 0;
const config_1 = require("../config");
/**
 * 自定义错误类
 */
class AppError extends Error {
    constructor(message, statusCode = 500) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
/**
 * 异步错误处理包装器
 */
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
/**
 * 全局错误处理中间件
 */
const errorHandler = (error, req, res, next) => {
    let statusCode = 500;
    let message = '服务器内部错误';
    let details = undefined;
    // 如果是自定义错误
    if (error instanceof AppError) {
        statusCode = error.statusCode;
        message = error.message;
    }
    // SQLite错误处理
    else if (error.message.includes('SQLITE_CONSTRAINT')) {
        statusCode = 400;
        if (error.message.includes('UNIQUE')) {
            message = '数据已存在，请检查重复项';
        }
        else if (error.message.includes('FOREIGN KEY')) {
            message = '关联数据不存在';
        }
        else {
            message = '数据约束错误';
        }
    }
    // JSON解析错误
    else if (error instanceof SyntaxError && 'body' in error) {
        statusCode = 400;
        message = '请求数据格式错误';
    }
    // 验证错误
    else if (error.name === 'ValidationError') {
        statusCode = 400;
        message = '数据验证失败';
        details = error.message;
    }
    // JWT错误
    else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = '无效的访问令牌';
    }
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        message = '访问令牌已过期';
    }
    // 记录错误日志
    console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
    });
    // 构建错误响应
    const errorResponse = {
        success: false,
        error: {
            message,
            code: statusCode
        }
    };
    // 开发环境下返回详细错误信息
    if (config_1.config.nodeEnv === 'development') {
        errorResponse.error.details = details || error.message;
        errorResponse.error.stack = error.stack;
    }
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
/**
 * 404错误处理
 */
const notFoundHandler = (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            message: `路由 ${req.originalUrl} 不存在`,
            code: 404
        }
    });
};
exports.notFoundHandler = notFoundHandler;
/**
 * 验证错误处理
 */
const validationError = (message, details) => {
    const error = new AppError(message, 400);
    if (details) {
        error.details = details;
    }
    return error;
};
exports.validationError = validationError;
/**
 * 权限错误
 */
const unauthorizedError = (message = '未授权访问') => {
    return new AppError(message, 401);
};
exports.unauthorizedError = unauthorizedError;
/**
 * 禁止访问错误
 */
const forbiddenError = (message = '禁止访问') => {
    return new AppError(message, 403);
};
exports.forbiddenError = forbiddenError;
/**
 * 资源不存在错误
 */
const notFoundError = (message = '资源不存在') => {
    return new AppError(message, 404);
};
exports.notFoundError = notFoundError;
/**
 * 冲突错误
 */
const conflictError = (message = '资源冲突') => {
    return new AppError(message, 409);
};
exports.conflictError = conflictError;
