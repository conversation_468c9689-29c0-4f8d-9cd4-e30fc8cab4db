import { Request, Response, NextFunction } from 'express';
import { validationError } from './error.middleware';

/**
 * 验证规则接口
 */
interface ValidationRule {
  field: string;
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: any[];
  custom?: (value: any) => boolean | string;
}

/**
 * 数据验证中间件
 */
export const validate = (rules: ValidationRule[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors: string[] = [];
      const data = { ...req.body, ...req.query, ...req.params };

      for (const rule of rules) {
        const value = data[rule.field];
        const fieldName = rule.field;

        // 必填验证
        if (rule.required && (value === undefined || value === null || value === '')) {
          errors.push(`${fieldName} 是必填项`);
          continue;
        }

        // 如果字段不存在且非必填，跳过后续验证
        if (value === undefined || value === null) {
          continue;
        }

        // 类型验证和转换
        if (rule.type) {
          // 尝试类型转换
          if (rule.type === 'number' && typeof value === 'string' && !isNaN(Number(value))) {
            value = Number(value);
            req.body[rule.field] = value; // 更新请求体中的值
          }

          if (!validateType(value, rule.type)) {
            errors.push(`${fieldName} 类型错误，期望 ${rule.type}`);
            continue;
          }
        }

        // 字符串长度验证
        if (rule.type === 'string' && typeof value === 'string') {
          if (rule.minLength && value.length < rule.minLength) {
            errors.push(`${fieldName} 长度不能少于 ${rule.minLength} 个字符`);
          }
          if (rule.maxLength && value.length > rule.maxLength) {
            errors.push(`${fieldName} 长度不能超过 ${rule.maxLength} 个字符`);
          }
        }

        // 数值范围验证
        if (rule.type === 'number' && typeof value === 'number') {
          if (rule.min !== undefined && value < rule.min) {
            errors.push(`${fieldName} 不能小于 ${rule.min}`);
          }
          if (rule.max !== undefined && value > rule.max) {
            errors.push(`${fieldName} 不能大于 ${rule.max}`);
          }
        }

        // 正则表达式验证
        if (rule.pattern && typeof value === 'string') {
          if (!rule.pattern.test(value)) {
            errors.push(`${fieldName} 格式不正确`);
          }
        }

        // 枚举值验证
        if (rule.enum && !rule.enum.includes(value)) {
          errors.push(`${fieldName} 必须是以下值之一: ${rule.enum.join(', ')}`);
        }

        // 自定义验证
        if (rule.custom) {
          const result = rule.custom(value);
          if (result !== true) {
            errors.push(typeof result === 'string' ? result : `${fieldName} 验证失败`);
          }
        }
      }

      if (errors.length > 0) {
        console.error('验证失败详情:', {
          url: req.url,
          method: req.method,
          body: req.body,
          errors
        });
        throw validationError('数据验证失败', errors);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * 类型验证函数
 */
function validateType(value: any, type: string): boolean {
  switch (type) {
    case 'string':
      return typeof value === 'string';
    case 'number':
      return typeof value === 'number' && !isNaN(value);
    case 'boolean':
      return typeof value === 'boolean';
    case 'array':
      return Array.isArray(value);
    case 'object':
      return typeof value === 'object' && value !== null && !Array.isArray(value);
    default:
      return true;
  }
}

/**
 * 常用验证规则
 */
export const commonRules = {
  // 用户相关
  openid: {
    field: 'openid',
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100
  },
  nickname: {
    field: 'nickname',
    type: 'string' as const,
    maxLength: 50
  },
  
  // 分类相关
  categoryName: {
    field: 'name',
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 20
  },
  categoryType: {
    field: 'type',
    required: true,
    type: 'string' as const,
    enum: ['income', 'expense']
  },
  categoryIcon: {
    field: 'icon',
    type: 'string' as const,
    maxLength: 10
  },
  categoryColor: {
    field: 'color',
    type: 'string' as const,
    pattern: /^#[0-9A-Fa-f]{6}$/
  },
  
  // 交易相关
  transactionAmount: {
    field: 'amount',
    required: true,
    type: 'number' as const,
    min: 0.01,
    max: 999999999
  },
  transactionType: {
    field: 'type',
    required: true,
    type: 'string' as const,
    enum: ['income', 'expense']
  },
  transactionDate: {
    field: 'date',
    required: true,
    type: 'string' as const,
    pattern: /^\d{4}-\d{2}-\d{2}$/
  },
  description: {
    field: 'description',
    type: 'string' as const,
    maxLength: 200
  },
  
  // 分页相关
  page: {
    field: 'page',
    type: 'number' as const,
    min: 1
  },
  pageSize: {
    field: 'pageSize',
    type: 'number' as const,
    min: 1,
    max: 100
  },
  
  // ID相关
  id: {
    field: 'id',
    required: true,
    type: 'string' as const,
    minLength: 1
  }
};

/**
 * 预定义验证器
 */
export const validators = {
  // 用户登录
  login: validate([
    commonRules.openid
  ]),
  
  // 创建分类
  createCategory: validate([
    commonRules.categoryName,
    commonRules.categoryType,
    commonRules.categoryIcon,
    commonRules.categoryColor
  ]),
  
  // 更新分类
  updateCategory: validate([
    commonRules.id,
    { ...commonRules.categoryName, required: false },
    { ...commonRules.categoryType, required: false },
    commonRules.categoryIcon,
    commonRules.categoryColor
  ]),
  
  // 创建交易
  createTransaction: validate([
    commonRules.transactionAmount,
    commonRules.transactionType,
    commonRules.transactionDate,
    { field: 'categoryId', required: true, type: 'string' as const },
    { field: 'description', required: false, type: 'string' as const },
    { field: 'imageUrl', required: false, type: 'string' as const }
  ]),
  
  // 更新交易
  updateTransaction: validate([
    commonRules.id,
    { ...commonRules.transactionAmount, required: false },
    { ...commonRules.transactionType, required: false },
    { ...commonRules.transactionDate, required: false },
    { field: 'category_id', type: 'string' as const },
    commonRules.description
  ]),
  
  // 分页查询（查询参数都是字符串，在路由中转换为数字）
  pagination: validate([
    { field: 'page', type: 'string', required: false },
    { field: 'pageSize', type: 'string', required: false },
    { field: 'startDate', type: 'string', required: false },
    { field: 'endDate', type: 'string', required: false },
    { field: 'type', type: 'string', required: false },
    { field: 'categoryId', type: 'string', required: false }
  ]),
  
  // ID参数
  idParam: validate([
    commonRules.id
  ])
};
