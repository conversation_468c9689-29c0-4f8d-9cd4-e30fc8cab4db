"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const error_middleware_1 = require("../middleware/error.middleware");
const response_1 = require("../utils/response");
const auth_service_1 = __importDefault(require("../services/auth.service"));
const router = (0, express_1.Router)();
// 应用响应中间件
router.use(response_1.responseMiddleware);
/**
 * 微信小程序登录
 * POST /api/auth/login
 */
router.post('/login', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { code, userInfo } = req.body;
    if (!code) {
        return res.badRequest('缺少微信授权码');
    }
    const result = await auth_service_1.default.login({ code, userInfo });
    return res.success(result, '登录成功');
}));
/**
 * 刷新访问令牌
 * POST /api/auth/refresh
 */
router.post('/refresh', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    if (!refreshToken) {
        return res.badRequest('缺少刷新令牌');
    }
    const result = await auth_service_1.default.refreshToken(refreshToken);
    return res.success(result, '令牌刷新成功');
}));
/**
 * 用户登出
 * POST /api/auth/logout
 */
router.post('/logout', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { userId } = req.body;
    if (userId) {
        await auth_service_1.default.logout(userId);
    }
    return res.success(null, '登出成功');
}));
/**
 * 验证令牌
 * GET /api/auth/verify
 */
router.get('/verify', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.unauthorized('缺少访问令牌');
    }
    // 这里可以添加令牌验证逻辑
    // 由于我们有auth中间件，这个端点主要用于客户端检查令牌有效性
    return res.success(null, '令牌有效');
}));
exports.default = router;
