import { Router, Request, Response } from 'express';
import { asyncHandler } from '../middleware/error.middleware';
import { validators } from '../middleware/validation.middleware';
import { responseMiddleware } from '../utils/response';
import authService from '../services/auth.service';

const router = Router();

// 应用响应中间件
router.use(responseMiddleware);

/**
 * 微信小程序登录
 * POST /api/auth/login
 */
router.post('/login', asyncHandler(async (req: Request, res: Response) => {
  const { code, userInfo } = req.body;

  if (!code) {
    return res.badRequest('缺少微信授权码');
  }

  const result = await authService.login({ code, userInfo });

  return res.success(result, '登录成功');
}));

/**
 * 刷新访问令牌
 * POST /api/auth/refresh
 */
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.badRequest('缺少刷新令牌');
  }

  const result = await authService.refreshToken(refreshToken);

  return res.success(result, '令牌刷新成功');
}));

/**
 * 用户登出
 * POST /api/auth/logout
 */
router.post('/logout', asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.body;

  if (userId) {
    await authService.logout(userId);
  }

  return res.success(null, '登出成功');
}));

/**
 * 验证令牌
 * GET /api/auth/verify
 */
router.get('/verify', asyncHandler(async (req: Request, res: Response) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.unauthorized('缺少访问令牌');
  }

  // 这里可以添加令牌验证逻辑
  // 由于我们有auth中间件，这个端点主要用于客户端检查令牌有效性

  return res.success(null, '令牌有效');
}));

export default router;
