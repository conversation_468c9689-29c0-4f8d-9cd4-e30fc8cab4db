"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const error_middleware_1 = require("../middleware/error.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const category_service_1 = __importDefault(require("../services/category.service"));
const router = (0, express_1.Router)();
/**
 * 获取分类列表
 * GET /api/categories
 */
router.get('/', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { type } = req.query;
    console.log('获取分类请求 - userId:', userId, 'type:', type);
    // 获取所有分类（不分页，前端期望简单数组）
    const result = await category_service_1.default.getCategories(userId, type, 1, 1000 // 获取所有分类
    );
    console.log('分类查询结果:', result);
    // 返回简单数组格式，符合前端期望
    res.success(result.list);
}));
/**
 * 根据ID获取分类
 * GET /api/categories/:id
 */
router.get('/:id', validation_middleware_1.validators.idParam, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { id } = req.params;
    const category = await category_service_1.default.getCategoryById(id, userId);
    res.success(category);
}));
/**
 * 创建分类
 * POST /api/categories
 */
router.post('/', validation_middleware_1.validators.createCategory, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const categoryData = req.body;
    const category = await category_service_1.default.createCategory(userId, categoryData);
    res.status(201).success(category, '分类创建成功');
}));
/**
 * 更新分类
 * PUT /api/categories/:id
 */
router.put('/:id', validation_middleware_1.validators.updateCategory, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { id } = req.params;
    const updateData = req.body;
    const category = await category_service_1.default.updateCategory(id, userId, updateData);
    res.success(category, '分类更新成功');
}));
/**
 * 删除分类
 * DELETE /api/categories/:id
 */
router.delete('/:id', validation_middleware_1.validators.idParam, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { id } = req.params;
    await category_service_1.default.deleteCategory(id, userId);
    res.success(null, '分类删除成功');
}));
/**
 * 批量更新分类排序
 * PUT /api/categories/order
 */
router.put('/order', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { categoryOrders } = req.body;
    if (!Array.isArray(categoryOrders)) {
        return res.badRequest('categoryOrders 必须是数组');
    }
    await category_service_1.default.updateCategoriesOrder(userId, categoryOrders);
    res.success(null, '分类排序更新成功');
}));
/**
 * 获取分类统计
 * GET /api/categories/stats
 */
router.get('/stats', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { type } = req.query;
    const stats = await category_service_1.default.getCategoryStats(userId, type);
    res.success(stats);
}));
exports.default = router;
