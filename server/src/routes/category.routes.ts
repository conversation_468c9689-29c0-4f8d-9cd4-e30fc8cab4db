import { Router } from 'express';
import { asyncHandler } from '../middleware/error.middleware';
import { validators } from '../middleware/validation.middleware';
import categoryService from '../services/category.service';

const router = Router();

/**
 * 获取分类列表
 * GET /api/categories
 */
router.get('/', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { type } = req.query;

  console.log('获取分类请求 - userId:', userId, 'type:', type);

  // 获取所有分类（不分页，前端期望简单数组）
  const result = await categoryService.getCategories(
    userId,
    type as 'income' | 'expense' | undefined,
    1,
    1000 // 获取所有分类
  );

  console.log('分类查询结果:', result);

  // 返回简单数组格式，符合前端期望
  res.success(result.list);
}));

/**
 * 根据ID获取分类
 * GET /api/categories/:id
 */
router.get('/:id', validators.idParam, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  
  const category = await categoryService.getCategoryById(id, userId);
  
  res.success(category);
}));

/**
 * 创建分类
 * POST /api/categories
 */
router.post('/', validators.createCategory, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const categoryData = req.body;
  
  const category = await categoryService.createCategory(userId, categoryData);
  
  res.status(201).success(category, '分类创建成功');
}));

/**
 * 更新分类
 * PUT /api/categories/:id
 */
router.put('/:id', validators.updateCategory, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  const updateData = req.body;
  
  const category = await categoryService.updateCategory(id, userId, updateData);
  
  res.success(category, '分类更新成功');
}));

/**
 * 删除分类
 * DELETE /api/categories/:id
 */
router.delete('/:id', validators.idParam, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  
  await categoryService.deleteCategory(id, userId);
  
  res.success(null, '分类删除成功');
}));

/**
 * 批量更新分类排序
 * PUT /api/categories/order
 */
router.put('/order', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { categoryOrders } = req.body;
  
  if (!Array.isArray(categoryOrders)) {
    return res.badRequest('categoryOrders 必须是数组');
  }
  
  await categoryService.updateCategoriesOrder(userId, categoryOrders);
  
  res.success(null, '分类排序更新成功');
}));

/**
 * 获取分类统计
 * GET /api/categories/stats
 */
router.get('/stats', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { type } = req.query;
  
  const stats = await categoryService.getCategoryStats(
    userId,
    type as 'income' | 'expense' | undefined
  );
  
  res.success(stats);
}));

export default router;
