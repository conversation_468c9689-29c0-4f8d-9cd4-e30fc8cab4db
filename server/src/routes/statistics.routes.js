"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const error_middleware_1 = require("../middleware/error.middleware");
const statistics_service_1 = __importDefault(require("../services/statistics.service"));
const router = (0, express_1.Router)();
/**
 * 获取概览统计
 * GET /api/statistics/overview
 */
router.get('/overview', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;
    const overview = await statistics_service_1.default.getOverview(userId, startDate, endDate);
    res.success(overview, '获取概览统计成功');
}));
/**
 * 获取当月概览
 * GET /api/statistics/current-month
 */
router.get('/current-month', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const overview = await statistics_service_1.default.getCurrentMonthOverview(userId);
    res.success(overview, '获取当月概览成功');
}));
/**
 * 获取分类统计
 * GET /api/statistics/categories
 */
router.get('/categories', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { type, startDate, endDate } = req.query;
    if (!type || (type !== 'income' && type !== 'expense')) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'type 参数必须是 income 或 expense',
                code: 400
            }
        });
    }
    const statistics = await statistics_service_1.default.getCategoryStatistics(userId, type, startDate, endDate);
    res.success(statistics, '获取分类统计成功');
}));
/**
 * 获取支出分类统计
 * GET /api/statistics/expense-categories
 */
router.get('/expense-categories', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;
    const statistics = await statistics_service_1.default.getExpenseCategoryStats(userId, startDate, endDate);
    res.success(statistics, '获取支出分类统计成功');
}));
/**
 * 获取收入分类统计
 * GET /api/statistics/income-categories
 */
router.get('/income-categories', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;
    const statistics = await statistics_service_1.default.getIncomeCategoryStats(userId, startDate, endDate);
    res.success(statistics, '获取收入分类统计成功');
}));
/**
 * 获取趋势数据
 * GET /api/statistics/trend
 */
router.get('/trend', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { type, period, count } = req.query;
    // 验证参数
    if (!type || !['income', 'expense', 'balance'].includes(type)) {
        return res.error('type 参数必须是 income、expense 或 balance', 400);
    }
    if (!period || !['week', 'month'].includes(period)) {
        return res.error('period 参数必须是 week 或 month', 400);
    }
    const periodCount = count ? parseInt(count) : (period === 'month' ? 12 : 8);
    const trendData = await statistics_service_1.default.getTrendAnalysis(userId, period, periodCount, type);
    res.success(trendData, '获取趋势数据成功');
}));
/**
 * 获取预算分析
 * GET /api/statistics/budget
 */
router.get('/budget', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;
    const budgetAnalysis = await statistics_service_1.default.getBudgetAnalysis(userId, startDate, endDate);
    res.success(budgetAnalysis, '获取预算分析成功');
}));
/**
 * 获取月度对比数据
 * GET /api/statistics/monthly-comparison
 */
router.get('/monthly-comparison', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { months = 6 } = req.query;
    const comparison = await statistics_service_1.default.getMonthlyComparison(userId, Number(months));
    res.success(comparison, '获取月度对比成功');
}));
/**
 * 获取用户统计信息
 * GET /api/statistics/user-stats
 */
router.get('/user-stats', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const userStats = await statistics_service_1.default.getUserStatistics(userId);
    res.success(userStats, '获取用户统计成功');
}));
exports.default = router;
