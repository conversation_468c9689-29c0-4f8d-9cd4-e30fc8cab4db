import { Router } from 'express';
import { asyncHandler } from '../middleware/error.middleware';
import statisticsService from '../services/statistics.service';

const router = Router();

/**
 * 获取概览统计
 * GET /api/statistics/overview
 */
router.get('/overview', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { startDate, endDate } = req.query;
  
  const overview = await statisticsService.getOverview(
    userId,
    startDate as string,
    endDate as string
  );
  
  res.success(overview, '获取概览统计成功');
}));

/**
 * 获取当月概览
 * GET /api/statistics/current-month
 */
router.get('/current-month', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  
  const overview = await statisticsService.getCurrentMonthOverview(userId);
  
  res.success(overview, '获取当月概览成功');
}));

/**
 * 获取分类统计
 * GET /api/statistics/categories
 */
router.get('/categories', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { type, startDate, endDate } = req.query;
  
  if (!type || (type !== 'income' && type !== 'expense')) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'type 参数必须是 income 或 expense',
        code: 400
      }
    });
  }
  
  const statistics = await statisticsService.getCategoryStatistics(
    userId,
    type as 'income' | 'expense',
    startDate as string,
    endDate as string
  );
  
  res.success(statistics, '获取分类统计成功');
}));

/**
 * 获取支出分类统计
 * GET /api/statistics/expense-categories
 */
router.get('/expense-categories', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { startDate, endDate } = req.query;
  
  const statistics = await statisticsService.getExpenseCategoryStats(
    userId,
    startDate as string,
    endDate as string
  );
  
  res.success(statistics, '获取支出分类统计成功');
}));

/**
 * 获取收入分类统计
 * GET /api/statistics/income-categories
 */
router.get('/income-categories', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { startDate, endDate } = req.query;

  const statistics = await statisticsService.getIncomeCategoryStats(
    userId,
    startDate as string,
    endDate as string
  );

  res.success(statistics, '获取收入分类统计成功');
}));

/**
 * 获取趋势数据
 * GET /api/statistics/trend
 */
router.get('/trend', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { type, period, count } = req.query;

  // 验证参数
  if (!type || !['income', 'expense', 'balance'].includes(type as string)) {
    return res.error('type 参数必须是 income、expense 或 balance', 400);
  }

  if (!period || !['week', 'month'].includes(period as string)) {
    return res.error('period 参数必须是 week 或 month', 400);
  }

  const periodCount = count ? parseInt(count as string) : (period === 'month' ? 12 : 8);

  const trendData = await statisticsService.getTrendAnalysis(
    userId,
    period as 'week' | 'month',
    periodCount,
    type as 'income' | 'expense' | 'balance'
  );

  res.success(trendData, '获取趋势数据成功');
}));

/**
 * 获取预算分析
 * GET /api/statistics/budget
 */
router.get('/budget', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { startDate, endDate } = req.query;

  const budgetAnalysis = await statisticsService.getBudgetAnalysis(
    userId,
    startDate as string,
    endDate as string
  );

  res.success(budgetAnalysis, '获取预算分析成功');
}));

/**
 * 获取月度对比数据
 * GET /api/statistics/monthly-comparison
 */
router.get('/monthly-comparison', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { months = 6 } = req.query;
  
  const comparison = await statisticsService.getMonthlyComparison(
    userId,
    Number(months)
  );
  
  res.success(comparison, '获取月度对比成功');
}));

/**
 * 获取用户统计信息
 * GET /api/statistics/user-stats
 */
router.get('/user-stats', asyncHandler(async (req, res) => {
  const userId = req.user!.id;

  const userStats = await statisticsService.getUserStatistics(userId);

  res.success(userStats, '获取用户统计成功');
}));

export default router;
