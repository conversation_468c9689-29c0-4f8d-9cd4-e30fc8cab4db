"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const error_middleware_1 = require("../middleware/error.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const transaction_service_1 = __importDefault(require("../services/transaction.service"));
const router = (0, express_1.Router)();
/**
 * 获取交易记录列表
 * GET /api/transactions
 */
router.get('/', validation_middleware_1.validators.pagination, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const query = req.query;
    // 转换分页参数为数字
    if (query.page)
        query.page = Number(query.page);
    if (query.pageSize)
        query.pageSize = Number(query.pageSize);
    const result = await transaction_service_1.default.getTransactions(userId, query);
    res.success(result);
}));
/**
 * 根据ID获取交易记录
 * GET /api/transactions/:id
 */
router.get('/:id', validation_middleware_1.validators.idParam, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { id } = req.params;
    const transaction = await transaction_service_1.default.getTransactionById(id, userId);
    res.success(transaction);
}));
/**
 * 创建交易记录
 * POST /api/transactions
 */
router.post('/', validation_middleware_1.validators.createTransaction, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const transactionData = req.body;
    const transaction = await transaction_service_1.default.createTransaction(userId, transactionData);
    res.status(201).success(transaction, '交易记录创建成功');
}));
/**
 * 更新交易记录
 * PUT /api/transactions/:id
 */
router.put('/:id', validation_middleware_1.validators.updateTransaction, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { id } = req.params;
    const updateData = req.body;
    const transaction = await transaction_service_1.default.updateTransaction(id, userId, updateData);
    res.success(transaction, '交易记录更新成功');
}));
/**
 * 删除交易记录
 * DELETE /api/transactions/:id
 */
router.delete('/:id', validation_middleware_1.validators.idParam, (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { id } = req.params;
    await transaction_service_1.default.deleteTransaction(id, userId);
    res.success(null, '交易记录删除成功');
}));
/**
 * 批量删除交易记录
 * DELETE /api/transactions
 */
router.delete('/', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { ids } = req.body;
    if (!Array.isArray(ids) || ids.length === 0) {
        return res.error('ids 必须是非空数组', 400);
    }
    await transaction_service_1.default.batchDeleteTransactions(ids, userId);
    res.success(null, '交易记录批量删除成功');
}));
/**
 * 获取最近交易记录
 * GET /api/transactions/recent
 */
router.get('/recent', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { limit = 10 } = req.query;
    const transactions = await transaction_service_1.default.getRecentTransactions(userId, Number(limit));
    res.success(transactions);
}));
exports.default = router;
