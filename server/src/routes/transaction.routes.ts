import { Router } from 'express';
import { asyncHandler } from '../middleware/error.middleware';
import { validators } from '../middleware/validation.middleware';
import transactionService from '../services/transaction.service';

const router = Router();

/**
 * 获取交易记录列表
 * GET /api/transactions
 */
router.get('/', validators.pagination, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const query = req.query;
  
  // 转换分页参数为数字
  if (query.page) query.page = Number(query.page);
  if (query.pageSize) query.pageSize = Number(query.pageSize);
  
  const result = await transactionService.getTransactions(userId, query);
  
  res.success(result);
}));

/**
 * 根据ID获取交易记录
 * GET /api/transactions/:id
 */
router.get('/:id', validators.idParam, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  
  const transaction = await transactionService.getTransactionById(id, userId);
  
  res.success(transaction);
}));

/**
 * 创建交易记录
 * POST /api/transactions
 */
router.post('/', validators.createTransaction, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const transactionData = req.body;
  
  const transaction = await transactionService.createTransaction(userId, transactionData);
  
  res.status(201).success(transaction, '交易记录创建成功');
}));

/**
 * 更新交易记录
 * PUT /api/transactions/:id
 */
router.put('/:id', validators.updateTransaction, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  const updateData = req.body;
  
  const transaction = await transactionService.updateTransaction(id, userId, updateData);
  
  res.success(transaction, '交易记录更新成功');
}));

/**
 * 删除交易记录
 * DELETE /api/transactions/:id
 */
router.delete('/:id', validators.idParam, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  
  await transactionService.deleteTransaction(id, userId);
  
  res.success(null, '交易记录删除成功');
}));

/**
 * 批量删除交易记录
 * DELETE /api/transactions
 */
router.delete('/', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { ids } = req.body;
  
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.error('ids 必须是非空数组', 400);
  }
  
  await transactionService.batchDeleteTransactions(ids, userId);
  
  res.success(null, '交易记录批量删除成功');
}));

/**
 * 获取最近交易记录
 * GET /api/transactions/recent
 */
router.get('/recent', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { limit = 10 } = req.query;
  
  const transactions = await transactionService.getRecentTransactions(
    userId,
    Number(limit)
  );
  
  res.success(transactions);
}));

export default router;
