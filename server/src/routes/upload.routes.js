"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const error_middleware_1 = require("../middleware/error.middleware");
const config_1 = require("../config");
const router = (0, express_1.Router)();
// 确保上传目录存在
if (!fs_1.default.existsSync(config_1.config.upload.uploadDir)) {
    fs_1.default.mkdirSync(config_1.config.upload.uploadDir, { recursive: true });
}
// 配置multer存储
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        // 按日期创建子目录
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const uploadPath = path_1.default.join(config_1.config.upload.uploadDir, `${year}/${month}/${day}`);
        // 确保目录存在
        if (!fs_1.default.existsSync(uploadPath)) {
            fs_1.default.mkdirSync(uploadPath, { recursive: true });
        }
        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        // 生成唯一文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path_1.default.extname(file.originalname);
        cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
    }
});
// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 检查文件类型
    if (!file.mimetype.startsWith('image/')) {
        return cb(new error_middleware_1.AppError('只允许上传图片文件', 400));
    }
    // 检查文件扩展名
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const ext = path_1.default.extname(file.originalname).toLowerCase();
    if (!allowedExtensions.includes(ext)) {
        return cb(new error_middleware_1.AppError('不支持的文件格式', 400));
    }
    cb(null, true);
};
// 配置multer
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: config_1.config.upload.maxFileSize, // 最大文件大小
        files: config_1.config.upload.maxFiles // 最大文件数量
    }
});
/**
 * 上传单个图片
 * POST /api/upload/image
 */
router.post('/image', upload.single('image'), (0, error_middleware_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new error_middleware_1.AppError('没有上传文件', 400);
    }
    // 构建文件URL
    const relativePath = path_1.default.relative(config_1.config.upload.uploadDir, req.file.path);
    const fileUrl = `/uploads/${relativePath.replace(/\\/g, '/')}`;
    res.success({
        filename: req.file.filename,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        url: fileUrl,
        path: relativePath
    }, '图片上传成功');
}));
/**
 * 上传多个图片
 * POST /api/upload/images
 */
router.post('/images', upload.array('images', config_1.config.upload.maxFiles), (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const files = req.files;
    if (!files || files.length === 0) {
        throw new error_middleware_1.AppError('没有上传文件', 400);
    }
    const uploadedFiles = files.map(file => {
        const relativePath = path_1.default.relative(config_1.config.upload.uploadDir, file.path);
        const fileUrl = `/uploads/${relativePath.replace(/\\/g, '/')}`;
        return {
            filename: file.filename,
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            url: fileUrl,
            path: relativePath
        };
    });
    res.success(uploadedFiles, '图片上传成功');
}));
/**
 * 删除文件
 * DELETE /api/upload/:filename
 */
router.delete('/:filename', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { filename } = req.params;
    // 安全检查：防止路径遍历攻击
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new error_middleware_1.AppError('无效的文件名', 400);
    }
    // 查找文件（在所有子目录中搜索）
    const findFile = (dir, targetFilename) => {
        const items = fs_1.default.readdirSync(dir);
        for (const item of items) {
            const fullPath = path_1.default.join(dir, item);
            const stat = fs_1.default.statSync(fullPath);
            if (stat.isDirectory()) {
                const found = findFile(fullPath, targetFilename);
                if (found)
                    return found;
            }
            else if (item === targetFilename) {
                return fullPath;
            }
        }
        return null;
    };
    const filePath = findFile(config_1.config.upload.uploadDir, filename);
    if (!filePath) {
        throw new error_middleware_1.AppError('文件不存在', 404);
    }
    // 删除文件
    fs_1.default.unlinkSync(filePath);
    res.success(null, '文件删除成功');
}));
/**
 * 获取文件信息
 * GET /api/upload/info/:filename
 */
router.get('/info/:filename', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const { filename } = req.params;
    // 安全检查
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new error_middleware_1.AppError('无效的文件名', 400);
    }
    // 查找文件
    const findFile = (dir, targetFilename) => {
        const items = fs_1.default.readdirSync(dir);
        for (const item of items) {
            const fullPath = path_1.default.join(dir, item);
            const stat = fs_1.default.statSync(fullPath);
            if (stat.isDirectory()) {
                const found = findFile(fullPath, targetFilename);
                if (found)
                    return found;
            }
            else if (item === targetFilename) {
                return { path: fullPath, stats: stat };
            }
        }
        return null;
    };
    const fileInfo = findFile(config_1.config.upload.uploadDir, filename);
    if (!fileInfo) {
        throw new error_middleware_1.AppError('文件不存在', 404);
    }
    const relativePath = path_1.default.relative(config_1.config.upload.uploadDir, fileInfo.path);
    const fileUrl = `/uploads/${relativePath.replace(/\\/g, '/')}`;
    res.success({
        filename,
        size: fileInfo.stats.size,
        url: fileUrl,
        path: relativePath,
        createdAt: fileInfo.stats.birthtime,
        modifiedAt: fileInfo.stats.mtime
    });
}));
// 错误处理中间件（专门处理multer错误）
router.use((error, req, res, next) => {
    if (error instanceof multer_1.default.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.error(`文件大小超过限制（最大 ${config_1.config.upload.maxFileSize / 1024 / 1024}MB）`, 400);
        }
        else if (error.code === 'LIMIT_FILE_COUNT') {
            return res.error(`文件数量超过限制（最大 ${config_1.config.upload.maxFiles} 个）`, 400);
        }
        else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.error('意外的文件字段', 400);
        }
    }
    next(error);
});
exports.default = router;
