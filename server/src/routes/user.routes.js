"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const error_middleware_1 = require("../middleware/error.middleware");
const database_1 = require("../database");
const router = (0, express_1.Router)();
/**
 * 获取当前用户信息
 * GET /api/users/profile
 */
router.get('/profile', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const db = await (0, database_1.getDatabase)();
    const user = await db.get(`SELECT 
      id, openid, nickname, avatar_url, gender, 
      city, province, country, created_at, last_login_at
     FROM users 
     WHERE id = ? AND status = 'active'`, [userId]);
    if (!user) {
        return res.status(404).json({
            success: false,
            error: {
                message: '用户不存在',
                code: 404
            }
        });
    }
    res.json({
        success: true,
        data: user
    });
}));
/**
 * 更新用户信息
 * PUT /api/users/profile
 */
router.put('/profile', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { nickname, avatar_url, gender, city, province, country } = req.body;
    const db = await (0, database_1.getDatabase)();
    // 构建更新语句
    const updateFields = [];
    const updateValues = [];
    if (nickname !== undefined) {
        updateFields.push('nickname = ?');
        updateValues.push(nickname);
    }
    if (avatar_url !== undefined) {
        updateFields.push('avatar_url = ?');
        updateValues.push(avatar_url);
    }
    if (gender !== undefined) {
        updateFields.push('gender = ?');
        updateValues.push(gender);
    }
    if (city !== undefined) {
        updateFields.push('city = ?');
        updateValues.push(city);
    }
    if (province !== undefined) {
        updateFields.push('province = ?');
        updateValues.push(province);
    }
    if (country !== undefined) {
        updateFields.push('country = ?');
        updateValues.push(country);
    }
    if (updateFields.length === 0) {
        return res.status(400).json({
            success: false,
            error: {
                message: '没有提供更新数据',
                code: 400
            }
        });
    }
    updateFields.push('updated_at = datetime("now")');
    updateValues.push(userId);
    // 执行更新
    await db.run(`UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`, updateValues);
    // 返回更新后的用户信息
    const updatedUser = await db.get(`SELECT 
      id, openid, nickname, avatar_url, gender, 
      city, province, country, created_at, updated_at
     FROM users 
     WHERE id = ?`, [userId]);
    res.json({
        success: true,
        data: updatedUser
    });
}));
/**
 * 获取用户统计信息
 * GET /api/users/stats
 */
router.get('/stats', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const db = await (0, database_1.getDatabase)();
    // 获取用户统计数据
    const stats = await db.get(`SELECT 
      COUNT(DISTINCT DATE(date)) as total_days,
      COUNT(*) as total_records,
      COUNT(DISTINCT category_id) as total_categories,
      MIN(date) as first_record_date,
      MAX(date) as last_record_date
     FROM transactions 
     WHERE user_id = ? AND deleted_at IS NULL`, [userId]);
    // 获取本月统计
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    const startDate = new Date(year, month, 1).toISOString().split('T')[0];
    const endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];
    const monthlyStats = await db.get(`SELECT 
      COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as monthly_income,
      COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as monthly_expense,
      COUNT(*) as monthly_records
     FROM transactions 
     WHERE user_id = ? AND date >= ? AND date <= ? AND deleted_at IS NULL`, [userId, startDate, endDate]);
    const result = {
        ...stats,
        ...monthlyStats,
        monthly_balance: monthlyStats.monthly_income - monthlyStats.monthly_expense
    };
    res.json({
        success: true,
        data: result
    });
}));
/**
 * 删除用户账户
 * DELETE /api/users/account
 */
router.delete('/account', (0, error_middleware_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { confirm } = req.body;
    if (!confirm) {
        return res.status(400).json({
            success: false,
            error: {
                message: '请确认删除操作',
                code: 400
            }
        });
    }
    const db = await (0, database_1.getDatabase)();
    // 开始事务
    await db.exec('BEGIN TRANSACTION');
    try {
        // 软删除用户的所有交易记录
        await db.run('UPDATE transactions SET deleted_at = datetime("now") WHERE user_id = ?', [userId]);
        // 软删除用户的自定义分类
        await db.run('UPDATE categories SET deleted_at = datetime("now") WHERE user_id = ?', [userId]);
        // 禁用用户账户
        await db.run('UPDATE users SET status = "deleted", updated_at = datetime("now") WHERE id = ?', [userId]);
        await db.exec('COMMIT');
        res.json({
            success: true,
            message: '账户删除成功'
        });
    }
    catch (error) {
        await db.exec('ROLLBACK');
        throw error;
    }
}));
exports.default = router;
