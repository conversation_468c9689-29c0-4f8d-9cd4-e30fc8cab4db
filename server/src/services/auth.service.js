"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const axios_1 = __importDefault(require("axios"));
const database_1 = require("../database");
const config_1 = require("../config");
const auth_middleware_1 = require("../middleware/auth.middleware");
const error_middleware_1 = require("../middleware/error.middleware");
const seed_1 = require("../database/seed");
/**
 * 微信登录服务
 */
class AuthService {
    /**
     * 微信小程序登录
     */
    async login(loginData) {
        try {
            // 调用微信API获取session_key和openid
            const wxResponse = await this.getWxSession(loginData.code);
            if (!wxResponse.openid) {
                throw new error_middleware_1.AppError('微信登录失败，无法获取用户标识', 400);
            }
            // 查找或创建用户
            const user = await this.findOrCreateUser({
                openid: wxResponse.openid,
                session_key: wxResponse.session_key,
                nickname: loginData.userInfo?.nickName || '',
                avatar_url: loginData.userInfo?.avatarUrl || '',
                gender: loginData.userInfo?.gender || 0,
                city: loginData.userInfo?.city || '',
                province: loginData.userInfo?.province || '',
                country: loginData.userInfo?.country || ''
            });
            // 生成JWT令牌
            const token = (0, auth_middleware_1.generateToken)({
                userId: user.id,
                openid: user.openid
            });
            const refreshToken = (0, auth_middleware_1.generateRefreshToken)({
                userId: user.id,
                openid: user.openid
            });
            // 更新用户最后登录时间
            await this.updateLastLogin(user.id);
            return {
                token,
                refreshToken,
                user: {
                    id: user.id,
                    openid: user.openid,
                    nickname: user.nickname || '',
                    avatar_url: user.avatar_url || '',
                    created_at: user.created_at
                }
            };
        }
        catch (error) {
            if (error instanceof error_middleware_1.AppError) {
                throw error;
            }
            console.error('登录失败:', error);
            throw new error_middleware_1.AppError('登录失败，请重试', 500);
        }
    }
    /**
     * 刷新访问令牌
     */
    async refreshToken(refreshToken) {
        try {
            // 验证刷新令牌
            const decoded = (0, auth_middleware_1.verifyRefreshToken)(refreshToken);
            // 检查用户是否存在
            const db = await (0, database_1.getDatabase)();
            const user = await db.get('SELECT id, openid, status FROM users WHERE id = ? AND status = "active"', [decoded.userId]);
            if (!user) {
                throw (0, error_middleware_1.unauthorizedError)('用户不存在或已被禁用');
            }
            // 生成新的令牌
            const newToken = (0, auth_middleware_1.generateToken)({
                userId: user.id,
                openid: user.openid
            });
            const newRefreshToken = (0, auth_middleware_1.generateRefreshToken)({
                userId: user.id,
                openid: user.openid
            });
            return {
                token: newToken,
                refreshToken: newRefreshToken
            };
        }
        catch (error) {
            if (error instanceof error_middleware_1.AppError) {
                throw error;
            }
            throw (0, error_middleware_1.unauthorizedError)('刷新令牌失败');
        }
    }
    /**
     * 获取微信session信息
     */
    async getWxSession(code) {
        try {
            const url = 'https://api.weixin.qq.com/sns/jscode2session';
            const params = {
                appid: config_1.config.wechat.appId,
                secret: config_1.config.wechat.appSecret,
                js_code: code,
                grant_type: 'authorization_code'
            };
            const response = await axios_1.default.get(url, { params });
            if (response.data.errcode) {
                throw new error_middleware_1.AppError(`微信API错误: ${response.data.errmsg}`, 400);
            }
            return response.data;
        }
        catch (error) {
            if (error instanceof error_middleware_1.AppError) {
                throw error;
            }
            console.error('微信API调用失败:', error);
            throw new error_middleware_1.AppError('微信登录服务暂时不可用', 503);
        }
    }
    /**
     * 查找或创建用户
     */
    async findOrCreateUser(userData) {
        const db = await (0, database_1.getDatabase)();
        // 查找现有用户
        let user = await db.get('SELECT * FROM users WHERE openid = ?', [userData.openid]);
        if (user) {
            // 更新用户信息
            await db.run(`UPDATE users SET 
         session_key = ?, nickname = ?, avatar_url = ?, 
         gender = ?, city = ?, province = ?, country = ?,
         updated_at = datetime("now")
         WHERE id = ?`, [
                userData.session_key,
                userData.nickname || user.nickname,
                userData.avatar_url || user.avatar_url,
                userData.gender || user.gender,
                userData.city || user.city,
                userData.province || user.province,
                userData.country || user.country,
                user.id
            ]);
            // 返回更新后的用户信息
            user = await db.get('SELECT * FROM users WHERE id = ?', [user.id]);
        }
        else {
            // 创建新用户
            const userId = this.generateUserId();
            await db.run(`INSERT INTO users (
          id, openid, session_key, nickname, avatar_url,
          gender, city, province, country, status,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, "active", datetime("now"), datetime("now"))`, [
                userId,
                userData.openid,
                userData.session_key,
                userData.nickname,
                userData.avatar_url,
                userData.gender,
                userData.city,
                userData.province,
                userData.country
            ]);
            user = await db.get('SELECT * FROM users WHERE id = ?', [userId]);
            // 为新用户初始化数据（分类等）
            try {
                await (0, seed_1.initUserData)(userId);
            }
            catch (error) {
                console.error('初始化用户数据失败:', error);
                // 不抛出错误，避免影响登录流程
            }
        }
        return user;
    }
    /**
     * 更新最后登录时间
     */
    async updateLastLogin(userId) {
        const db = await (0, database_1.getDatabase)();
        await db.run('UPDATE users SET last_login_at = datetime("now") WHERE id = ?', [userId]);
    }
    /**
     * 生成用户ID
     */
    generateUserId() {
        return 'user_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
    }
    /**
     * 用户登出（可选实现）
     */
    async logout(userId) {
        // 这里可以实现令牌黑名单或其他登出逻辑
        console.log(`用户 ${userId} 已登出`);
    }
    /**
     * 验证用户状态
     */
    async validateUser(userId) {
        const db = await (0, database_1.getDatabase)();
        const user = await db.get('SELECT * FROM users WHERE id = ? AND status = "active"', [userId]);
        return user || null;
    }
}
exports.AuthService = AuthService;
exports.default = new AuthService();
