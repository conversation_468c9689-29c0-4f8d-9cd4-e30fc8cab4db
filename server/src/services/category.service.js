"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoryService = void 0;
const database_1 = require("../database");
const error_middleware_1 = require("../middleware/error.middleware");
/**
 * 分类管理服务
 */
class CategoryService {
    /**
     * 获取用户分类列表
     */
    async getCategories(userId, type, page = 1, pageSize = 50) {
        const db = await (0, database_1.getDatabase)();
        let whereClause = 'WHERE (user_id = ? OR user_id IS NULL) AND deleted_at IS NULL';
        const params = [userId];
        if (type) {
            whereClause += ' AND type = ?';
            params.push(type);
        }
        // 获取总数
        const countResult = await db.get(`SELECT COUNT(*) as total FROM categories ${whereClause}`, params);
        // 获取分页数据
        const offset = (page - 1) * pageSize;
        const categories = await db.all(`SELECT * FROM categories ${whereClause} 
       ORDER BY sort_order ASC, created_at DESC 
       LIMIT ? OFFSET ?`, [...params, pageSize, offset]);
        return {
            list: categories,
            total: countResult.total,
            page,
            pageSize,
            totalPages: Math.ceil(countResult.total / pageSize)
        };
    }
    /**
     * 根据ID获取分类
     */
    async getCategoryById(id, userId) {
        const db = await (0, database_1.getDatabase)();
        const category = await db.get(`SELECT * FROM categories 
       WHERE id = ? AND (user_id = ? OR user_id IS NULL) AND deleted_at IS NULL`, [id, userId]);
        if (!category) {
            throw (0, error_middleware_1.notFoundError)('分类不存在');
        }
        return category;
    }
    /**
     * 创建分类
     */
    async createCategory(userId, categoryData) {
        const db = await (0, database_1.getDatabase)();
        // 检查分类名称是否重复
        const existingCategory = await db.get(`SELECT id FROM categories 
       WHERE name = ? AND type = ? AND user_id = ? AND deleted_at IS NULL`, [categoryData.name, categoryData.type, userId]);
        if (existingCategory) {
            throw (0, error_middleware_1.conflictError)('该分类名称已存在');
        }
        // 生成分类ID
        const categoryId = this.generateCategoryId();
        // 获取排序顺序
        const sortOrder = await this.getNextSortOrder(userId, categoryData.type);
        // 插入分类
        await db.run(`INSERT INTO categories (
        id, user_id, name, type, icon, color, budget,
        sort_order, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime("now"), datetime("now"))`, [
            categoryId,
            userId,
            categoryData.name,
            categoryData.type,
            categoryData.icon || '💰',
            categoryData.color || '#1890ff',
            categoryData.budget || null,
            sortOrder
        ]);
        // 返回创建的分类
        return await this.getCategoryById(categoryId, userId);
    }
    /**
     * 更新分类
     */
    async updateCategory(id, userId, updateData) {
        const db = await (0, database_1.getDatabase)();
        // 检查分类是否存在且属于用户
        const existingCategory = await db.get(`SELECT * FROM categories 
       WHERE id = ? AND user_id = ? AND deleted_at IS NULL`, [id, userId]);
        if (!existingCategory) {
            throw (0, error_middleware_1.notFoundError)('分类不存在或无权限修改');
        }
        // 如果更新名称，检查是否重复
        if (updateData.name && updateData.name !== existingCategory.name) {
            const duplicateCategory = await db.get(`SELECT id FROM categories 
         WHERE name = ? AND type = ? AND user_id = ? AND id != ? AND deleted_at IS NULL`, [updateData.name, updateData.type || existingCategory.type, userId, id]);
            if (duplicateCategory) {
                throw (0, error_middleware_1.conflictError)('该分类名称已存在');
            }
        }
        // 构建更新语句
        const updateFields = [];
        const updateValues = [];
        if (updateData.name !== undefined) {
            updateFields.push('name = ?');
            updateValues.push(updateData.name);
        }
        if (updateData.type !== undefined) {
            updateFields.push('type = ?');
            updateValues.push(updateData.type);
        }
        if (updateData.icon !== undefined) {
            updateFields.push('icon = ?');
            updateValues.push(updateData.icon);
        }
        if (updateData.color !== undefined) {
            updateFields.push('color = ?');
            updateValues.push(updateData.color);
        }
        if (updateData.budget !== undefined) {
            updateFields.push('budget = ?');
            updateValues.push(updateData.budget);
        }
        if (updateData.sort_order !== undefined) {
            updateFields.push('sort_order = ?');
            updateValues.push(updateData.sort_order);
        }
        if (updateFields.length === 0) {
            return existingCategory; // 没有更新内容
        }
        updateFields.push('updated_at = datetime("now")');
        updateValues.push(id);
        // 执行更新
        await db.run(`UPDATE categories SET ${updateFields.join(', ')} WHERE id = ?`, updateValues);
        // 返回更新后的分类
        return await this.getCategoryById(id, userId);
    }
    /**
     * 删除分类（软删除）
     */
    async deleteCategory(id, userId) {
        const db = await (0, database_1.getDatabase)();
        // 检查分类是否存在且属于用户
        const category = await db.get(`SELECT * FROM categories 
       WHERE id = ? AND user_id = ? AND deleted_at IS NULL`, [id, userId]);
        if (!category) {
            throw (0, error_middleware_1.notFoundError)('分类不存在或无权限删除');
        }
        // 检查是否有关联的交易记录
        const transactionCount = await db.get('SELECT COUNT(*) as count FROM transactions WHERE category_id = ? AND deleted_at IS NULL', [id]);
        if (transactionCount.count > 0) {
            throw (0, error_middleware_1.conflictError)('该分类下还有交易记录，无法删除');
        }
        // 软删除分类
        await db.run('UPDATE categories SET deleted_at = datetime("now") WHERE id = ?', [id]);
    }
    /**
     * 批量更新分类排序
     */
    async updateCategoriesOrder(userId, categoryOrders) {
        const db = await (0, database_1.getDatabase)();
        // 开始事务
        await db.exec('BEGIN TRANSACTION');
        try {
            for (const item of categoryOrders) {
                // 验证分类属于用户
                const category = await db.get('SELECT id FROM categories WHERE id = ? AND user_id = ? AND deleted_at IS NULL', [item.id, userId]);
                if (!category) {
                    throw (0, error_middleware_1.notFoundError)(`分类 ${item.id} 不存在或无权限修改`);
                }
                // 更新排序
                await db.run('UPDATE categories SET sort_order = ?, updated_at = datetime("now") WHERE id = ?', [item.sort_order, item.id]);
            }
            await db.exec('COMMIT');
        }
        catch (error) {
            await db.exec('ROLLBACK');
            throw error;
        }
    }
    /**
     * 获取分类统计信息
     */
    async getCategoryStats(userId, type) {
        const db = await (0, database_1.getDatabase)();
        let whereClause = 'WHERE (c.user_id = ? OR c.user_id IS NULL) AND c.deleted_at IS NULL';
        const params = [userId];
        if (type) {
            whereClause += ' AND c.type = ?';
            params.push(type);
        }
        const stats = await db.all(`SELECT
        c.id,
        c.name,
        c.icon,
        c.color,
        c.type,
        COUNT(t.id) as transaction_count,
        COALESCE(SUM(t.amount), 0) as total_amount,
        COALESCE(AVG(t.amount), 0) as avg_amount
       FROM categories c
       LEFT JOIN transactions t ON c.id = t.category_id AND t.user_id = ? AND t.deleted_at IS NULL
       ${whereClause}
       GROUP BY c.id, c.name, c.icon, c.color, c.type
       ORDER BY total_amount DESC`, [userId, ...params]);
        return stats;
    }
    /**
     * 获取下一个排序顺序
     */
    async getNextSortOrder(userId, type) {
        const db = await (0, database_1.getDatabase)();
        const result = await db.get(`SELECT MAX(sort_order) as max_order 
       FROM categories 
       WHERE user_id = ? AND type = ? AND deleted_at IS NULL`, [userId, type]);
        return (result.max_order || 0) + 1;
    }
    /**
     * 生成分类ID
     */
    generateCategoryId() {
        return 'cat_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}
exports.CategoryService = CategoryService;
exports.default = new CategoryService();
