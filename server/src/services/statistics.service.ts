import { getDatabase } from '../database';
import { StatisticsOverview, CategoryStatistics, TrendData, BudgetAnalysis } from '../types';

/**
 * 统计分析服务
 */
export class StatisticsService {
  /**
   * 获取概览统计
   */
  async getOverview(
    userId: string,
    startDate?: string,
    endDate?: string
  ): Promise<StatisticsOverview> {
    const db = await getDatabase();
    
    // 如果没有指定日期范围，使用当月
    if (!startDate || !endDate) {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();
      startDate = new Date(year, month, 1).toISOString().split('T')[0];
      endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];
    }
    
    // 获取收入统计
    const incomeResult = await db.get(
      `SELECT 
        COALESCE(SUM(amount), 0) as total_income,
        COUNT(*) as income_count
       FROM transactions 
       WHERE user_id = ? AND type = 'income' AND date >= ? AND date <= ? AND deleted_at IS NULL`,
      [userId, startDate, endDate]
    );
    
    // 获取支出统计
    const expenseResult = await db.get(
      `SELECT 
        COALESCE(SUM(amount), 0) as total_expense,
        COUNT(*) as expense_count
       FROM transactions 
       WHERE user_id = ? AND type = 'expense' AND date >= ? AND date <= ? AND deleted_at IS NULL`,
      [userId, startDate, endDate]
    );
    
    // 计算结余
    const balance = incomeResult.total_income - expenseResult.total_expense;
    
    // 计算日均支出
    const daysDiff = this.getDaysBetween(startDate, endDate);
    const avgDailyExpense = daysDiff > 0 ? expenseResult.total_expense / daysDiff : 0;
    
    return {
      totalIncome: incomeResult.total_income,
      totalExpense: expenseResult.total_expense,
      balance,
      transactionCount: incomeResult.income_count + expenseResult.expense_count,
      incomeCount: incomeResult.income_count,
      expenseCount: expenseResult.expense_count,
      avgDailyExpense,
      period: {
        startDate,
        endDate
      }
    };
  }

  /**
   * 获取当月概览
   */
  async getCurrentMonthOverview(userId: string): Promise<StatisticsOverview> {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    const startDate = new Date(year, month, 1).toISOString().split('T')[0];
    const endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];
    
    return this.getOverview(userId, startDate, endDate);
  }

  /**
   * 获取分类统计
   */
  async getCategoryStatistics(
    userId: string,
    type: 'income' | 'expense',
    startDate?: string,
    endDate?: string
  ): Promise<CategoryStatistics[]> {
    const db = await getDatabase();
    
    // 如果没有指定日期范围，使用当月
    if (!startDate || !endDate) {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();
      startDate = new Date(year, month, 1).toISOString().split('T')[0];
      endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];
    }
    
    const statistics = await db.all(
      `SELECT 
        c.id as category_id,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color,
        COALESCE(SUM(t.amount), 0) as amount,
        COUNT(t.id) as transaction_count
       FROM categories c
       LEFT JOIN transactions t ON c.id = t.category_id 
         AND t.user_id = ? 
         AND t.type = ? 
         AND t.date >= ? 
         AND t.date <= ? 
         AND t.deleted_at IS NULL
       WHERE (c.user_id = ? OR c.user_id IS NULL) 
         AND c.type = ? 
         AND c.deleted_at IS NULL
       GROUP BY c.id, c.name, c.icon, c.color
       HAVING amount > 0
       ORDER BY amount DESC`,
      [userId, type, startDate, endDate, userId, type]
    );
    
    // 计算总金额用于计算百分比
    const totalAmount = statistics.reduce((sum, item) => sum + item.amount, 0);
    
    // 添加百分比
    return statistics.map(item => ({
      ...item,
      percentage: totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0
    }));
  }

  /**
   * 获取支出分类统计
   */
  async getExpenseCategoryStats(
    userId: string,
    startDate?: string,
    endDate?: string
  ): Promise<CategoryStatistics[]> {
    return this.getCategoryStatistics(userId, 'expense', startDate, endDate);
  }

  /**
   * 获取收入分类统计
   */
  async getIncomeCategoryStats(
    userId: string,
    startDate?: string,
    endDate?: string
  ): Promise<CategoryStatistics[]> {
    return this.getCategoryStatistics(userId, 'income', startDate, endDate);
  }

  /**
   * 获取趋势数据
   */
  async getTrendData(
    userId: string,
    type: 'income' | 'expense' | 'balance',
    period: 'week' | 'month' | 'year',
    startDate?: string,
    endDate?: string
  ): Promise<TrendData[]> {
    const db = await getDatabase();
    
    let dateFormat: string;
    let groupBy: string;
    
    switch (period) {
      case 'week':
        dateFormat = '%Y-%W'; // 年-周
        groupBy = 'strftime("%Y-%W", date)';
        break;
      case 'month':
        dateFormat = '%Y-%m'; // 年-月
        groupBy = 'strftime("%Y-%m", date)';
        break;
      case 'year':
        dateFormat = '%Y'; // 年
        groupBy = 'strftime("%Y", date)';
        break;
    }
    
    let whereClause = 'WHERE user_id = ? AND deleted_at IS NULL';
    const params: any[] = [userId];
    
    if (startDate) {
      whereClause += ' AND date >= ?';
      params.push(startDate);
    }
    
    if (endDate) {
      whereClause += ' AND date <= ?';
      params.push(endDate);
    }
    
    if (type === 'balance') {
      // 获取收支数据计算结余
      const trendData = await db.all(
        `SELECT 
          ${groupBy} as period,
          COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as income,
          COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as expense
         FROM transactions 
         ${whereClause}
         GROUP BY ${groupBy}
         ORDER BY period`,
        params
      );
      
      return trendData.map(item => ({
        period: item.period,
        value: item.income - item.expense
      }));
    } else {
      // 获取单一类型数据
      whereClause += ' AND type = ?';
      params.push(type);
      
      const trendData = await db.all(
        `SELECT 
          ${groupBy} as period,
          COALESCE(SUM(amount), 0) as value
         FROM transactions 
         ${whereClause}
         GROUP BY ${groupBy}
         ORDER BY period`,
        params
      );
      
      return trendData;
    }
  }

  /**
   * 获取预算分析
   */
  async getBudgetAnalysis(
    userId: string,
    startDate?: string,
    endDate?: string
  ): Promise<BudgetAnalysis[]> {
    const db = await getDatabase();
    
    // 如果没有指定日期范围，使用当月
    if (!startDate || !endDate) {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();
      startDate = new Date(year, month, 1).toISOString().split('T')[0];
      endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];
    }
    
    const budgetAnalysis = await db.all(
      `SELECT 
        c.id as category_id,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color,
        c.budget,
        COALESCE(SUM(t.amount), 0) as spent
       FROM categories c
       LEFT JOIN transactions t ON c.id = t.category_id 
         AND t.user_id = ? 
         AND t.type = 'expense' 
         AND t.date >= ? 
         AND t.date <= ? 
         AND t.deleted_at IS NULL
       WHERE (c.user_id = ? OR c.user_id IS NULL) 
         AND c.type = 'expense' 
         AND c.budget IS NOT NULL 
         AND c.budget > 0
         AND c.deleted_at IS NULL
       GROUP BY c.id, c.name, c.icon, c.color, c.budget
       ORDER BY (spent / c.budget) DESC`,
      [userId, startDate, endDate, userId]
    );
    
    return budgetAnalysis.map(item => ({
      ...item,
      usage_percentage: item.budget > 0 ? (item.spent / item.budget) * 100 : 0,
      remaining: Math.max(0, item.budget - item.spent),
      is_over_budget: item.spent > item.budget
    }));
  }

  /**
   * 获取月度对比数据
   */
  async getMonthlyComparison(userId: string, months: number = 6): Promise<any[]> {
    const db = await getDatabase();
    
    const comparison = await db.all(
      `SELECT 
        strftime('%Y-%m', date) as month,
        COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as income,
        COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as expense,
        COUNT(*) as transaction_count
       FROM transactions 
       WHERE user_id = ? 
         AND date >= date('now', '-${months} months')
         AND deleted_at IS NULL
       GROUP BY strftime('%Y-%m', date)
       ORDER BY month DESC
       LIMIT ?`,
      [userId, months]
    );
    
    return comparison.map(item => ({
      ...item,
      balance: item.income - item.expense
    }));
  }

  /**
   * 获取趋势分析数据
   */
  async getTrendAnalysis(
    userId: string,
    period: 'week' | 'month',
    count: number,
    type: 'income' | 'expense' | 'balance'
  ): Promise<{
    data: Array<{
      period: string;
      income: number;
      expense: number;
      balance: number;
    }>;
    summary: {
      avg: number;
      max: number;
      min: number;
      growth: number;
    };
  }> {
    const db = await getDatabase();

    // 根据period类型生成日期格式
    const dateFormat = period === 'month' ? '%Y-%m' : '%Y-%W';
    const dateGroup = period === 'month' ?
      "strftime('%Y-%m', date)" :
      "strftime('%Y-%W', date)";

    // 计算开始日期
    let startDate: string;
    if (period === 'month') {
      startDate = new Date(Date.now() - count * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    } else {
      startDate = new Date(Date.now() - count * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    }

    // 获取趋势数据
    const trendQuery = `
      SELECT
        ${dateGroup} as period,
        SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as income,
        SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expense
      FROM transactions
      WHERE user_id = ? AND deleted_at IS NULL
        AND date >= ?
      GROUP BY ${dateGroup}
      ORDER BY period ASC
    `;

    const trendResults = await db.all(trendQuery, [userId, startDate]);

    // 处理数据
    const data = trendResults.map(row => ({
      period: row.period,
      income: row.income || 0,
      expense: row.expense || 0,
      balance: (row.income || 0) - (row.expense || 0)
    }));

    // 计算摘要统计
    let values: number[] = [];
    switch (type) {
      case 'income':
        values = data.map(d => d.income);
        break;
      case 'expense':
        values = data.map(d => d.expense);
        break;
      case 'balance':
        values = data.map(d => d.balance);
        break;
    }

    const avg = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
    const max = values.length > 0 ? Math.max(...values) : 0;
    const min = values.length > 0 ? Math.min(...values) : 0;

    // 计算增长率（最新值相比第一个值的增长率）
    let growth = 0;
    if (values.length >= 2 && values[0] !== 0) {
      growth = ((values[values.length - 1] - values[0]) / Math.abs(values[0])) * 100;
    }

    return {
      data,
      summary: {
        avg,
        max,
        min,
        growth
      }
    };
  }

  /**
   * 获取用户统计信息
   */
  async getUserStatistics(userId: string): Promise<{
    totalDays: number;
    totalRecords: number;
    totalCategories: number;
    firstRecordDate?: string;
  }> {
    const db = await getDatabase();

    // 获取总记录数
    const recordResult = await db.get(
      'SELECT COUNT(*) as total FROM transactions WHERE user_id = ? AND deleted_at IS NULL',
      [userId]
    );

    // 获取使用的分类数
    const categoryResult = await db.get(
      `SELECT COUNT(DISTINCT category_id) as total
       FROM transactions
       WHERE user_id = ? AND deleted_at IS NULL`,
      [userId]
    );

    // 获取第一条记录的日期
    const firstRecordResult = await db.get(
      'SELECT MIN(date) as first_date FROM transactions WHERE user_id = ? AND deleted_at IS NULL',
      [userId]
    );

    // 计算记账天数
    let totalDays = 0;
    if (firstRecordResult.first_date) {
      const firstDate = new Date(firstRecordResult.first_date);
      const today = new Date();
      totalDays = Math.ceil((today.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    }

    return {
      totalDays,
      totalRecords: recordResult.total,
      totalCategories: categoryResult.total,
      firstRecordDate: firstRecordResult.first_date
    };
  }

  /**
   * 计算两个日期之间的天数
   */
  private getDaysBetween(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  }
}

export default new StatisticsService();
