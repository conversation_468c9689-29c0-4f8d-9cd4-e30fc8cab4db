"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionService = void 0;
const database_1 = require("../database");
const error_middleware_1 = require("../middleware/error.middleware");
/**
 * 交易记录服务
 */
class TransactionService {
    /**
     * 获取交易记录列表
     */
    async getTransactions(userId, query = {}) {
        const db = await (0, database_1.getDatabase)();
        const { page = 1, pageSize = 20, type, category_id, categoryId, start_date, end_date, startDate, endDate, keyword, sort_by = 'date', sort_order = 'desc' } = query;
        // 兼容前端参数名
        const finalCategoryId = category_id || categoryId;
        const finalStartDate = start_date || startDate;
        const finalEndDate = end_date || endDate;
        // 构建WHERE条件
        let whereClause = 'WHERE t.user_id = ? AND t.deleted_at IS NULL';
        const params = [userId];
        if (type) {
            whereClause += ' AND t.type = ?';
            params.push(type);
        }
        if (finalCategoryId) {
            whereClause += ' AND t.category_id = ?';
            params.push(finalCategoryId);
        }
        if (finalStartDate) {
            whereClause += ' AND t.date >= ?';
            params.push(finalStartDate);
        }
        if (finalEndDate) {
            whereClause += ' AND t.date <= ?';
            params.push(finalEndDate);
        }
        if (keyword) {
            whereClause += ' AND (t.description LIKE ? OR c.name LIKE ?)';
            params.push(`%${keyword}%`, `%${keyword}%`);
        }
        // 构建ORDER BY子句
        const validSortFields = ['date', 'amount', 'created_at'];
        const validSortOrders = ['asc', 'desc'];
        const sortField = validSortFields.includes(sort_by) ? sort_by : 'date';
        const sortDirection = validSortOrders.includes(sort_order) ? sort_order : 'desc';
        const orderClause = `ORDER BY t.${sortField} ${sortDirection.toUpperCase()}, t.created_at DESC`;
        // 获取总数
        const countResult = await db.get(`SELECT COUNT(*) as total 
       FROM transactions t
       LEFT JOIN categories c ON t.category_id = c.id
       ${whereClause}`, params);
        // 获取分页数据
        const offset = (page - 1) * pageSize;
        const transactions = await db.all(`SELECT 
        t.*,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color
       FROM transactions t
       LEFT JOIN categories c ON t.category_id = c.id
       ${whereClause}
       ${orderClause}
       LIMIT ? OFFSET ?`, [...params, pageSize, offset]);
        return {
            list: transactions,
            total: countResult.total,
            page,
            pageSize,
            totalPages: Math.ceil(countResult.total / pageSize)
        };
    }
    /**
     * 根据ID获取交易记录
     */
    async getTransactionById(id, userId) {
        const db = await (0, database_1.getDatabase)();
        const transaction = await db.get(`SELECT 
        t.*,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color
       FROM transactions t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.id = ? AND t.user_id = ? AND t.deleted_at IS NULL`, [id, userId]);
        if (!transaction) {
            throw (0, error_middleware_1.notFoundError)('交易记录不存在');
        }
        return transaction;
    }
    /**
     * 创建交易记录
     */
    async createTransaction(userId, transactionData) {
        const db = await (0, database_1.getDatabase)();
        // 兼容前端参数名
        const categoryId = transactionData.categoryId || transactionData.category_id;
        console.log('创建交易记录 - 交易类型:', transactionData.type, '分类ID:', categoryId);
        // 验证分类是否存在
        const category = await db.get('SELECT id, type FROM categories WHERE id = ? AND (user_id = ? OR user_id IS NULL) AND deleted_at IS NULL', [categoryId, userId]);
        console.log('查询到的分类:', category);
        if (!category) {
            throw (0, error_middleware_1.validationError)('分类不存在');
        }
        // 验证交易类型与分类类型是否匹配
        if (transactionData.type !== category.type) {
            console.error('类型不匹配 - 交易类型:', transactionData.type, '分类类型:', category.type);
            throw (0, error_middleware_1.validationError)('交易类型与分类类型不匹配');
        }
        // 生成交易ID
        const transactionId = this.generateTransactionId();
        // 插入交易记录
        await db.run(`INSERT INTO transactions (
        id, user_id, type, amount, category_id, description,
        date, image_url, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime("now"), datetime("now"))`, [
            transactionId,
            userId,
            transactionData.type,
            transactionData.amount,
            categoryId,
            transactionData.description || null,
            transactionData.date,
            transactionData.imageUrl || transactionData.image_url || null
        ]);
        // 返回创建的交易记录
        return await this.getTransactionById(transactionId, userId);
    }
    /**
     * 更新交易记录
     */
    async updateTransaction(id, userId, updateData) {
        const db = await (0, database_1.getDatabase)();
        // 检查交易记录是否存在且属于用户
        const existingTransaction = await db.get('SELECT * FROM transactions WHERE id = ? AND user_id = ? AND deleted_at IS NULL', [id, userId]);
        if (!existingTransaction) {
            throw (0, error_middleware_1.notFoundError)('交易记录不存在或无权限修改');
        }
        // 如果更新分类，验证分类是否存在
        if (updateData.category_id) {
            const category = await db.get('SELECT id, type FROM categories WHERE id = ? AND (user_id = ? OR user_id IS NULL) AND deleted_at IS NULL', [updateData.category_id, userId]);
            if (!category) {
                throw (0, error_middleware_1.validationError)('分类不存在');
            }
            // 验证交易类型与分类类型是否匹配
            const transactionType = updateData.type || existingTransaction.type;
            if (transactionType !== category.type) {
                throw (0, error_middleware_1.validationError)('交易类型与分类类型不匹配');
            }
        }
        // 构建更新语句
        const updateFields = [];
        const updateValues = [];
        if (updateData.type !== undefined) {
            updateFields.push('type = ?');
            updateValues.push(updateData.type);
        }
        if (updateData.amount !== undefined) {
            updateFields.push('amount = ?');
            updateValues.push(updateData.amount);
        }
        if (updateData.category_id !== undefined) {
            updateFields.push('category_id = ?');
            updateValues.push(updateData.category_id);
        }
        if (updateData.description !== undefined) {
            updateFields.push('description = ?');
            updateValues.push(updateData.description);
        }
        if (updateData.date !== undefined) {
            updateFields.push('date = ?');
            updateValues.push(updateData.date);
        }
        if (updateData.image_url !== undefined) {
            updateFields.push('image_url = ?');
            updateValues.push(updateData.image_url);
        }
        if (updateFields.length === 0) {
            return existingTransaction; // 没有更新内容
        }
        updateFields.push('updated_at = datetime("now")');
        updateValues.push(id);
        // 执行更新
        await db.run(`UPDATE transactions SET ${updateFields.join(', ')} WHERE id = ?`, updateValues);
        // 返回更新后的交易记录
        return await this.getTransactionById(id, userId);
    }
    /**
     * 删除交易记录（软删除）
     */
    async deleteTransaction(id, userId) {
        const db = await (0, database_1.getDatabase)();
        // 检查交易记录是否存在且属于用户
        const transaction = await db.get('SELECT id FROM transactions WHERE id = ? AND user_id = ? AND deleted_at IS NULL', [id, userId]);
        if (!transaction) {
            throw (0, error_middleware_1.notFoundError)('交易记录不存在或无权限删除');
        }
        // 软删除交易记录
        await db.run('UPDATE transactions SET deleted_at = datetime("now") WHERE id = ?', [id]);
    }
    /**
     * 批量删除交易记录
     */
    async batchDeleteTransactions(ids, userId) {
        const db = await (0, database_1.getDatabase)();
        if (ids.length === 0) {
            return;
        }
        // 验证所有交易记录都属于用户
        const placeholders = ids.map(() => '?').join(',');
        const transactions = await db.all(`SELECT id FROM transactions 
       WHERE id IN (${placeholders}) AND user_id = ? AND deleted_at IS NULL`, [...ids, userId]);
        if (transactions.length !== ids.length) {
            throw (0, error_middleware_1.validationError)('部分交易记录不存在或无权限删除');
        }
        // 批量软删除
        await db.run(`UPDATE transactions SET deleted_at = datetime("now") 
       WHERE id IN (${placeholders}) AND user_id = ?`, [...ids, userId]);
    }
    /**
     * 获取最近交易记录
     */
    async getRecentTransactions(userId, limit = 10) {
        const db = await (0, database_1.getDatabase)();
        const transactions = await db.all(`SELECT 
        t.*,
        c.name as category_name,
        c.icon as category_icon,
        c.color as category_color
       FROM transactions t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.user_id = ? AND t.deleted_at IS NULL
       ORDER BY t.created_at DESC
       LIMIT ?`, [userId, limit]);
        return transactions;
    }
    /**
     * 生成交易ID
     */
    generateTransactionId() {
        return 'txn_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}
exports.TransactionService = TransactionService;
exports.default = new TransactionService();
