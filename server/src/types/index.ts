// 基础类型定义

/**
 * 用户相关类型
 */
export interface User {
  id: string;
  openid: string;
  session_key?: string;
  nickname?: string;
  avatar_url?: string;
  gender?: number;
  city?: string;
  province?: string;
  country?: string;
  role?: string;
  status: string;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateUserRequest {
  openid: string;
  session_key?: string;
  nickname?: string;
  avatar_url?: string;
  gender?: number;
  city?: string;
  province?: string;
  country?: string;
}

export interface LoginRequest {
  code: string;
  userInfo?: {
    nickName?: string;
    avatarUrl?: string;
    gender?: number;
    city?: string;
    province?: string;
    country?: string;
  };
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: {
    id: string;
    openid: string;
    nickname?: string;
    avatar_url?: string;
    created_at: string;
  };
}

/**
 * 分类相关类型
 */
export interface Category {
  id: string;
  user_id?: string;
  name: string;
  type: 'income' | 'expense';
  icon: string;
  color: string;
  budget?: number;
  sort_order: number;
  deleted_at?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCategoryRequest {
  name: string;
  type: 'income' | 'expense';
  icon?: string;
  color?: string;
  budget?: number;
}

export interface UpdateCategoryRequest {
  name?: string;
  type?: 'income' | 'expense';
  icon?: string;
  color?: string;
  budget?: number;
  sort_order?: number;
}

/**
 * 交易记录相关类型
 */
export interface Transaction {
  id: string;
  user_id: string;
  type: 'income' | 'expense';
  amount: number;
  category_id: string;
  description?: string;
  date: string;
  image_url?: string;
  deleted_at?: string;
  created_at: string;
  updated_at: string;
  // 关联数据
  category_name?: string;
  category_icon?: string;
  category_color?: string;
}

export interface CreateTransactionRequest {
  type: 'income' | 'expense';
  amount: number;
  categoryId?: string;  // 前端使用的参数名
  category_id?: string; // 后端兼容的参数名
  description?: string;
  date: string;
  imageUrl?: string;    // 前端使用的参数名
  image_url?: string;   // 后端兼容的参数名
}

export interface UpdateTransactionRequest {
  type?: 'income' | 'expense';
  amount?: number;
  categoryId?: string;  // 前端使用的参数名
  category_id?: string; // 后端兼容的参数名
  description?: string;
  date?: string;
  imageUrl?: string;    // 前端使用的参数名
  image_url?: string;   // 后端兼容的参数名
}

export interface TransactionQuery {
  page?: number;
  pageSize?: number;
  type?: 'income' | 'expense';
  category_id?: string;
  start_date?: string;
  end_date?: string;
  keyword?: string;
  sort_by?: 'date' | 'amount' | 'created_at';
  sort_order?: 'asc' | 'desc';
  minAmount?: number;
  maxAmount?: number;
}

/**
 * 统计相关类型
 */
export interface StatisticsOverview {
  totalIncome: number;
  totalExpense: number;
  balance: number;
  transactionCount?: number;
  incomeCount?: number;
  expenseCount?: number;
  avgDailyExpense?: number;
  period?: {
    startDate: string;
    endDate: string;
  };
}

export interface CategoryStatistics {
  category_id: string;
  category_name: string;
  category_icon: string;
  category_color: string;
  amount: number;
  transaction_count: number;
  percentage: number;
}

export interface TrendData {
  period: string;
  value: number;
}

export interface BudgetAnalysis {
  category_id: string;
  category_name: string;
  category_icon: string;
  category_color: string;
  budget: number;
  spent: number;
  usage_percentage: number;
  remaining: number;
  is_over_budget: boolean;
}

/**
 * 通用类型
 */
export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: number;
    details?: any;
  };
  message?: string;
}
