"use strict";
/**
 * 统一API响应格式工具
 * 确保前后端响应格式一致
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseMiddleware = exports.ResponseUtil = void 0;
/**
 * API响应工具类
 */
class ResponseUtil {
    /**
     * 成功响应
     */
    static success(data, message = 'success') {
        return {
            code: 200,
            message,
            data
        };
    }
    /**
     * 分页成功响应
     */
    static successWithPagination(list, total, page, pageSize, message = 'success') {
        return {
            code: 200,
            message,
            data: {
                list,
                total,
                page,
                pageSize,
                totalPages: Math.ceil(total / pageSize)
            }
        };
    }
    /**
     * 错误响应
     */
    static error(code, message) {
        return {
            code,
            message,
            data: null
        };
    }
    /**
     * 400 - 请求参数错误
     */
    static badRequest(message = '请求参数错误') {
        return this.error(400, message);
    }
    /**
     * 401 - 未授权
     */
    static unauthorized(message = '未授权访问') {
        return this.error(401, message);
    }
    /**
     * 403 - 禁止访问
     */
    static forbidden(message = '禁止访问') {
        return this.error(403, message);
    }
    /**
     * 404 - 资源不存在
     */
    static notFound(message = '资源不存在') {
        return this.error(404, message);
    }
    /**
     * 409 - 资源冲突
     */
    static conflict(message = '资源冲突') {
        return this.error(409, message);
    }
    /**
     * 422 - 数据验证失败
     */
    static validationError(message = '数据验证失败') {
        return this.error(422, message);
    }
    /**
     * 500 - 服务器内部错误
     */
    static internalError(message = '服务器内部错误') {
        return this.error(500, message);
    }
    /**
     * 502 - 网关错误
     */
    static badGateway(message = '网关错误') {
        return this.error(502, message);
    }
    /**
     * 503 - 服务不可用
     */
    static serviceUnavailable(message = '服务暂时不可用') {
        return this.error(503, message);
    }
}
exports.ResponseUtil = ResponseUtil;
/**
 * 响应中间件
 * 为Express Response对象添加统一响应方法
 */
const responseMiddleware = (req, res, next) => {
    // 成功响应
    res.success = function (data, message = 'success') {
        return this.json(ResponseUtil.success(data, message));
    };
    // 分页成功响应
    res.successWithPagination = function (list, total, page, pageSize, message = 'success') {
        return this.json(ResponseUtil.successWithPagination(list, total, page, pageSize, message));
    };
    // 错误响应
    res.error = function (code, message) {
        return this.status(code).json(ResponseUtil.error(code, message));
    };
    // 400 - 请求参数错误
    res.badRequest = function (message) {
        const response = ResponseUtil.badRequest(message);
        return this.status(400).json(response);
    };
    // 401 - 未授权
    res.unauthorized = function (message) {
        const response = ResponseUtil.unauthorized(message);
        return this.status(401).json(response);
    };
    // 403 - 禁止访问
    res.forbidden = function (message) {
        const response = ResponseUtil.forbidden(message);
        return this.status(403).json(response);
    };
    // 404 - 资源不存在
    res.notFound = function (message) {
        const response = ResponseUtil.notFound(message);
        return this.status(404).json(response);
    };
    // 409 - 资源冲突
    res.conflict = function (message) {
        const response = ResponseUtil.conflict(message);
        return this.status(409).json(response);
    };
    // 422 - 数据验证失败
    res.validationError = function (message) {
        const response = ResponseUtil.validationError(message);
        return this.status(422).json(response);
    };
    // 500 - 服务器内部错误
    res.internalError = function (message) {
        const response = ResponseUtil.internalError(message);
        return this.status(500).json(response);
    };
    next();
};
exports.responseMiddleware = responseMiddleware;
exports.default = ResponseUtil;
