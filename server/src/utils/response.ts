/**
 * 统一API响应格式工具
 * 确保前后端响应格式一致
 */

// 统一响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T | null;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * API响应工具类
 */
export class ResponseUtil {
  /**
   * 成功响应
   */
  static success<T>(data: T, message: string = 'success'): ApiResponse<T> {
    return {
      code: 200,
      message,
      data
    };
  }

  /**
   * 分页成功响应
   */
  static successWithPagination<T>(
    list: T[],
    total: number,
    page: number,
    pageSize: number,
    message: string = 'success'
  ): ApiResponse<PaginatedResponse<T>> {
    return {
      code: 200,
      message,
      data: {
        list,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  /**
   * 错误响应
   */
  static error(code: number, message: string): ApiResponse<null> {
    return {
      code,
      message,
      data: null
    };
  }

  /**
   * 400 - 请求参数错误
   */
  static badRequest(message: string = '请求参数错误'): ApiResponse<null> {
    return this.error(400, message);
  }

  /**
   * 401 - 未授权
   */
  static unauthorized(message: string = '未授权访问'): ApiResponse<null> {
    return this.error(401, message);
  }

  /**
   * 403 - 禁止访问
   */
  static forbidden(message: string = '禁止访问'): ApiResponse<null> {
    return this.error(403, message);
  }

  /**
   * 404 - 资源不存在
   */
  static notFound(message: string = '资源不存在'): ApiResponse<null> {
    return this.error(404, message);
  }

  /**
   * 409 - 资源冲突
   */
  static conflict(message: string = '资源冲突'): ApiResponse<null> {
    return this.error(409, message);
  }

  /**
   * 422 - 数据验证失败
   */
  static validationError(message: string = '数据验证失败'): ApiResponse<null> {
    return this.error(422, message);
  }

  /**
   * 500 - 服务器内部错误
   */
  static internalError(message: string = '服务器内部错误'): ApiResponse<null> {
    return this.error(500, message);
  }

  /**
   * 502 - 网关错误
   */
  static badGateway(message: string = '网关错误'): ApiResponse<null> {
    return this.error(502, message);
  }

  /**
   * 503 - 服务不可用
   */
  static serviceUnavailable(message: string = '服务暂时不可用'): ApiResponse<null> {
    return this.error(503, message);
  }
}

/**
 * Express响应扩展
 * 为Express Response对象添加统一响应方法
 */
declare global {
  namespace Express {
    interface Response {
      success<T>(data: T, message?: string): Response;
      successWithPagination<T>(
        list: T[],
        total: number,
        page: number,
        pageSize: number,
        message?: string
      ): Response;
      error(code: number, message: string): Response;
      badRequest(message?: string): Response;
      unauthorized(message?: string): Response;
      forbidden(message?: string): Response;
      notFound(message?: string): Response;
      conflict(message?: string): Response;
      validationError(message?: string): Response;
      internalError(message?: string): Response;
    }
  }
}

/**
 * 响应中间件
 * 为Express Response对象添加统一响应方法
 */
export const responseMiddleware = (req: any, res: any, next: any) => {
  // 成功响应
  res.success = function<T>(data: T, message: string = 'success') {
    return this.json(ResponseUtil.success(data, message));
  };

  // 分页成功响应
  res.successWithPagination = function<T>(
    list: T[],
    total: number,
    page: number,
    pageSize: number,
    message: string = 'success'
  ) {
    return this.json(ResponseUtil.successWithPagination(list, total, page, pageSize, message));
  };

  // 错误响应
  res.error = function(code: number, message: string) {
    return this.status(code).json(ResponseUtil.error(code, message));
  };

  // 400 - 请求参数错误
  res.badRequest = function(message?: string) {
    const response = ResponseUtil.badRequest(message);
    return this.status(400).json(response);
  };

  // 401 - 未授权
  res.unauthorized = function(message?: string) {
    const response = ResponseUtil.unauthorized(message);
    return this.status(401).json(response);
  };

  // 403 - 禁止访问
  res.forbidden = function(message?: string) {
    const response = ResponseUtil.forbidden(message);
    return this.status(403).json(response);
  };

  // 404 - 资源不存在
  res.notFound = function(message?: string) {
    const response = ResponseUtil.notFound(message);
    return this.status(404).json(response);
  };

  // 409 - 资源冲突
  res.conflict = function(message?: string) {
    const response = ResponseUtil.conflict(message);
    return this.status(409).json(response);
  };

  // 422 - 数据验证失败
  res.validationError = function(message?: string) {
    const response = ResponseUtil.validationError(message);
    return this.status(422).json(response);
  };

  // 500 - 服务器内部错误
  res.internalError = function(message?: string) {
    const response = ResponseUtil.internalError(message);
    return this.status(500).json(response);
  };

  next();
};

export default ResponseUtil;
