#!/usr/bin/env node

// 简化的记账小程序后端服务启动脚本
// 避免TypeScript编译和复杂依赖问题

const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 加载环境变量
require('dotenv').config();

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 创建必要的目录
const dirs = ['data', 'uploads', 'logs'];
dirs.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ 创建目录: ${dir}`);
  }
});

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 简单的内存数据库（开发环境使用）
let database = {
  users: [],
  categories: [
    {
      id: 'cat_1',
      user_id: null,
      name: '餐饮',
      type: 'expense',
      icon: '🍔',
      color: '#FF6B6B',
      sort_order: 1,
      created_at: new Date().toISOString()
    },
    {
      id: 'cat_2',
      user_id: null,
      name: '交通',
      type: 'expense',
      icon: '🚗',
      color: '#4ECDC4',
      sort_order: 2,
      created_at: new Date().toISOString()
    },
    {
      id: 'cat_3',
      user_id: null,
      name: '购物',
      type: 'expense',
      icon: '🛍️',
      color: '#45B7D1',
      sort_order: 3,
      created_at: new Date().toISOString()
    },
    {
      id: 'cat_4',
      user_id: null,
      name: '娱乐',
      type: 'expense',
      icon: '🎮',
      color: '#9B59B6',
      sort_order: 4,
      created_at: new Date().toISOString()
    },
    {
      id: 'cat_5',
      user_id: null,
      name: '医疗',
      type: 'expense',
      icon: '🏥',
      color: '#E74C3C',
      sort_order: 5,
      created_at: new Date().toISOString()
    },
    {
      id: 'cat_6',
      user_id: null,
      name: '工资',
      type: 'income',
      icon: '💼',
      color: '#2ECC71',
      sort_order: 1,
      created_at: new Date().toISOString()
    },
    {
      id: 'cat_7',
      user_id: null,
      name: '奖金',
      type: 'income',
      icon: '🏆',
      color: '#F39C12',
      sort_order: 2,
      created_at: new Date().toISOString()
    },
    {
      id: 'cat_8',
      user_id: null,
      name: '投资收益',
      type: 'income',
      icon: '📈',
      color: '#27AE60',
      sort_order: 3,
      created_at: new Date().toISOString()
    }
  ],
  transactions: []
};

// 数据持久化
const dbFile = path.join(__dirname, 'data', 'database.json');

// 加载数据
function loadDatabase() {
  try {
    if (fs.existsSync(dbFile)) {
      const data = fs.readFileSync(dbFile, 'utf8');
      const loadedData = JSON.parse(data);
      database = { ...database, ...loadedData };
      console.log('✅ 数据库加载成功');
    } else {
      console.log('📝 使用默认数据库');
    }
  } catch (error) {
    console.log('⚠️ 数据库加载失败，使用默认数据:', error.message);
  }
}

// 保存数据
function saveDatabase() {
  try {
    fs.writeFileSync(dbFile, JSON.stringify(database, null, 2));
  } catch (error) {
    console.error('❌ 数据库保存失败:', error);
  }
}

// 生成ID
function generateId(prefix = 'id') {
  return prefix + '_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 生成JWT令牌（简化版）
function generateToken(payload) {
  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64');
  const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64');
  const signature = crypto.createHmac('sha256', process.env.JWT_SECRET || 'simple-secret').update(`${header}.${payloadStr}`).digest('base64');
  return `${header}.${payloadStr}.${signature}`;
}

// 验证JWT令牌（简化版）
function verifyToken(token) {
  try {
    const [header, payload, signature] = token.split('.');
    const expectedSignature = crypto.createHmac('sha256', process.env.JWT_SECRET || 'simple-secret').update(`${header}.${payload}`).digest('base64');
    if (signature === expectedSignature) {
      return JSON.parse(Buffer.from(payload, 'base64').toString());
    }
  } catch (error) {
    return null;
  }
  return null;
}

// 认证中间件
function authMiddleware(req, res, next) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: { message: '缺少访问令牌', code: 401 }
    });
  }
  
  const token = authHeader.substring(7);
  const payload = verifyToken(token);
  if (!payload) {
    return res.status(401).json({
      success: false,
      error: { message: '无效的访问令牌', code: 401 }
    });
  }
  
  req.user = payload;
  next();
}

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    message: '记账小程序后端服务运行正常'
  });
});

// API 根路径
app.get('/api', (req, res) => {
  res.json({
    message: '记账小程序 API 服务',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      auth: '/api/auth/*',
      categories: '/api/categories',
      transactions: '/api/transactions',
      statistics: '/api/statistics/*'
    }
  });
});

// 认证接口
app.post('/api/auth/login', (req, res) => {
  const { code, userInfo } = req.body;
  
  if (!code) {
    return res.status(400).json({
      success: false,
      error: { message: '缺少微信授权码', code: 400 }
    });
  }
  
  // 查找或创建用户
  let user = database.users.find(u => u.openid === `mock_${code}`);
  if (!user) {
    user = {
      id: generateId('user'),
      openid: `mock_${code}`,
      nickname: userInfo?.nickName || '测试用户',
      avatar_url: userInfo?.avatarUrl || '',
      created_at: new Date().toISOString()
    };
    database.users.push(user);
    saveDatabase();
  }
  
  const token = generateToken({
    userId: user.id,
    openid: user.openid
  });
  
  res.json({
    success: true,
    data: {
      token,
      refreshToken: generateToken({ userId: user.id, type: 'refresh' }),
      user
    }
  });
});

// 分类接口
app.get('/api/categories', authMiddleware, (req, res) => {
  const { type } = req.query;
  let categories = database.categories.filter(c => 
    (c.user_id === null || c.user_id === req.user.userId) && !c.deleted_at
  );
  
  if (type) {
    categories = categories.filter(c => c.type === type);
  }
  
  res.json({
    success: true,
    data: {
      list: categories.sort((a, b) => a.sort_order - b.sort_order),
      total: categories.length,
      page: 1,
      pageSize: 50,
      totalPages: 1
    }
  });
});

app.post('/api/categories', authMiddleware, (req, res) => {
  const { name, type, icon, color, budget } = req.body;
  
  if (!name || !type) {
    return res.status(400).json({
      success: false,
      error: { message: '缺少必要参数', code: 400 }
    });
  }
  
  const category = {
    id: generateId('cat'),
    user_id: req.user.userId,
    name,
    type,
    icon: icon || '💰',
    color: color || '#1890ff',
    budget: budget || null,
    sort_order: database.categories.filter(c => c.type === type).length + 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  database.categories.push(category);
  saveDatabase();
  
  res.status(201).json({
    success: true,
    data: category
  });
});

// 交易记录接口
app.get('/api/transactions', authMiddleware, (req, res) => {
  const { page = 1, pageSize = 20, type } = req.query;
  let transactions = database.transactions.filter(t => 
    t.user_id === req.user.userId && !t.deleted_at
  );
  
  if (type) {
    transactions = transactions.filter(t => t.type === type);
  }
  
  // 添加分类信息
  transactions = transactions.map(t => {
    const category = database.categories.find(c => c.id === t.category_id);
    return {
      ...t,
      category_name: category?.name,
      category_icon: category?.icon,
      category_color: category?.color
    };
  });
  
  // 排序
  transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  
  // 分页
  const start = (page - 1) * pageSize;
  const end = start + parseInt(pageSize);
  const paginatedTransactions = transactions.slice(start, end);
  
  res.json({
    success: true,
    data: {
      list: paginatedTransactions,
      total: transactions.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      totalPages: Math.ceil(transactions.length / pageSize)
    }
  });
});

app.post('/api/transactions', authMiddleware, (req, res) => {
  const { type, amount, category_id, description, date } = req.body;
  
  if (!type || !amount || !category_id || !date) {
    return res.status(400).json({
      success: false,
      error: { message: '缺少必要参数', code: 400 }
    });
  }
  
  const transaction = {
    id: generateId('txn'),
    user_id: req.user.userId,
    type,
    amount: parseFloat(amount),
    category_id,
    description: description || '',
    date,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  database.transactions.push(transaction);
  saveDatabase();
  
  // 返回带分类信息的交易记录
  const category = database.categories.find(c => c.id === category_id);
  const result = {
    ...transaction,
    category_name: category?.name,
    category_icon: category?.icon,
    category_color: category?.color
  };
  
  res.status(201).json({
    success: true,
    data: result
  });
});

// 统计接口
app.get('/api/statistics/overview', authMiddleware, (req, res) => {
  const transactions = database.transactions.filter(t => 
    t.user_id === req.user.userId && !t.deleted_at
  );
  
  const totalIncome = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);
    
  const totalExpense = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);
  
  res.json({
    success: true,
    data: {
      totalIncome,
      totalExpense,
      balance: totalIncome - totalExpense,
      transactionCount: transactions.length
    }
  });
});

// 错误处理
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    error: { message: '服务器内部错误', code: 500 }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: { message: `路由 ${req.originalUrl} 不存在`, code: 404 }
  });
});

// 启动服务器
loadDatabase();

app.listen(PORT, () => {
  console.log('🚀 记账小程序后端服务启动成功');
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
  console.log(`📱 API文档: http://localhost:${PORT}/api`);
  console.log('');
  console.log('可用的API端点:');
  console.log('  POST /api/auth/login - 用户登录');
  console.log('  GET  /api/categories - 获取分类列表');
  console.log('  POST /api/categories - 创建分类');
  console.log('  GET  /api/transactions - 获取交易记录');
  console.log('  POST /api/transactions - 创建交易记录');
  console.log('  GET  /api/statistics/overview - 获取统计概览');
  console.log('');
  console.log('💡 提示: 数据保存在 data/database.json 文件中');
  console.log('🔧 如果需要重置数据，删除 data/database.json 文件即可');
});

module.exports = app;
