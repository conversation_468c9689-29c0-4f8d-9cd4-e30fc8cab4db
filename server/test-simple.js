/**
 * 简单的接口测试脚本
 * 绕过ICU问题，直接测试接口功能
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

// 创建简单的测试应用
const app = express();

// 基础中间件
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 响应中间件
app.use((req, res, next) => {
  res.success = function(data, message = 'success') {
    return res.json({
      code: 200,
      message,
      data
    });
  };
  
  res.error = function(message, code = 500) {
    return res.status(code).json({
      code,
      message,
      data: null
    });
  };
  
  res.badRequest = function(message = '请求参数错误') {
    return res.error(message, 400);
  };
  
  res.unauthorized = function(message = '未授权') {
    return res.error(message, 401);
  };
  
  res.forbidden = function(message = '禁止访问') {
    return res.error(message, 403);
  };
  
  res.notFound = function(message = '资源不存在') {
    return res.error(message, 404);
  };
  
  next();
});

// 模拟认证中间件
const mockAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.unauthorized('未提供认证令牌');
  }
  
  // 模拟用户信息
  req.user = {
    id: 'test-user-123',
    openid: 'test-openid-123'
  };
  
  next();
};

// 健康检查
app.get('/health', (req, res) => {
  res.success({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  }, '服务运行正常');
});

app.get('/api/health', (req, res) => {
  res.success({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    database: 'connected'
  }, 'API服务运行正常');
});

// 认证接口
app.post('/api/auth/login', (req, res) => {
  const { code, userInfo } = req.body;
  
  if (!code) {
    return res.badRequest('缺少微信授权码');
  }
  
  // 模拟登录成功
  res.success({
    token: 'mock-access-token-123',
    refreshToken: 'mock-refresh-token-123',
    user: {
      id: 'test-user-123',
      openid: 'test-openid-123',
      nickname: userInfo?.nickName || '测试用户',
      avatar_url: userInfo?.avatarUrl || ''
    }
  }, '登录成功');
});

// 分类接口
app.get('/api/categories', mockAuth, (req, res) => {
  const { type } = req.query;
  
  // 模拟分类数据
  const allCategories = [
    { id: 'cat_1', name: '餐饮', type: 'expense', icon: '🍽️', color: '#FF6B6B' },
    { id: 'cat_2', name: '交通', type: 'expense', icon: '🚗', color: '#4ECDC4' },
    { id: 'cat_3', name: '购物', type: 'expense', icon: '🛒', color: '#45B7D1' },
    { id: 'cat_4', name: '工资', type: 'income', icon: '💰', color: '#2ECC71' },
    { id: 'cat_5', name: '奖金', type: 'income', icon: '🎁', color: '#3498DB' }
  ];
  
  const filteredCategories = type 
    ? allCategories.filter(cat => cat.type === type)
    : allCategories;
  
  res.success(filteredCategories);
});

app.post('/api/categories', mockAuth, (req, res) => {
  const { name, type, icon, color } = req.body;
  
  if (!name || !type) {
    return res.badRequest('缺少必填字段');
  }
  
  // 模拟创建分类
  const newCategory = {
    id: `cat_${Date.now()}`,
    name,
    type,
    icon: icon || '📦',
    color: color || '#BDC3C7',
    user_id: req.user.id,
    created_at: new Date().toISOString()
  };
  
  res.status(201).success(newCategory, '分类创建成功');
});

// 交易记录接口
app.get('/api/transactions', mockAuth, (req, res) => {
  const { 
    page = 1, 
    pageSize = 20, 
    type, 
    categoryId, 
    startDate, 
    endDate 
  } = req.query;
  
  // 模拟交易记录数据
  const allTransactions = [
    {
      id: 'trans_1',
      category_id: 'cat_1',
      type: 'expense',
      amount: 45.5,
      description: '午餐',
      date: '2024-01-15',
      category: { name: '餐饮', icon: '🍽️' }
    },
    {
      id: 'trans_2',
      category_id: 'cat_2',
      type: 'expense',
      amount: 12,
      description: '地铁费',
      date: '2024-01-16',
      category: { name: '交通', icon: '🚗' }
    },
    {
      id: 'trans_3',
      category_id: 'cat_4',
      type: 'income',
      amount: 8000,
      description: '工资',
      date: '2024-01-01',
      category: { name: '工资', icon: '💰' }
    }
  ];
  
  // 简单筛选
  let filteredTransactions = allTransactions;
  if (type) {
    filteredTransactions = filteredTransactions.filter(t => t.type === type);
  }
  if (categoryId) {
    filteredTransactions = filteredTransactions.filter(t => t.category_id === categoryId);
  }
  
  const total = filteredTransactions.length;
  const pageNum = parseInt(page);
  const pageSizeNum = parseInt(pageSize);
  const start = (pageNum - 1) * pageSizeNum;
  const end = start + pageSizeNum;
  const transactions = filteredTransactions.slice(start, end);
  
  res.success({
    transactions,
    pagination: {
      total,
      page: pageNum,
      pageSize: pageSizeNum,
      totalPages: Math.ceil(total / pageSizeNum)
    }
  });
});

app.post('/api/transactions', mockAuth, (req, res) => {
  const { category_id, type, amount, description, date } = req.body;
  
  if (!category_id || !type || !amount || !date) {
    return res.badRequest('缺少必填字段');
  }
  
  if (amount <= 0) {
    return res.badRequest('金额必须大于0');
  }
  
  // 模拟创建交易记录
  const newTransaction = {
    id: `trans_${Date.now()}`,
    category_id,
    type,
    amount: parseFloat(amount),
    description: description || '',
    date,
    user_id: req.user.id,
    created_at: new Date().toISOString()
  };
  
  res.status(201).success(newTransaction, '交易记录创建成功');
});

// 统计接口
app.get('/api/statistics/overview', mockAuth, (req, res) => {
  // 模拟统计数据
  res.success({
    totalIncome: 8000,
    totalExpense: 157.5,
    balance: 7842.5,
    transactionCount: 3,
    categoryCount: 5
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.error('服务器内部错误', 500);
});

// 404处理
app.use((req, res) => {
  res.notFound('接口不存在');
});

// 启动服务器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 测试服务器启动成功！`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📋 API健康检查: http://localhost:${PORT}/api/health`);
  console.log('');
  console.log('📝 可用接口:');
  console.log('  POST /api/auth/login - 登录');
  console.log('  GET  /api/categories - 获取分类列表');
  console.log('  POST /api/categories - 创建分类');
  console.log('  GET  /api/transactions - 获取交易记录');
  console.log('  POST /api/transactions - 创建交易记录');
  console.log('  GET  /api/statistics/overview - 统计概览');
  console.log('');
  console.log('🧪 测试命令:');
  console.log('  curl http://localhost:3000/health');
  console.log('  curl -X POST http://localhost:3000/api/auth/login -H "Content-Type: application/json" -d \'{"code":"test"}\'');
});
